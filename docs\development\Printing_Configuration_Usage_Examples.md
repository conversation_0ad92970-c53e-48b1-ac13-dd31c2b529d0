# 打印配置管理使用示例

## 基础使用

### 1. 加载和初始化配置

```javascript
const configManager = require('./config-manager.js');

// 加载配置
configManager.loadConfig();

// 获取完整的打印配置
const printingConfig = configManager.getPrintingConfig();
console.log('打印配置已加载:', printingConfig ? '成功' : '失败');
```

### 2. 获取打印参数

```javascript
// 获取速度等级配置
const fastSpeed = configManager.getPrintSpeedLevel('fast');
console.log('快速模式延迟:', fastSpeed.step_delay_us + 'μs');

// 获取校准参数
const calibration = configManager.getPrintPrecisionSettings('calibration');
console.log('X轴每圈毫米数:', calibration.mm_per_revolution_x);

// 获取盲文尺寸参数
const brailleDims = configManager.getPrintPrecisionSettings('braille_dimensions');
console.log('字符间距:', brailleDims.char_spacing + 'mm');
```

### 3. 应用打印模式

```javascript
// 应用高质量打印模式
const appliedConfig = configManager.applyPrintModePreset('high_quality');
console.log('已应用打印模式:', appliedConfig.mode);
console.log('速度等级:', appliedConfig.speed_level);
console.log('质量因子:', appliedConfig.quality_factor);

// 根据应用的配置调整打印参数
const speedConfig = configManager.getPrintSpeedLevel(appliedConfig.speed_level);
const stepDelay = speedConfig.step_delay_us;
```

## 动态配置管理

### 1. 监听配置变更

```javascript
// 添加配置变更监听器
configManager.addListener((event, data) => {
    switch (event) {
        case 'configChanged':
            if (data.path.startsWith('printing.')) {
                console.log('打印配置已更新:', data.path);
                // 重新应用配置到打印系统
                updatePrintingSystem(data.path, data.newValue);
            }
            break;
            
        case 'printModeApplied':
            console.log('打印模式已切换:', data.mode);
            // 更新UI显示
            updatePrintModeUI(data);
            break;
            
        case 'configReloaded':
            console.log('配置已重新加载');
            // 重新初始化打印系统
            reinitializePrintingSystem();
            break;
    }
});

function updatePrintingSystem(path, value) {
    // 根据配置路径更新相应的打印参数
    if (path.includes('speed_levels')) {
        // 更新速度参数
        updatePrintSpeed(value);
    } else if (path.includes('precision_settings')) {
        // 更新精度参数
        updatePrintPrecision(value);
    }
}
```

### 2. 动态调整打印参数

```javascript
// 创建自定义速度等级
function createCustomSpeedLevel(name, stepDelay, description) {
    try {
        configManager.setPrintSpeedLevel(name, {
            step_delay_us: stepDelay,
            description: description,
            max_complexity: 0.5
        });
        console.log(`自定义速度等级 ${name} 创建成功`);
        return true;
    } catch (error) {
        console.error('创建速度等级失败:', error.message);
        return false;
    }
}

// 调整校准参数
function updateCalibrationParams(stepsPerRev, mmPerRevX, mmPerRevY) {
    const newCalibration = {
        steps_per_revolution: stepsPerRev,
        mm_per_revolution_x: mmPerRevX,
        mm_per_revolution_y: mmPerRevY,
        mm_per_revolution_z: 8.0
    };
    
    configManager.setPrintPrecisionSettings('calibration', newCalibration);
    console.log('校准参数已更新');
}

// 调整任务管理参数
function updateTaskManagement(maxQueue, timeout) {
    const taskConfig = configManager.getPrintTaskManagement();
    taskConfig.max_queue_size = maxQueue;
    taskConfig.timeout_ms = timeout;
    
    configManager.setPrintTaskManagement(taskConfig);
    console.log('任务管理配置已更新');
}
```

## 实际应用场景

### 1. 打印质量优化

```javascript
// 根据打印内容复杂度选择合适的模式
function selectOptimalPrintMode(contentComplexity) {
    let mode;
    
    if (contentComplexity < 0.3) {
        mode = 'draft';  // 简单内容使用草稿模式
    } else if (contentComplexity < 0.7) {
        mode = 'normal'; // 中等复杂度使用标准模式
    } else {
        mode = 'high_quality'; // 复杂内容使用高质量模式
    }
    
    const appliedConfig = configManager.applyPrintModePreset(mode);
    console.log(`根据复杂度 ${contentComplexity} 选择模式: ${mode}`);
    
    return appliedConfig;
}

// 动态调整打印速度
function adjustPrintSpeed(urgency) {
    const currentMode = configManager.get('printing.current_mode') || 'normal';
    const modeConfig = configManager.getPrintMode(currentMode);
    
    let speedLevel = modeConfig.speed_level;
    
    if (urgency === 'high') {
        speedLevel = 'fast';
    } else if (urgency === 'low') {
        speedLevel = 'precise';
    }
    
    const speedConfig = configManager.getPrintSpeedLevel(speedLevel);
    console.log(`紧急程度 ${urgency}, 使用速度等级: ${speedLevel}`);
    
    return speedConfig.step_delay_us;
}
```

### 2. 错误恢复和配置回滚

```javascript
// 配置备份和恢复
class PrintConfigBackup {
    constructor() {
        this.backups = [];
    }
    
    // 创建配置备份
    createBackup(description) {
        const backup = {
            timestamp: new Date().toISOString(),
            description: description,
            config: JSON.parse(JSON.stringify(configManager.getPrintingConfig()))
        };
        
        this.backups.push(backup);
        console.log('配置备份已创建:', description);
        
        // 保持最多10个备份
        if (this.backups.length > 10) {
            this.backups.shift();
        }
    }
    
    // 恢复配置
    restoreBackup(index = 0) {
        if (index >= this.backups.length) {
            throw new Error('备份索引无效');
        }
        
        const backup = this.backups[this.backups.length - 1 - index];
        
        // 恢复各个配置部分
        Object.keys(backup.config).forEach(key => {
            configManager.set(`printing.${key}`, backup.config[key]);
        });
        
        console.log('配置已恢复到:', backup.description);
    }
    
    // 列出所有备份
    listBackups() {
        return this.backups.map((backup, index) => ({
            index: this.backups.length - 1 - index,
            timestamp: backup.timestamp,
            description: backup.description
        }));
    }
}

// 使用示例
const configBackup = new PrintConfigBackup();

// 在重要操作前创建备份
configBackup.createBackup('优化前的配置');

// 尝试新配置
try {
    configManager.setPrintSpeedLevel('experimental', {
        step_delay_us: 100,
        description: '实验性高速模式',
        max_complexity: 0.2
    });
    
    // 测试新配置...
    
} catch (error) {
    console.error('新配置失败，恢复备份:', error.message);
    configBackup.restoreBackup(0);
}
```

### 3. 性能监控集成

```javascript
// 配置性能监控
function setupPerformanceMonitoring() {
    const monitoring = configManager.get('printing.monitoring');
    
    if (monitoring.enable_performance_tracking) {
        // 启动性能监控
        setInterval(() => {
            const currentConfig = {
                speed_level: getCurrentSpeedLevel(),
                queue_size: getCurrentQueueSize(),
                error_rate: getErrorRate()
            };
            
            // 根据性能数据自动调整配置
            autoTuneConfiguration(currentConfig);
            
        }, monitoring.status_update_interval);
    }
}

function autoTuneConfiguration(performanceData) {
    // 如果错误率过高，降低打印速度
    if (performanceData.error_rate > 0.1) {
        const currentSpeed = configManager.get('printing.current_speed_level') || 'standard';
        
        if (currentSpeed === 'fast') {
            console.log('错误率过高，切换到标准速度');
            configManager.applyPrintModePreset('normal');
        } else if (currentSpeed === 'standard') {
            console.log('错误率过高，切换到精确模式');
            configManager.applyPrintModePreset('high_quality');
        }
    }
    
    // 如果队列积压严重，启用批处理
    if (performanceData.queue_size > 5) {
        const taskConfig = configManager.getPrintTaskManagement();
        if (!taskConfig.enable_batch_processing) {
            taskConfig.enable_batch_processing = true;
            configManager.setPrintTaskManagement(taskConfig);
            console.log('队列积压，启用批处理模式');
        }
    }
}
```

## 最佳实践总结

### 1. 配置管理原则
- 始终验证配置参数的有效性
- 在重要操作前创建配置备份
- 使用事件监听器响应配置变更
- 定期验证配置完整性

### 2. 性能优化
- 缓存常用配置参数
- 避免频繁的配置读写操作
- 使用批量配置更新
- 监控配置变更的性能影响

### 3. 错误处理
- 提供配置回滚机制
- 记录所有配置变更日志
- 实现配置验证和自动修复
- 设置合理的默认值和边界检查

### 4. 维护性
- 保持配置结构的一致性
- 提供清晰的配置文档
- 使用语义化的配置名称
- 定期清理无用的配置项
