[{"id": "err_1751614045606_fbr8g8", "code": 1001, "type": "步进电机故障", "level": "error", "message": "步进电机故障 - 测试模拟", "timestamp": "2025-07-04T07:27:25.606Z", "context": {"taskId": "test_task_001", "clientId": "voice_client_1", "component": "stepper_motor", "operation": "print_line", "timestamp": "2025-07-04T07:27:25.606Z"}, "recoveryStrategy": {"strategy": "reset", "maxAttempts": 2}, "retryCount": 0, "resolved": false, "loggedAt": "2025-07-04T07:27:25.606Z"}, {"id": "err_1751614047610_j6npv9", "code": 2001, "type": "DTU连接丢失", "level": "warning", "message": "DTU连接丢失 - 测试模拟", "timestamp": "2025-07-04T07:27:27.610Z", "context": {"taskId": "test_task_002", "clientId": "voice_client_1", "component": "dtu", "operation": "send_command", "timestamp": "2025-07-04T07:27:27.610Z"}, "recoveryStrategy": {"strategy": "retry", "maxAttempts": 5}, "retryCount": 0, "resolved": false, "loggedAt": "2025-07-04T07:27:27.610Z"}, {"id": "err_1751614149712_bipdr0", "code": 3001, "type": "无效打印数据", "level": "error", "message": "无效的打印数据 - 测试模拟", "timestamp": "2025-07-04T07:29:09.712Z", "context": {"taskId": "test_task_app_001", "clientId": "voice_client_1", "component": "print_processor", "operation": "validate_data", "timestamp": "2025-07-04T07:29:09.711Z"}, "recoveryStrategy": {"strategy": "ignore", "maxAttempts": 0}, "retryCount": 0, "resolved": false, "loggedAt": "2025-07-04T07:29:09.712Z"}, {"id": "err_1751614211975_0jdnul", "code": 3001, "type": "无效打印数据", "level": "error", "message": "无效的打印数据 - 测试模拟", "timestamp": "2025-07-04T07:30:11.975Z", "context": {"taskId": "test_task_app_001", "clientId": "voice_client_2", "component": "print_processor", "operation": "validate_data", "timestamp": "2025-07-04T07:30:11.975Z"}, "recoveryStrategy": {"strategy": "ignore", "maxAttempts": 0}, "retryCount": 0, "resolved": false, "loggedAt": "2025-07-04T07:30:11.975Z"}]