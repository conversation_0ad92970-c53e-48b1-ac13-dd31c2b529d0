@echo off
title WebSocket+DTU System Startup
color 0A

echo ========================================
echo   WebSocket+DTU Braille Printer System
echo   Simple Startup Script
echo ========================================
echo.

:: Check Node.js
echo [1/4] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo OK: Node.js found
for /f "tokens=*" %%i in ('node --version') do echo Version: %%i

:: Check project files
echo.
echo [2/4] Checking project files...
if not exist "code\voice-backend\server.js" (
    echo ERROR: Backend server file not found
    echo Please run this script from project root directory
    pause
    exit /b 1
)

if not exist "code\voice-frontend\server.js" (
    echo ERROR: Frontend server file not found
    echo Please check voice-frontend directory
    pause
    exit /b 1
)

echo OK: Project files found

:: Install dependencies
echo.
echo [3/4] Installing dependencies...
cd code\voice-backend
echo Installing backend dependencies...
call npm install --silent
if errorlevel 1 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)

cd ..\voice-frontend
echo Installing frontend dependencies...
call npm install --silent
if errorlevel 1 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)

cd ..\..
echo OK: Dependencies installed

:: Start services
echo.
echo [4/4] Starting services...
echo Starting WebSocket backend server...
start "WebSocket Backend" cmd /k "cd code\voice-backend && node server.js"

timeout /t 3 /nobreak >nul

echo Starting frontend web server...
start "Frontend Server" cmd /k "cd code\voice-frontend && node server.js"

timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo   System Started Successfully!
echo ========================================
echo.
echo Access URLs:
echo   Frontend:  http://localhost:4000
echo   WebSocket: ws://localhost:8081/voice-ws
echo.
echo Opening frontend in browser...
start http://localhost:4000

echo.
echo Press any key to show control menu...
pause >nul

:menu
cls
echo ========================================
echo   WebSocket+DTU System Control Menu
echo ========================================
echo.
echo   R - Restart system
echo   S - Show status
echo   T - Test connections
echo   H - Help
echo   Q - Quit system
echo.
set /p choice="Select option (R/S/T/H/Q): "

if /i "%choice%"=="R" goto restart
if /i "%choice%"=="S" goto status
if /i "%choice%"=="T" goto test
if /i "%choice%"=="H" goto help
if /i "%choice%"=="Q" goto quit
goto menu

:restart
echo Restarting system...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul
goto start_services

:status
echo.
echo System Status:
netstat -an | findstr :4000 >nul && echo   Frontend Server: RUNNING || echo   Frontend Server: STOPPED
netstat -an | findstr :8081 >nul && echo   WebSocket Server: RUNNING || echo   WebSocket Server: STOPPED
echo.
pause
goto menu

:test
echo.
echo Testing connections...
ping -n 1 localhost >nul && echo   Localhost: OK || echo   Localhost: FAILED
curl -s http://localhost:4000 >nul && echo   Frontend: OK || echo   Frontend: FAILED
echo.
pause
goto menu

:help
echo.
echo Help Information:
echo   - Frontend URL: http://localhost:4000
echo   - WebSocket URL: ws://localhost:8081/voice-ws
echo   - Check firewall if connection fails
echo   - Ensure ports 4000 and 8081 are available
echo.
pause
goto menu

:quit
echo Stopping system...
taskkill /f /im node.exe >nul 2>&1
echo System stopped.
pause
exit

:start_services
echo Starting WebSocket backend server...
start "WebSocket Backend" cmd /k "cd code\voice-backend && node server.js"
timeout /t 3 /nobreak >nul
echo Starting frontend web server...
start "Frontend Server" cmd /k "cd code\voice-frontend && node server.js"
timeout /t 3 /nobreak >nul
echo System restarted.
pause
goto menu
