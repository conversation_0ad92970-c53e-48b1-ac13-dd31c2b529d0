# 盲文打印系统配置管理文档

## 概述

本文档详细说明了WebSocket+DTU智能语音识别盲文打印系统中的打印控制配置管理功能。该系统基于现有的config-manager.js架构，扩展了专门的打印参数管理能力。

## 配置架构

### 1. 打印配置节点结构

```javascript
printing: {
    speed_levels: {        // 打印速度等级配置
        fast: { ... },
        standard: { ... },
        precise: { ... }
    },
    precision_settings: {  // 打印精度参数配置
        calibration: { ... },
        braille_dimensions: { ... },
        compensation: { ... }
    },
    print_modes: {         // 打印模式预设
        draft: { ... },
        normal: { ... },
        high_quality: { ... }
    },
    task_management: { ... },  // 任务管理配置
    monitoring: { ... },       // 状态监控配置
    error_handling: { ... }    // 错误处理配置
}
```

### 2. 速度等级配置

支持三种预定义的打印速度等级：

- **fast**: 快速打印模式，适用于草稿和预览
- **standard**: 标准打印模式，平衡速度和质量
- **precise**: 精确打印模式，最高质量输出

每个速度等级包含：
- `step_delay_us`: 步进电机延迟时间（微秒）
- `description`: 模式描述
- `max_complexity`: 最大复杂度阈值

### 3. 精度参数配置

#### 校准参数 (calibration)
- `steps_per_revolution`: 每圈步数
- `mm_per_revolution_x/y/z`: X/Y/Z轴每圈毫米数

#### 盲文尺寸参数 (braille_dimensions)
- `dot_pitch_x/y`: 盲文点间距
- `char_spacing`: 字符间距
- `line_spacing`: 行间距
- `z_retract_height`: Z轴抬起高度
- `z_punch_depth`: Z轴打孔深度

#### 补偿参数 (compensation)
- `enable_backlash_compensation`: 启用反向间隙补偿
- `backlash_x/y`: X/Y轴反向间隙值
- `enable_thermal_compensation`: 启用热补偿
- `thermal_coefficient`: 热膨胀系数

### 4. 打印模式预设

提供三种预定义的打印模式：

- **draft**: 草稿模式 - 快速打印，用于内容预览
- **normal**: 标准模式 - 日常使用的标准打印质量
- **high_quality**: 高质量模式 - 最高质量打印，用于正式文档

## API接口

### ConfigManager扩展方法

#### 打印模式管理
```javascript
// 获取打印模式配置
const mode = configManager.getPrintMode('normal');

// 设置打印模式配置
configManager.setPrintMode('custom', {
    name: '自定义模式',
    speed_level: 'standard',
    quality_factor: 0.9,
    enable_compensation: true
});

// 应用打印模式预设
const appliedConfig = configManager.applyPrintModePreset('high_quality');
```

#### 速度等级管理
```javascript
// 获取速度等级配置
const speedConfig = configManager.getPrintSpeedLevel('fast');

// 设置速度等级配置
configManager.setPrintSpeedLevel('custom_fast', {
    step_delay_us: 300,
    description: '自定义快速模式',
    max_complexity: 0.4
});
```

#### 精度参数管理
```javascript
// 获取校准参数
const calibration = configManager.getPrintPrecisionSettings('calibration');

// 设置盲文尺寸参数
configManager.setPrintPrecisionSettings('braille_dimensions', {
    dot_pitch_x: 2.5,
    dot_pitch_y: 2.5,
    char_spacing: 6.0,
    line_spacing: 10.0
});
```

#### 任务管理配置
```javascript
// 获取任务管理配置
const taskConfig = configManager.getPrintTaskManagement();

// 设置任务管理配置
configManager.setPrintTaskManagement({
    max_queue_size: 15,
    default_priority: 5,
    timeout_ms: 300000,
    enable_batch_processing: true,
    batch_size: 5
});
```

## 环境变量支持

系统支持通过环境变量覆盖默认配置：

### 速度配置
- `PRINT_SPEED_FAST`: 快速模式延迟时间
- `PRINT_SPEED_STANDARD`: 标准模式延迟时间
- `PRINT_SPEED_PRECISE`: 精确模式延迟时间

### 校准参数
- `PRINT_STEPS_PER_REV`: 每圈步数
- `PRINT_MM_PER_REV_X/Y/Z`: X/Y/Z轴每圈毫米数

### 盲文尺寸
- `PRINT_DOT_PITCH_X/Y`: 盲文点间距
- `PRINT_CHAR_SPACING`: 字符间距
- `PRINT_LINE_SPACING`: 行间距

### 任务管理
- `PRINT_MAX_QUEUE`: 最大队列大小
- `PRINT_DEFAULT_PRIORITY`: 默认优先级
- `PRINT_TIMEOUT`: 超时时间

## 配置验证

系统提供完整的配置验证机制：

### 自动验证
- 启动时自动验证所有打印配置
- 参数范围检查和合理性验证
- 依赖关系验证

### 手动验证
```javascript
// 验证完整配置
const result = configManager.validateConfig();

// 检查验证结果
if (!result.valid) {
    console.error('配置验证失败:', result.errors);
}
```

## 热更新支持

配置管理器支持打印参数的热更新：

### 事件监听
```javascript
// 监听配置变更事件
configManager.addListener((event, data) => {
    if (event === 'configChanged' && data.path.startsWith('printing.')) {
        console.log('打印配置已更新:', data);
        // 应用新配置到打印系统
    }
});

// 监听打印模式应用事件
configManager.addListener((event, data) => {
    if (event === 'printModeApplied') {
        console.log('打印模式已应用:', data);
    }
});
```

### 文件监控
```javascript
// 启用配置文件监控
configManager.enableFileWatching(['./config.js', './config.prod.js']);
```

## 最佳实践

### 1. 配置分层管理
- 基础配置在config.js中定义
- 环境特定配置通过环境变量覆盖
- 运行时配置通过API动态调整

### 2. 参数验证
- 始终验证配置参数的有效性
- 提供合理的默认值
- 记录配置变更日志

### 3. 性能优化
- 缓存常用配置参数
- 避免频繁的配置读取
- 使用事件机制通知配置变更

### 4. 错误处理
- 提供配置回滚机制
- 记录配置错误日志
- 实现配置恢复策略

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查参数类型和范围
   - 验证环境变量设置
   - 查看配置验证日志

2. **热更新不生效**
   - 确认文件监控已启用
   - 检查配置文件权限
   - 验证事件监听器设置

3. **性能问题**
   - 减少配置读取频率
   - 使用配置缓存
   - 优化事件处理逻辑

### 调试方法

```javascript
// 获取配置摘要
const summary = configManager.getConfigSummary();
console.log('配置摘要:', summary);

// 获取完整打印配置
const printConfig = configManager.getPrintingConfig();
console.log('打印配置:', printConfig);

// 验证特定配置路径
const value = configManager.get('printing.speed_levels.fast.step_delay_us');
console.log('快速模式延迟:', value);
```

## 版本历史

- **v2.0.0**: 初始版本，支持基础打印配置管理
- 支持速度等级、精度参数、打印模式配置
- 提供完整的API接口和热更新功能
- 集成配置验证和错误处理机制
