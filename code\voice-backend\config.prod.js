// CH32V307智能语音识别盲文打印系统 - 生产环境配置
// 生产环境特定配置覆盖
// 版本: 2.0.0

module.exports = {
    // 系统信息
    system: {
        environment: 'production',
        debug: false,
        logLevel: 'info'
    },

    // 服务器配置 - 生产环境
    server: {
        websocket: {
            host: '0.0.0.0',  // 监听所有接口
            port: 8081,
            maxConnections: 1000,  // 生产环境支持更多连接
            heartbeatInterval: 30000,  // 标准心跳间隔
            connectionTimeout: 120000  // 更长的连接超时
        },
        
        http: {
            enabled: false,  // 生产环境通常不需要HTTP服务器
            port: 8080
        }
    },

    // 讯飞语音识别 - 生产环境
    xunfei: {
        enabled: true,
        
        // 生产环境使用标准参数
        recognition: {
            timeout: 20000,  // 标准超时时间
            confidenceThreshold: 0.7  // 标准置信度阈值
        }
    },

    // DTU平台 - 生产环境
    dtu: {
        enabled: true,
        
        api: {
            timeout: 10000,  // 标准超时时间
            retryAttempts: 3,  // 标准重试次数
            retryDelay: 1000
        },

        management: {
            heartbeatInterval: 30000,  // 标准心跳间隔
            offlineTimeout: 300000  // 5分钟离线超时
        },

        forwarding: {
            confidenceThreshold: 0.7  // 标准置信度阈值
        }
    },

    // Air780e - 生产环境
    air780e: {
        device: {
            reconnectInterval: 5000,  // 标准重连间隔
            heartbeatInterval: 30000  // 标准心跳间隔
        }
    },

    // 生产环境特定功能
    production: {
        // 性能优化
        performance: {
            enableGzip: true,  // 启用压缩
            maxMemoryUsage: '512MB',  // 内存使用限制
            gcInterval: 300000,  // 垃圾回收间隔（5分钟）
            connectionPoolSize: 50  // 连接池大小
        },

        // 安全配置
        security: {
            enableRateLimit: true,  // 启用速率限制
            maxRequestsPerMinute: 60,  // 每分钟最大请求数
            enableCors: false,  // 生产环境禁用CORS
            requireAuth: false,  // 是否需要认证（根据需求调整）
            allowedOrigins: []  // 允许的来源域名
        },

        // 监控和日志
        monitoring: {
            enableMetrics: true,  // 启用性能指标收集
            metricsInterval: 60000,  // 指标收集间隔（1分钟）
            enableHealthCheck: true,  // 启用健康检查
            healthCheckPath: '/health',
            logRotation: {
                enabled: true,
                maxFileSize: '10MB',
                maxFiles: 5,
                datePattern: 'YYYY-MM-DD'
            }
        },

        // 错误处理
        errorHandling: {
            enableErrorReporting: true,  // 启用错误报告
            maxErrorsPerMinute: 10,  // 每分钟最大错误数
            enableAutoRestart: true,  // 启用自动重启
            restartThreshold: 50  // 错误阈值触发重启
        },

        // 备份和恢复
        backup: {
            enableConfigBackup: true,  // 启用配置备份
            backupInterval: 86400000,  // 备份间隔（24小时）
            maxBackupFiles: 7,  // 最大备份文件数
            backupPath: './backups'
        }
    }
};
