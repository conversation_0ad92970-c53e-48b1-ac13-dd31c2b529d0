# WebSocket+DTU智能语音识别盲文打印系统

基于CH32V307微控制器的智能语音识别盲文打印系统，采用WebSocket+银尔达DTU混合架构，实现语音识别、4G通信和盲文打印的完整解决方案。

## 🎯 系统特性

### 核心功能
- **🎤 语音识别**: 支持讯飞语音API，实时语音转文字
- **📡 4G通信**: Air780e 4G模块通过银尔达DTU平台通信
- **🖨️ 盲文打印**: CH32V307控制步进电机实现精确盲文打印
- **🌐 Web界面**: 现代化Web前端控制界面
- **⚡ 实时通信**: WebSocket实时双向通信

### 技术架构
- **前端**: HTML5 + JavaScript + WebSocket
- **后端**: Node.js + WebSocket + 银尔达DTU API
- **云平台**: 银尔达DTU平台 (dtu.yinled.com:8888)
- **4G通信**: Air780e模块 + 银尔达DTU平台
- **单片机**: CH32V307 + 步进电机控制
- **语音识别**: 讯飞语音API
- **音频采集**: INMP441麦克风模块

## 📁 项目结构

```
WebSocket+DTU智能语音识别盲文打印系统/
├── 📁 voice-backend/              # WebSocket后端服务
│   ├── server.js                  # 主服务器文件
│   ├── config.js                  # 统一配置管理
│   ├── dtu-integration.js         # DTU集成模块
│   ├── xunfei-voice-config.js     # 讯飞语音配置
│   └── package.json               # 后端依赖配置
├── 📁 voice-frontend/             # Web前端界面
│   ├── index.html                 # 主界面文件
│   ├── server.js                  # 前端服务器
│   └── package.json               # 前端依赖配置
├── 📁 01.0/                       # CH32V307固件项目
│   ├── User/main.c                # 主程序文件(WebSocket+DTU模式)
│   ├── air780e_websocket_dtu.lua  # Air780e混合通信脚本
│   ├── Driver/                    # 硬件驱动
│   ├── Core/                      # 系统核心
│   └── README.md                  # 固件说明文档
├── 📁 docs/                       # 项目文档
│   ├── development/               # 开发文档
│   ├── architecture/              # 架构文档
│   ├── analytics/                 # 分析报告
│   └── WebSocket_DTU系统使用指南.md # 系统使用指南
├── 📁 tools/                      # 工具和测试脚本
│   ├── cloudflared.exe            # 内网穿透工具
│   └── test-connection.js         # 连接测试工具
├── config.js                      # 🔧 统一配置管理
├── utils.js                       # 🔧 通用工具函数
└── README.md                      # 📖 项目说明文档
```

## 🚀 快速开始

### 1. 环境准备

**硬件要求:**
- CH32V307开发板
- Air780e 4G模块和SIM卡
- INMP441麦克风模块
- Keyes CNC Shield v3.0
- TMC2209步进电机驱动 × 3
- 17HS4401步进电机 × 3
- 定制CNC机械结构

**软件要求:**
- Node.js >= 16.0.0
- MounRiver Studio (CH32开发环境)
- 4G SIM卡 (支持数据流量)

### 2. 一键启动

```bash
# Windows环境 - 完整启动（推荐）
start_websocket_dtu_system.bat

# 快速启动（开发调试）
quick_start_websocket_dtu.bat

# 系统状态检查
check_websocket_dtu_status.bat

# 安全停止系统
stop_websocket_dtu_system.bat
```

### 3. 访问系统

- **主界面**: http://localhost:4000
- **WebSocket服务**: ws://localhost:8081/voice-ws
- **DTU控制台**: http://localhost:4000/dtu-console
- **系统状态**: 运行状态检查脚本

## ? ��ϸ����

### 4G��������

#### Air780e 4Gģ������

```lua
-- air780e-scripts/main.lua
local config = {
    device_id = "braille_printer_001",
    device_type = "ch32v307_braille_printer",
    
    -- ��ƽ̨����
    cloud = {
        custom = {
            enabled = true,
            server = "your-mqtt-server.com",  -- ����MQTT������
            port = 1883,
            username = "your_username",
            password = "your_password"
        }
    }
}
```

#### MQTT����ṹ

```
�豸�������� (�豸 -> �ƶ�):
- device/braille_printer_001/status       # �豸״̬
- device/braille_printer_001/heartbeat    # ����
- device/braille_printer_001/audio        # ��Ƶ����
- device/braille_printer_001/print_result # ��ӡ���
- device/braille_printer_001/error        # �����ϱ�

�豸�������� (�ƶ� -> �豸):
- device/braille_printer_001/voice_cmd    # ����ָ��
- device/braille_printer_001/cmd          # ����ָ��
- device/braille_printer_001/config       # ���ø���
```

### ����ʶ������

```javascript
// voice-backend/config.js
module.exports = {
    xunfei: {
        enabled: true,  // ����Ѷ������ʶ��
        appId: 'your_app_id',
        apiSecret: 'your_api_secret',
        apiKey: 'your_api_key'
    },
    
    iot: {
        enabled: true,  // ����IoT�豸����
        mqtt: {
            url: 'mqtt://your-mqtt-server:1883',
            username: 'your_username',
            password: 'your_password'
        }
    }
};
```

### CH32V307�̼�����

```c
// 01 - ����/Driver/plotter.h
#define MM_PER_REVOLUTION_X  10.0f   // X��У׼����
#define MM_PER_REVOLUTION_Y  16.60f  // Y��У׼����
#define BRAILLE_DOT_PITCH_Y  2.5f    // ä�ĵ���
#define BRAILLE_CHAR_SPACING 6.0f    // �ַ����
```

## ? ϵͳ�ܹ�

### ������������

```mermaid
graph TD
    A[? INMP441��˷�] -->|��Ƶ�ɼ�| B[CH32V307]
    B -->|��������| C[Air780e 4Gģ��]
    C -->|4G����| D[MQTT��ƽ̨]
    D -->|WebSocket| E[������˷���]
    E -->|Ѷ��API| F[����ʶ��]
    F -->|ʶ����| E
    E -->|MQTTָ��| D
    D -->|4G�·�| C
    C -->|����ָ��| B
    B -->|��������| G[?? ä�Ĵ�ӡ��]
    
    H[? Webǰ��] -->|WebSocket| E
    E -->|ʵʱ״̬| H
```

### ͨ��Э��

#### 1. WebSocketЭ�� (ǰ�� ? ���)

```javascript
// ����ʶ������
{
    type: "audio_data",
    data: ArrayBuffer,  // ��Ƶ����
    format: "pcm",
    sampleRate: 16000
}

// ����ʶ����
{
    type: "voice_result",
    recognized_text: "�������",
    confidence: 0.95,
    iot_status: "sent_to_printer",
    target_device: "braille_printer_001"
}
```

#### 2. MQTTЭ�� (�ƶ� ? 4Gģ��)

```json
// ����ָ���·�
{
    "command_id": "1699123456789",
    "recognized_text": "�������",
    "confidence": 0.95,
    "language": "zh-cn",
    "command_type": "print_braille",
    "timestamp": 1699123456789
}

// ��ӡ����ϱ�
{
    "device_id": "braille_printer_001",
    "print_result": {
        "status": "completed",
        "text": "�������",
        "duration": 30,
        "error": null
    },
    "timestamp": 1699123456789
}
```

#### 3. ����Э�� (4Gģ�� ? CH32V307)

```json
// ������ӡָ��
{
    "type": "VOICE_PRINT",
    "text": "�������",
    "confidence": 0.95,
    "command_id": "1699123456789"
}

// ״̬�ϱ�
{
    "type": "status_update",
    "printer_status": "printing",
    "audio_status": "ready",
    "timestamp": 1699123456789
}
```

## ?? ����ָ��

### �����µ�����ָ��

1. **�޸�����ʶ���߼�**:
```javascript
// voice-backend/server-iot.js
async function processVoiceRecognition(audioData, clientId) {
    // ... ʶ���߼�
    
    // �Զ���ָ���
    if (result.recognized_text.includes('�����¶�')) {
        await iotManager.sendVoiceCommand(targetDeviceId, {
            recognized_text: result.recognized_text,
            command_type: 'temperature_control'  // ��ָ������
        });
    }
}
```

2. **����Air780e�����߼�**:
```lua
-- air780e-scripts/main.lua
function handle_voice_command(voice_data)
    local mcu_command = {
        type = "VOICE_CMD",
        text = voice_data.recognized_text,
        command_type = voice_data.command_type
    }
    send_to_mcu(mcu_command)
end
```

3. **��չCH32V307�̼�**:
```c
// 01 - ����/User/main.c
void process_voice_command(char* text, char* command_type) {
    if (strcmp(command_type, "temperature_control") == 0) {
        // �����¶ȿ����߼�
        handle_temperature_command(text);
    }
}
```

### �����µ���ƽ̨

֧�ֶ�����ƽ̨����:

#### ������IoTƽ̨
```lua
-- Air780e����
aliyun = {
    enabled = true,
    endpoint = "iot-as-mqtt.cn-shanghai.aliyuncs.com",
    product_key = "your_product_key",
    device_name = "your_device_name",
    device_secret = "your_device_secret"
}
```

#### ��Ѷ��IoTƽ̨
```lua
-- Air780e����  
tencent = {
    enabled = true,
    endpoint = "iotcloud-mqtt.gz.tencentcloudapi.com",
    product_id = "your_product_id",
    device_name = "your_device_name",
    device_secret = "your_device_secret"
}
```

## ? ʹ��ָ��

### ��������

1. **����ʶ���ӡ**:
   - ��Web����
   - ���"��ʼ¼��"
   - ˵��Ҫ��ӡ������
   - ϵͳ�Զ�ʶ�𲢷��͵���ӡ��

2. **�ֶ��ı���ӡ**:
   - ��Web���������ı�
   - ���"���͵���ӡ��"
   - �ı�ֱ�ӷ��͵��豸

3. **�豸״̬���**:
   - ʵʱ�鿴�豸����״̬
   - ��ش�ӡ����
   - �鿴������Ϣ

### �߼�����

1. **�豸У׼**:
```bash
# ͨ�����ڷ���У׼����
CAL_X 2000          # X���ƶ�2000������
CAL_Y 1500          # Y���ƶ�1500������
CAL_SET X 2000 50.0 # ����X��У׼����
```

2. **ϵͳ����**:
```bash
# ���Ը���ģ��
HELP                    # �鿴��������
TEST_UART3 "������Ϣ"   # ����4Gģ��ͨ��
PRINT "���Դ�ӡ"        # ����ä�Ĵ�ӡ
```

## ? �����ų�

### ��������

1. **4Gģ������ʧ��**:
   - ���SIM���Ƿ������ȷ
   - ȷ���ź�ǿ�� > -90dBm
   - ��֤APN����

2. **MQTT���ӶϿ�**:
   - �����������
   - ��֤MQTT��������ַ�Ͷ˿�
   - ȷ���û���������ȷ

3. **����ʶ��ʧ��**:
   - ���Ѷ��API����
   - ȷ����˷���������
   - ��֤��Ƶ����������

4. **ä�Ĵ�ӡ�쳣**:
   - ��鲽���������
   - ��֤����������
   - ȷ��У׼������ȷ

### ��־����

```bash
# �鿴ϵͳ��־
tail -f logs/system.log

# �鿴MQTTͨ����־  
tail -f logs/mqtt.log

# �鿴�豸״̬
curl http://127.0.0.1:8080/health
```

## ? �����Ż�

### ϵͳ����ָ��

- **����ʶ���ӳ�**: < 2��
- **4Gͨ���ӳ�**: < 500ms
- **��ӡ��Ӧʱ��**: < 1��
- **ϵͳ�ȶ���**: > 99.5%

### �Ż�����

1. **�����Ż�**:
   - ʹ��CDN���پ�̬��Դ
   - ����GZIPѹ��
   - ����HTTP/2

2. **�豸�Ż�**:
   - ������������ٶ�
   - �Ż���ӡ·���㷨
   - ������Ч�ƶ�

3. **��ƽ̨�Ż�**:
   - ʹ��QoS 1ȷ����Ϣ�ʹ�
   - ʵ����Ϣ�ش�����
   - ���ø��ؾ���

## ? ����ָ��

### ������������

```bash
# ��¡��Ŀ
git clone https://github.com/your-repo/braille-printer-iot.git

# ��װ����
npm install

# ��������������
npm run dev
```

### �ύ����

1. Fork��Ŀ
2. �������ܷ�֧: `git checkout -b feature/new-feature`
3. �ύ����: `git commit -am 'Add new feature'`
4. ���ͷ�֧: `git push origin feature/new-feature`
5. �ύPull Request

## ? ����֤

����Ŀ���� MIT ����֤ - �鿴 [LICENSE](LICENSE) �ļ��˽����顣

## ? ����֧��

### ��ϵ��ʽ
- **�����ĵ�**: ��� `docs/` Ŀ¼
- **���ⷴ��**: GitHub Issues
- **��������**: ��ĿWiki

### �������
- [CH32V307�ٷ��ĵ�](https://www.wch.cn/products/CH32V307.html)
- [Air780e����ָ��](https://doc.openluat.com/wiki/21?wiki_page_id=2070)
- [Ѷ������API](https://www.xfyun.cn/services/voicedictation)

---

## ? ��Ŀ����

����һ��**������Ҫ����ֵ**�����ܻ����ϰ��豸��Ŀ��

- ? **�����Ƚ�**: ����ʶ�� + IoT + ���ܻ�е����
- ? **ʵ����ǿ**: ���������ʿ��ʵ������  
- ? **�ܹ�����**: ģ�黯������չ��ϵͳ���
- ? **���̳���**: �����Ĳ��ԡ�У׼���ĵ���ϵ
- ? **�ƶ˼���**: 4G���� + MQTT��ƽ̨ʵ��Զ�̿���

**�ÿƼ�������ÿһ���ˣ�Ϊ����Ⱥ���������õ��������** ? 