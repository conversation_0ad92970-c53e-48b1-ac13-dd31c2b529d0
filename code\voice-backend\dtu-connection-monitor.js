/**
 * DTU设备连接监听器
 * 监听DTU设备的连接状态和数据传输
 */

const WebSocket = require('ws');
const net = require('net');

class DTUConnectionMonitor {
    constructor() {
        this.wsPort = 8081;
        this.tcpPort = 8082; // DTU专用TCP端口
        this.connectedDevices = new Map();
        this.wsServer = null;
        this.tcpServer = null;
    }

    /**
     * 启动WebSocket监听器
     */
    startWebSocketMonitor() {
        console.log('🔍 启动WebSocket连接监听器...');
        console.log(`📡 监听端口: ${this.wsPort}`);

        try {
            // 连接到现有的WebSocket服务器进行监听
            const ws = new WebSocket(`ws://localhost:${this.wsPort}/voice-ws`);
            
            ws.on('open', () => {
                console.log('✅ 已连接到WebSocket服务器');
                console.log('👂 开始监听DTU设备连接...');
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    if (message.type === 'dtu_register' || message.source === 'dtu') {
                        console.log('📱 检测到DTU设备消息:', message);
                        this.handleDTUMessage(message);
                    }
                } catch (error) {
                    // 忽略非JSON消息
                }
            });

            ws.on('error', (error) => {
                console.log('❌ WebSocket连接错误:', error.message);
            });

            ws.on('close', () => {
                console.log('🔌 WebSocket连接已断开');
            });

        } catch (error) {
            console.log('❌ 无法连接到WebSocket服务器:', error.message);
        }
    }

    /**
     * 启动TCP服务器（专门用于DTU设备连接）
     */
    startTCPServer() {
        console.log('🚀 启动DTU专用TCP服务器...');
        console.log(`📡 监听端口: ${this.tcpPort}`);

        this.tcpServer = net.createServer((socket) => {
            const clientInfo = `${socket.remoteAddress}:${socket.remotePort}`;
            console.log(`🔗 DTU设备连接: ${clientInfo}`);

            // 设置设备信息
            const deviceId = `DTU_${Date.now()}`;
            this.connectedDevices.set(deviceId, {
                socket: socket,
                address: socket.remoteAddress,
                port: socket.remotePort,
                connectTime: new Date(),
                lastHeartbeat: new Date()
            });

            // 发送欢迎消息
            const welcomeMsg = JSON.stringify({
                type: 'welcome',
                message: 'DTU设备连接成功',
                deviceId: deviceId,
                serverTime: new Date().toISOString()
            });
            socket.write(welcomeMsg + '\n');

            // 处理数据接收
            socket.on('data', (data) => {
                const message = data.toString().trim();
                console.log(`📨 收到DTU数据 [${clientInfo}]: ${message}`);
                
                try {
                    const jsonData = JSON.parse(message);
                    this.handleDTUMessage(jsonData, deviceId);
                } catch (error) {
                    console.log(`📝 收到文本数据: ${message}`);
                    this.handleTextMessage(message, deviceId);
                }
            });

            // 处理连接断开
            socket.on('close', () => {
                console.log(`🔌 DTU设备断开: ${clientInfo}`);
                this.connectedDevices.delete(deviceId);
            });

            // 处理错误
            socket.on('error', (error) => {
                console.log(`❌ DTU连接错误 [${clientInfo}]: ${error.message}`);
                this.connectedDevices.delete(deviceId);
            });
        });

        this.tcpServer.listen(this.tcpPort, '0.0.0.0', () => {
            console.log(`✅ DTU TCP服务器已启动，监听端口 ${this.tcpPort}`);
        });

        this.tcpServer.on('error', (error) => {
            console.log(`❌ TCP服务器错误: ${error.message}`);
        });
    }

    /**
     * 处理DTU JSON消息
     */
    handleDTUMessage(message, deviceId = null) {
        console.log('📋 处理DTU消息:', message);

        switch (message.type) {
            case 'dtu_register':
                console.log(`🔐 DTU设备注册: IMEI=${message.imei}`);
                if (deviceId && this.connectedDevices.has(deviceId)) {
                    this.connectedDevices.get(deviceId).imei = message.imei;
                }
                break;

            case 'heartbeat':
            case 'ping':
                console.log(`💓 DTU心跳: ${deviceId || 'WebSocket'}`);
                if (deviceId && this.connectedDevices.has(deviceId)) {
                    this.connectedDevices.get(deviceId).lastHeartbeat = new Date();
                }
                break;

            case 'voice_command':
                console.log(`🎤 语音指令: ${message.command}`);
                break;

            case 'print_status':
                console.log(`🖨️ 打印状态: ${message.status}`);
                break;

            default:
                console.log(`📦 其他消息类型: ${message.type}`);
        }
    }

    /**
     * 处理文本消息
     */
    handleTextMessage(message, deviceId) {
        if (message.toUpperCase() === 'PING') {
            console.log(`💓 DTU心跳 (文本): ${deviceId}`);
            const device = this.connectedDevices.get(deviceId);
            if (device) {
                device.lastHeartbeat = new Date();
                device.socket.write('PONG\n');
            }
        } else {
            console.log(`📝 DTU文本消息: ${message}`);
        }
    }

    /**
     * 显示连接状态
     */
    showConnectionStatus() {
        console.log('\n📊 DTU设备连接状态:');
        console.log('=' * 40);

        if (this.connectedDevices.size === 0) {
            console.log('📱 当前无DTU设备连接');
            console.log('💡 请检查DTU设备配置:');
            console.log(`   服务器地址: 10.5.170.78`);
            console.log(`   WebSocket端口: ${this.wsPort}`);
            console.log(`   TCP端口: ${this.tcpPort}`);
        } else {
            this.connectedDevices.forEach((device, deviceId) => {
                const uptime = Math.floor((Date.now() - device.connectTime.getTime()) / 1000);
                const lastHeartbeat = Math.floor((Date.now() - device.lastHeartbeat.getTime()) / 1000);
                
                console.log(`📱 设备ID: ${deviceId}`);
                console.log(`   地址: ${device.address}:${device.port}`);
                console.log(`   IMEI: ${device.imei || '未知'}`);
                console.log(`   连接时长: ${uptime}秒`);
                console.log(`   最后心跳: ${lastHeartbeat}秒前`);
                console.log('');
            });
        }
    }

    /**
     * 启动监听器
     */
    start() {
        console.log('🚀 启动DTU连接监听器');
        console.log('=' * 50);

        // 启动WebSocket监听
        this.startWebSocketMonitor();

        // 启动TCP服务器
        this.startTCPServer();

        // 定期显示连接状态
        setInterval(() => {
            this.showConnectionStatus();
        }, 30000); // 每30秒显示一次

        // 初始状态显示
        setTimeout(() => {
            this.showConnectionStatus();
        }, 3000);

        console.log('\n🎯 监听器已启动，等待DTU设备连接...');
        console.log('💡 DTU设备配置建议:');
        console.log(`   主服务器: 10.5.170.78:${this.wsPort} (WebSocket)`);
        console.log(`   备用服务器: 10.5.170.78:${this.tcpPort} (TCP)`);
        console.log('\n按 Ctrl+C 停止监听');
    }

    /**
     * 停止监听器
     */
    stop() {
        console.log('\n🛑 停止DTU连接监听器...');
        
        if (this.tcpServer) {
            this.tcpServer.close();
        }

        this.connectedDevices.forEach((device) => {
            device.socket.end();
        });

        console.log('✅ 监听器已停止');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const monitor = new DTUConnectionMonitor();
    
    // 处理退出信号
    process.on('SIGINT', () => {
        monitor.stop();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        monitor.stop();
        process.exit(0);
    });

    monitor.start();
}

module.exports = DTUConnectionMonitor;
