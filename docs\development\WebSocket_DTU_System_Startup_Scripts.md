# WebSocket+DTU系统启动脚本文档

## 概述

本文档描述了WebSocket+DTU智能语音识别盲文打印系统的统一启动脚本集合，提供了完整的系统启动、状态检查、和停止功能。

## 脚本文件列表

### 1. start_websocket_dtu_system.bat
**主要启动脚本** - 完整功能的系统启动器

#### 功能特性
- ✅ 完整的环境检查（Node.js版本、项目结构、依赖包）
- ✅ 自动安装缺失的依赖包
- ✅ 分步启动WebSocket后端和前端服务器
- ✅ 实时连接测试和状态验证
- ✅ 详细的系统信息显示
- ✅ 交互式控制菜单（重启、状态、测试、帮助、退出）
- ✅ 自动浏览器启动选项
- ✅ 完整的错误处理和用户提示

#### 启动流程
1. **环境检查**: Node.js版本验证
2. **目录验证**: 检查关键文件存在性
3. **依赖检查**: 自动安装npm包
4. **后端启动**: WebSocket+DTU服务器 (端口8081)
5. **前端启动**: Web界面服务器 (端口4000)
6. **状态显示**: 完整的系统信息和访问地址

#### 使用方法
```bash
# 在项目根目录运行
start_websocket_dtu_system.bat
```

### 2. quick_start_websocket_dtu.bat
**快速启动脚本** - 简化版一键启动

#### 功能特性
- ✅ 最小化环境检查
- ✅ 快速启动服务器
- ✅ 自动打开浏览器
- ✅ 简洁的用户界面
- ✅ 一键停止功能

#### 适用场景
- 开发环境快速测试
- 已确认环境正常的情况下
- 需要频繁重启系统的调试场景

#### 使用方法
```bash
# 快速启动（适合开发调试）
quick_start_websocket_dtu.bat
```

### 3. check_websocket_dtu_status.bat
**系统状态检查脚本** - 全面的系统诊断工具

#### 检查项目
1. **Node.js环境**: 版本信息和可用性
2. **端口占用**: 8081和4000端口状态
3. **进程状态**: Node.js进程运行情况
4. **项目文件**: 关键文件完整性
5. **依赖包**: npm包安装状态
6. **网络连接**: 银尔达DTU平台连通性
7. **HTTP服务**: 前端服务器响应测试
8. **WebSocket服务**: 后端服务器响应测试
9. **系统资源**: 内存使用情况
10. **配置验证**: 配置文件语法检查

#### 使用方法
```bash
# 系统诊断
check_websocket_dtu_status.bat
```

### 4. stop_websocket_dtu_system.bat
**系统停止脚本** - 安全的系统关闭工具

#### 功能特性
- ✅ 进程检测和确认
- ✅ 优雅停止机制
- ✅ 强制停止备用方案
- ✅ 临时文件清理
- ✅ 停止状态验证

#### 停止流程
1. **进程检测**: 查找运行中的Node.js进程
2. **用户确认**: 防止误操作
3. **优雅停止**: 正常终止进程
4. **强制停止**: 处理无响应进程
5. **文件清理**: 删除临时文件
6. **状态验证**: 确认完全停止

#### 使用方法
```bash
# 安全停止系统
stop_websocket_dtu_system.bat
```

## 系统架构支持

### WebSocket+DTU混合架构
```
前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
```

### 端口配置
- **WebSocket后端**: 8081端口
- **前端Web服务器**: 4000端口
- **银尔达DTU平台**: dtu.yinled.com:8888

### 设备信息
- **DTU设备型号**: 银尔达 M100P
- **IMEI**: 869080075169294
- **ICCID**: 898604021024C0050919
- **数据套餐**: 4G 30M/月

## 环境要求

### 必需环境
- **Node.js**: 16.x或更高版本
- **操作系统**: Windows 10/11
- **网络**: 互联网连接（访问银尔达DTU平台）

### 项目依赖
- **后端依赖**: ws, crypto, formdata-node
- **前端依赖**: express, http-proxy-middleware

## 故障排除

### 常见问题

#### 1. 端口被占用
**症状**: 启动失败，提示端口已被使用
**解决方案**:
```bash
# 查找占用进程
netstat -ano | findstr :8081
netstat -ano | findstr :4000

# 终止占用进程
taskkill /f /pid [进程ID]
```

#### 2. Node.js环境问题
**症状**: 提示未找到Node.js
**解决方案**:
- 安装Node.js: https://nodejs.org/
- 确认PATH环境变量包含Node.js路径
- 重启命令行窗口

#### 3. 依赖包安装失败
**症状**: npm install报错
**解决方案**:
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rmdir /s node_modules
npm install
```

#### 4. WebSocket连接失败
**症状**: 前端无法连接WebSocket
**解决方案**:
- 检查防火墙设置
- 确认8081端口未被阻止
- 检查WebSocket服务器日志

#### 5. DTU平台连接异常
**症状**: 无法连接银尔达DTU平台
**解决方案**:
- 检查网络连接
- 验证DTU设备在线状态
- 确认IMEI和ICCID正确

## 开发调试

### 日志查看
- **后端日志**: WebSocket+DTU后端服务器窗口
- **前端日志**: 前端Web界面窗口
- **浏览器日志**: F12开发者工具Console

### 配置修改
- **主配置文件**: `code/voice-backend/config.js`
- **环境配置**: 通过环境变量覆盖默认配置
- **端口修改**: 修改配置文件中的端口设置

### 性能监控
- **内存使用**: 通过状态检查脚本查看
- **网络连接**: 实时监控DTU平台连接状态
- **响应时间**: WebSocket消息处理延迟

## 部署建议

### 生产环境
1. 使用`start_websocket_dtu_system.bat`进行完整启动
2. 定期运行`check_websocket_dtu_status.bat`进行健康检查
3. 配置系统服务自动启动
4. 设置日志轮转和监控

### 开发环境
1. 使用`quick_start_websocket_dtu.bat`快速启动
2. 频繁使用状态检查脚本进行调试
3. 根据需要修改配置文件
4. 使用停止脚本安全关闭系统

## 版本信息

- **脚本版本**: 2.0.0
- **系统架构**: WebSocket+DTU混合模式
- **兼容性**: Windows 10/11, Node.js 16+
- **最后更新**: 2025年7月4日

## 总结

这套启动脚本集合为WebSocket+DTU智能语音识别盲文打印系统提供了完整的生命周期管理功能，从环境检查、系统启动、状态监控到安全停止，确保系统的稳定运行和便捷管理。通过不同的脚本满足不同场景的需求，提高了系统的可用性和维护效率。
