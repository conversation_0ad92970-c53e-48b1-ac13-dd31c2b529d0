const WebSocket = require('ws');
const http = require('http');
const config = require('./config');
const XunfeiVoiceRecognition = require('./xunfei-voice-config');
const DTUIntegration = require('./dtu-integration');

// 设置字符编码
process.stdout.setEncoding('utf8');
if (process.platform === 'win32') {
    // Windows 平台设置控制台编�?
    try {
        const { spawn } = require('child_process');
        spawn('chcp', ['65001'], { stdio: 'ignore' });
    } catch (error) {
        console.log('无法设置控制台编码，继续运行...');
    }
}

// 初始化讯飞语音识�?
let voiceRecognizer = null;
if (config.xunfei.enabled) {
    voiceRecognizer = new XunfeiVoiceRecognition(config.xunfei);
    console.log('🔊 讯飞语音识别已启�?');
} else {
    console.log('🎭 演示模式已启用（要使用真实语音识别，请配置讯飞API�?');
}



// 初始化银尔达DTU管理器
let dtuManager = null;
if (config.dtu.enabled) {
    dtuManager = new DTUIntegration(config.dtu.api);

    // 初始化DTU管理器
    dtuManager.initialize().then(() => {
        console.log('🌐 银尔达DTU管理器已启动');
        setupDTUEventHandlers();
    }).catch(error => {
        console.error('❌ DTU管理器启动失败:', error.message);
        console.log('💡 将在语音识别模式下运行，不支持DTU设备通信');
    });
} else {
    console.log('📱 DTU设备管理未启用');
}

// 存储WebSocket连接
const webSocketClients = new Set();



// 设置DTU事件处理器
function setupDTUEventHandlers() {
    if (!dtuManager) return;

    // DTU连接成功事件
    dtuManager.on('connected', (data) => {
        console.log('✅ DTU设备连接成功:', data);

        // 广播DTU连接状态到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'dtu_connected',
            deviceId: data.deviceId,
            status: data.status,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU数据发送成功事件
    dtuManager.on('dataSent', (data) => {
        console.log('📤 DTU数据发送成功:', data);

        // 通知前端数据发送成功
        const message = JSON.stringify({
            type: 'dtu_data_sent',
            deviceId: data.deviceId,
            data: data.data,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU状态更新事件
    dtuManager.on('statusUpdate', (status) => {
        console.log('📊 DTU设备状态更新:', status);

        // 广播设备状态到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'dtu_status_update',
            ...status,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU连接丢失事件
    dtuManager.on('connectionLost', (data) => {
        console.log('⚠️ DTU连接丢失:', data);

        // 通知前端连接丢失
        const message = JSON.stringify({
            type: 'dtu_connection_lost',
            retryCount: data.retryCount,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU错误事件
    dtuManager.on('error', (error) => {
        console.error('❌ DTU错误:', error);

        // 通知前端错误信息
        const message = JSON.stringify({
            type: 'dtu_error',
            error: error.message,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });
}

const server = http.createServer((req, res) => {
    // 设置 CORS �?
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            status: 'ok',
            service: 'voice-backend',
            voiceApi: config.xunfei.enabled ? 'xunfei' : 'demo',
            iotEnabled: config.iot.enabled,
            iotConnected: iotManager ? iotManager.isConnected : false,
            deviceCount: iotManager ? iotManager.getDevices().length : 0,
            timestamp: new Date().toISOString()
        }));
    } else if (req.url === '/api/voice-config' && req.method === 'GET') {
        // 返回语音配置信息的API
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            provider: config.xunfei.enabled ? 'xunfei' : 'demo',
            status: config.xunfei.enabled ? 'active' : 'demo_mode',
            message: config.xunfei.enabled ? '讯飞语音识别已启�?' : '演示模式（需配置讯飞API启用真实识别�?'
        }));
    } else if (req.url === '/api/devices' && req.method === 'GET') {
        // 新增：获取设备列表API
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        const devices = iotManager ? iotManager.getDevices() : [];
        res.end(JSON.stringify({
            devices,
            count: devices.length,
            iotEnabled: config.iot.enabled,
            timestamp: new Date().toISOString()
        }));
    } else if (req.url.startsWith('/api/device/') && req.method === 'POST') {
        // 新增：向设备发送指令API
        const pathParts = req.url.split('/');
        const deviceId = pathParts[3];
        const action = pathParts[4]; // �? 'command'

        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', async () => {
            try {
                const data = JSON.parse(body);

                if (iotManager && action === 'command') {
                    await iotManager.sendCommandToDevice(deviceId, data);
                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: true,
                        message: '指令发送成�?',
                        deviceId,
                        command: data
                    }));
                } else {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ error: 'IoT管理器未启用或无效操�?' }));
                }
            } catch (error) {
                res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({ error: error.message }));
            }
        });
    } else if (req.url === '/api/translate' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const result = {
                    original_text: data.text,
                    translated_text: `[ģ�ⷭ�뵽${data.target_lang}] ${data.text}`,
                    source_lang: data.source_lang,
                    target_lang: data.target_lang,
                    confidence: 0.95,
                    provider: 'demo'
                };
                res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify(result));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
        });
    } else if (req.url === '/api/send/text' && req.method === 'POST') {
        // �������������ֵ�CH32V307��API
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', async () => {
            try {
                const data = JSON.parse(body);
                const { text, device_id } = data;

                if (!text || text.trim().length === 0) {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: false,
                        error: '�������ݲ���Ϊ��'
                    }));
                    return;
                }

                console.log(`? ��������: ${text}`);

                // ����������Ϣ
                const textMessage = {
                    type: 'text_message',
                    text: text,
                    timestamp: new Date().toISOString(),
                    sender: 'web_interface'
                };

                if (iotManager) {
                    try {
                        // ȷ��Ŀ���豸
                        const targetDeviceId = device_id || 'ch32v307_audio_01';

                        // ���͵�ָ���豸�����������豸
                        const targetDevices = device_id
                            ? [device_id]
                            : iotManager.getDevices()
                                .filter(device => iotManager.isDeviceOnline(device.id))
                                .map(device => device.id);

                        if (targetDevices.length === 0) {
                            targetDevices.push('ch32v307_audio_01'); // Ĭ���豸
                        }

                        for (const deviceId of targetDevices) {
                            await iotManager.sendCommandToDevice(deviceId, textMessage);
                            console.log(`? ���ַ��ͳɹ�: ${deviceId}`);
                        }

                        // ֪ͨ����WebSocket�ͻ���
                        const notificationMessage = JSON.stringify({
                            type: 'text_sent',
                            text: text,
                            target_devices: targetDevices,
                            timestamp: new Date().toISOString(),
                            success: true
                        });

                        webSocketClients.forEach(ws => {
                            if (ws.readyState === WebSocket.OPEN) {
                                ws.send(notificationMessage);
                            }
                        });

                        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            success: true,
                            message: '���ַ��ͳɹ�',
                            text: text,
                            target_devices: targetDevices,
                            timestamp: new Date().toISOString()
                        }));

                    } catch (error) {
                        console.error(`? ���ַ���ʧ��:`, error.message);
                        res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            success: false,
                            error: '����ʧ��: ' + error.message
                        }));
                    }
                } else {
                    // IoT������δ����ʱ����ʾģʽ
                    console.log(`? ��ʾģʽ - ��������: ${text}`);

                    // ֪ͨWebSocket�ͻ���
                    const demoMessage = JSON.stringify({
                        type: 'text_sent',
                        text: text,
                        target_devices: ['ch32v307_demo'],
                        timestamp: new Date().toISOString(),
                        success: true,
                        demo_mode: true
                    });

                    webSocketClients.forEach(ws => {
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(demoMessage);
                        }
                    });

                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: true,
                        message: '���ַ��ͳɹ� (��ʾģʽ)',
                        text: text,
                        target_devices: ['ch32v307_demo'],
                        demo_mode: true,
                        timestamp: new Date().toISOString()
                    }));
                }

            } catch (error) {
                console.error('�������ַ�������ʱ����:', error.message);
                res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({
                    success: false,
                    error: '�������ڲ�����: ' + error.message
                }));
            }
        });
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({ error: 'Not found' }));
    }
});

const wss = new WebSocket.Server({ noServer: true });

let clientCounter = 0;

// 语音识别处理函数
async function processVoiceRecognition(audioData, clientId) {
    console.log(`[${clientId}] 开始处理语音识�? - 数据大小: ${audioData.length} bytes`);

    let result;

    if (config.xunfei.enabled && voiceRecognizer) {
        try {
            console.log(`[${clientId}] 使用讯飞API进行语音识别...`);

            // 验证音频数据
            if (audioData.length < 500) {
                console.log(`[${clientId}] 音频数据太小，可能无效`);
                result = {
                    type: 'voice_result',
                    recognized_text: '录音时间太短，请重新录制',
                    confidence: 0.1,
                    timestamp: new Date().toISOString(),
                    provider: 'xunfei',
                    status: 'warning'
                };
            } else {
                const recognitionResult = await voiceRecognizer.recognizeAudioSimple(audioData);

                // 确保中文字符正确编码
                const recognizedText = recognitionResult.text;
                console.log(`[${clientId}]识别结果: `, recognizedText);

                // 检查是否包含中文字符，确保正确编码
                const processedText = Buffer.from(recognizedText, 'utf8').toString('utf8');

                result = {
                    type: 'voice_result',
                    recognized_text: processedText,
                    confidence: recognitionResult.confidence || 0.8,
                    timestamp: new Date().toISOString(),
                    provider: 'xunfei',
                    status: recognitionResult.isError ? 'error' : 'success'
                };
            }
        } catch (error) {
            console.error(`[${clientId}]讯飞API调用失败: `, error.message);
            result = {
                type: 'voice_result',
                recognized_text: `[API调用异常] 讯飞语音识别: ${error.message}`,
                confidence: 0.1,
                timestamp: new Date().toISOString(),
                provider: 'xunfei',
                status: 'error'
            };
        }
    } else {
        // 演示模式
        console.log(`[${clientId}]使用演示模式...`);
        const demoTexts = [
            '打开客厅�?',
            '关闭空调',
            '查询温度',
            '设置温度�?25�?',
            '打开窗帘',
            '播放音乐'
        ];

        const randomText = demoTexts[Math.floor(Math.random() * demoTexts.length)];

        result = {
            type: 'voice_result',
            recognized_text: `${randomText}`,
            confidence: 0.95,
            timestamp: new Date().toISOString(),
            provider: 'demo',
            status: 'success'
        };
    }



    // 自动转发语音识别结果到DTU设备
    if (dtuManager && config.dtu.forwarding.autoForwardVoice &&
        result.confidence >= config.dtu.forwarding.confidenceThreshold &&
        result.status === 'success') {

        try {
            console.log(`🚀 转发语音识别结果到DTU设备: ${config.dtu.api.deviceId}`);

            const voiceData = {
                text: result.recognized_text,
                confidence: result.confidence,
                provider: result.provider,
                timestamp: result.timestamp
            };

            await dtuManager.sendVoiceResult(voiceData);
            console.log(`✅ 语音指令已发送到DTU设备: ${config.dtu.api.deviceId}`);

            // 添加DTU转发信息到结果中
            result.forwarded_to_dtu = {
                deviceId: config.dtu.api.deviceId,
                success: true,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ DTU语音结果转发失败:', error.message);
            result.forwarded_to_dtu = {
                deviceId: config.dtu.api.deviceId,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    return result;
}

wss.on('connection', (ws, req) => {
    clientCounter++;
    const clientId = `voice_client_${clientCounter}`;
    console.log(`[${new Date().toLocaleTimeString()}] 客户端连�?: ${clientId}`);

    // 添加到WebSocket客户端集�?
    webSocketClients.add(ws);

    // 发送配置信息给客户�?
    ws.send(JSON.stringify({
        type: 'config_info',
        voiceProvider: config.xunfei.enabled ? 'xunfei' : 'demo',
        dtuEnabled: config.dtu.enabled,
        dtuConnected: dtuManager ? dtuManager.isConnected : false,
        dtuDeviceId: config.dtu.api.deviceId,
        message: config.xunfei.enabled ? '讯飞语音识别已就�?' : '演示模式已就�?'
    }));

    ws.on('message', async (message) => {
        try {
            // 区分二进制音频数据和JSON命令
            if (Buffer.isBuffer(message)) {
                // 二进制音频数�?
                console.log(`[${clientId}] 收到音频数据: ${message.length} bytes`);

                // 使用新的语音识别处理函数
                const result = await processVoiceRecognition(message, clientId);

                // 确保消息正确编码后发�?
                const jsonString = JSON.stringify(result);
                ws.send(jsonString);

            } else {
                // JSON文本命令
                const messageText = message.toString('utf8');
                console.log(`[${clientId}] 收到消息: ${messageText}`);

                try {
                    const data = JSON.parse(messageText);

                    if (data.type === 'heartbeat') {
                        ws.send(JSON.stringify({ type: 'heartbeat_ack' }));
                    } else if (data.type === 'get_config') {
                        // 客户端请求配置信�?
                        ws.send(JSON.stringify({
                            type: 'config_info',
                            voiceProvider: config.xunfei.enabled ? 'xunfei' : 'demo',
                            dtuEnabled: config.dtu.enabled,
                            dtuConnected: dtuManager ? dtuManager.isConnected : false,
                            dtuDeviceId: config.dtu.api.deviceId,
                            message: config.xunfei.enabled ? '讯飞语音识别已启�?' : '演示模式（需配置讯飞API�?'
                        }));

                    } else if (data.type === 'get_dtu_status') {
                        // 客户端请求DTU状态
                        if (dtuManager) {
                            try {
                                const status = await dtuManager.getDeviceStatus();
                                const connectionStatus = dtuManager.getConnectionStatus();
                                ws.send(JSON.stringify({
                                    type: 'dtu_status',
                                    deviceStatus: status,
                                    connectionStatus: connectionStatus,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_status_error',
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_status_error',
                                error: 'DTU管理器未启用',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'send_dtu_command') {
                        // 客户端发送DTU命令
                        if (dtuManager && data.command) {
                            try {
                                const result = await dtuManager.sendControlCommand(data.command, data.params || {});
                                ws.send(JSON.stringify({
                                    type: 'dtu_command_result',
                                    success: true,
                                    command: data.command,
                                    result: result,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_command_result',
                                    success: false,
                                    command: data.command,
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_command_result',
                                success: false,
                                error: 'DTU管理器未启用或命令参数缺失',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'send_dtu_text') {
                        // 客户端发送文字到DTU设备
                        if (dtuManager && data.text) {
                            try {
                                const result = await dtuManager.sendToDevice({
                                    type: 'text_message',
                                    text: data.text,
                                    source: 'websocket_client'
                                });
                                ws.send(JSON.stringify({
                                    type: 'dtu_text_result',
                                    success: true,
                                    text: data.text,
                                    result: result,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_text_result',
                                    success: false,
                                    text: data.text,
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_text_result',
                                success: false,
                                error: 'DTU管理器未启用或文字参数缺失',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'translate_req') {
                        const translatedText = `[翻译�?${data.lang}] ${data.text}`;
                        ws.send(JSON.stringify({
                            type: 'translation_result',
                            text: translatedText,
                            lang: data.lang,
                            timestamp: new Date().toISOString(),
                            provider: 'demo'
                        }));
                    }
                } catch (parseError) {
                    // 如果不是JSON，当作普通文本消息处�?
                    console.log(`[${clientId}] 收到文本: ${messageText}`);
                }
            }
        } catch (error) {
            console.error(`处理消息时出�?: ${error.message}`);
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息处理失败',
                detail: error.message
            }));
        }
    });

    ws.on('close', () => {
        console.log(`[${new Date().toLocaleTimeString()}] 客户端断开连接: ${clientId}`);
        webSocketClients.delete(ws);
    });

    ws.on('error', (error) => {
        console.error(`[${clientId}] WebSocket错误: ${error.message}`);
        webSocketClients.delete(ws);
    });
});

server.on('upgrade', (request, socket, head) => {
    const pathname = request.url;
    if (pathname === '/voice-ws') {
        wss.handleUpgrade(request, socket, head, (ws) => {
            wss.emit('connection', ws, request);
        });
    } else {
        socket.destroy();
    }
});

const PORT = 8080;
server.listen(PORT, '0.0.0.0', () => {
    console.log('🎉 语音识别后端服务器已启动!');
    console.log(`🔗 WebSocket: ws://0.0.0.0:${PORT}/voice-ws`);
    console.log(`📡 API接口: http://0.0.0.0:${PORT}/api/translate`);
    console.log(`❤️  健康检�?: http://0.0.0.0:${PORT}/health`);
    console.log(`⚙️  配置信息: http://0.0.0.0:${PORT}/api/voice-config`);
    console.log(`🌐 设备管理: http://0.0.0.0:${PORT}/api/devices`);
    console.log(`👥 当前连接�?: ${wss.clients.size}`);

    if (config.xunfei.enabled) {
        console.log('🌟 讯飞语音识别已启用，支持真实语音识别');
    } else {
        console.log('💡 提示：要启用真实语音识别，请编辑 config.js 文件配置讯飞API');
    }

    console.log('🚀 DTU设备管理已启用，支持自动转发语音指令到设备');
}); 