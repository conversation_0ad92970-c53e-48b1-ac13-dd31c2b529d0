const WebSocket = require('ws');
const http = require('http');
const config = require('./config');
const XunfeiVoiceRecognition = require('./xunfei-voice-config');
const DTUIntegration = require('./dtu-integration');
const configManager = require('./config-manager');

// 设置字符编码
process.stdout.setEncoding('utf8');
if (process.platform === 'win32') {
    // Windows 平台设置控制台编�?
    try {
        const { spawn } = require('child_process');
        spawn('chcp', ['65001'], { stdio: 'ignore' });
    } catch (error) {
        console.log('无法设置控制台编码，继续运行...');
    }
}

// 初始化讯飞语音识�?
let voiceRecognizer = null;
if (config.xunfei.enabled) {
    voiceRecognizer = new XunfeiVoiceRecognition(config.xunfei);
    console.log('🔊 讯飞语音识别已启�?');
} else {
    console.log('🎭 演示模式已启用（要使用真实语音识别，请配置讯飞API�?');
}



// 初始化银尔达DTU管理器
let dtuManager = null;
if (config.dtu.enabled) {
    dtuManager = new DTUIntegration(config.dtu.api);

    // 初始化DTU管理器
    dtuManager.initialize().then(() => {
        console.log('🌐 银尔达DTU管理器已启动');
        setupDTUEventHandlers();
    }).catch(error => {
        console.error('❌ DTU管理器启动失败:', error.message);
        console.log('💡 将在语音识别模式下运行，不支持DTU设备通信');
    });
} else {
    console.log('📱 DTU设备管理未启用');
}

// 存储WebSocket连接
const webSocketClients = new Set();

// 打印任务队列管理
class PrintTaskQueue {
    constructor() {
        this.queue = [];
        this.currentTask = null;
        this.isProcessing = false;
        this.maxQueueSize = configManager.get('printing.task_management.max_queue_size') || 10;
        this.taskTimeout = configManager.get('printing.task_management.timeout_ms') || 30000;
        this.statistics = {
            totalTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            averageTime: 0
        };
    }

    // 添加打印任务
    addTask(task) {
        if (this.queue.length >= this.maxQueueSize) {
            throw new Error(`打印队列已满，最大容量: ${this.maxQueueSize}`);
        }

        const printTask = {
            id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: task.type || 'text_print',
            content: task.content,
            priority: task.priority || 'normal',
            config: task.config || {},
            status: 'queued',
            createdAt: new Date().toISOString(),
            clientId: task.clientId,
            retryCount: 0,
            maxRetries: 3
        };

        // 根据优先级插入队列
        if (printTask.priority === 'high') {
            this.queue.unshift(printTask);
        } else {
            this.queue.push(printTask);
        }

        this.statistics.totalTasks++;
        console.log(`[打印队列] 任务已添加: ${printTask.id}, 队列长度: ${this.queue.length}`);
        return printTask;
    }

    // 获取队列状态
    getStatus() {
        return {
            queueLength: this.queue.length,
            currentTask: this.currentTask ? {
                id: this.currentTask.id,
                type: this.currentTask.type,
                status: this.currentTask.status,
                progress: this.currentTask.progress || 0
            } : null,
            isProcessing: this.isProcessing,
            statistics: this.statistics
        };
    }

    // 取消任务
    cancelTask(taskId) {
        const index = this.queue.findIndex(task => task.id === taskId);
        if (index !== -1) {
            const task = this.queue.splice(index, 1)[0];
            console.log(`[打印队列] 任务已取消: ${taskId}`);
            return task;
        }
        return null;
    }

    // 清空队列
    clearQueue() {
        const canceledCount = this.queue.length;
        this.queue = [];
        console.log(`[打印队列] 队列已清空，取消了 ${canceledCount} 个任务`);
        return canceledCount;
    }
}

// 全局打印任务队列实例
const printQueue = new PrintTaskQueue();



// 设置DTU事件处理器
function setupDTUEventHandlers() {
    if (!dtuManager) return;

    // DTU连接成功事件
    dtuManager.on('connected', (data) => {
        console.log('✅ DTU设备连接成功:', data);

        // 广播DTU连接状态到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'dtu_connected',
            deviceId: data.deviceId,
            status: data.status,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU数据发送成功事件
    dtuManager.on('dataSent', (data) => {
        console.log('📤 DTU数据发送成功:', data);

        // 通知前端数据发送成功
        const message = JSON.stringify({
            type: 'dtu_data_sent',
            deviceId: data.deviceId,
            data: data.data,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU状态更新事件
    dtuManager.on('statusUpdate', (status) => {
        console.log('📊 DTU设备状态更新:', status);

        // 广播设备状态到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'dtu_status_update',
            ...status,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU连接丢失事件
    dtuManager.on('connectionLost', (data) => {
        console.log('⚠️ DTU连接丢失:', data);

        // 通知前端连接丢失
        const message = JSON.stringify({
            type: 'dtu_connection_lost',
            retryCount: data.retryCount,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU错误事件
    dtuManager.on('error', (error) => {
        console.error('❌ DTU错误:', error);

        // 通知前端错误信息
        const message = JSON.stringify({
            type: 'dtu_error',
            error: error.message,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });
}

const server = http.createServer((req, res) => {
    // 设置 CORS �?
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            status: 'ok',
            service: 'voice-backend',
            voiceApi: config.xunfei.enabled ? 'xunfei' : 'demo',
            iotEnabled: config.iot.enabled,
            iotConnected: iotManager ? iotManager.isConnected : false,
            deviceCount: iotManager ? iotManager.getDevices().length : 0,
            timestamp: new Date().toISOString()
        }));
    } else if (req.url === '/api/voice-config' && req.method === 'GET') {
        // 返回语音配置信息的API
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            provider: config.xunfei.enabled ? 'xunfei' : 'demo',
            status: config.xunfei.enabled ? 'active' : 'demo_mode',
            message: config.xunfei.enabled ? '讯飞语音识别已启�?' : '演示模式（需配置讯飞API启用真实识别�?'
        }));
    } else if (req.url === '/api/devices' && req.method === 'GET') {
        // 新增：获取设备列表API
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        const devices = iotManager ? iotManager.getDevices() : [];
        res.end(JSON.stringify({
            devices,
            count: devices.length,
            iotEnabled: config.iot.enabled,
            timestamp: new Date().toISOString()
        }));
    } else if (req.url.startsWith('/api/device/') && req.method === 'POST') {
        // 新增：向设备发送指令API
        const pathParts = req.url.split('/');
        const deviceId = pathParts[3];
        const action = pathParts[4]; // �? 'command'

        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', async () => {
            try {
                const data = JSON.parse(body);

                if (iotManager && action === 'command') {
                    await iotManager.sendCommandToDevice(deviceId, data);
                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: true,
                        message: '指令发送成�?',
                        deviceId,
                        command: data
                    }));
                } else {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ error: 'IoT管理器未启用或无效操�?' }));
                }
            } catch (error) {
                res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({ error: error.message }));
            }
        });
    } else if (req.url === '/api/translate' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const result = {
                    original_text: data.text,
                    translated_text: `[ģ�ⷭ�뵽${data.target_lang}] ${data.text}`,
                    source_lang: data.source_lang,
                    target_lang: data.target_lang,
                    confidence: 0.95,
                    provider: 'demo'
                };
                res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify(result));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
        });
    } else if (req.url === '/api/send/text' && req.method === 'POST') {
        // �������������ֵ�CH32V307��API
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', async () => {
            try {
                const data = JSON.parse(body);
                const { text, device_id } = data;

                if (!text || text.trim().length === 0) {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: false,
                        error: '�������ݲ���Ϊ��'
                    }));
                    return;
                }

                console.log(`? ��������: ${text}`);

                // ����������Ϣ
                const textMessage = {
                    type: 'text_message',
                    text: text,
                    timestamp: new Date().toISOString(),
                    sender: 'web_interface'
                };

                if (iotManager) {
                    try {
                        // ȷ��Ŀ���豸
                        const targetDeviceId = device_id || 'ch32v307_audio_01';

                        // ���͵�ָ���豸�����������豸
                        const targetDevices = device_id
                            ? [device_id]
                            : iotManager.getDevices()
                                .filter(device => iotManager.isDeviceOnline(device.id))
                                .map(device => device.id);

                        if (targetDevices.length === 0) {
                            targetDevices.push('ch32v307_audio_01'); // Ĭ���豸
                        }

                        for (const deviceId of targetDevices) {
                            await iotManager.sendCommandToDevice(deviceId, textMessage);
                            console.log(`? ���ַ��ͳɹ�: ${deviceId}`);
                        }

                        // ֪ͨ����WebSocket�ͻ���
                        const notificationMessage = JSON.stringify({
                            type: 'text_sent',
                            text: text,
                            target_devices: targetDevices,
                            timestamp: new Date().toISOString(),
                            success: true
                        });

                        webSocketClients.forEach(ws => {
                            if (ws.readyState === WebSocket.OPEN) {
                                ws.send(notificationMessage);
                            }
                        });

                        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            success: true,
                            message: '���ַ��ͳɹ�',
                            text: text,
                            target_devices: targetDevices,
                            timestamp: new Date().toISOString()
                        }));

                    } catch (error) {
                        console.error(`? ���ַ���ʧ��:`, error.message);
                        res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            success: false,
                            error: '����ʧ��: ' + error.message
                        }));
                    }
                } else {
                    // IoT������δ����ʱ����ʾģʽ
                    console.log(`? ��ʾģʽ - ��������: ${text}`);

                    // ֪ͨWebSocket�ͻ���
                    const demoMessage = JSON.stringify({
                        type: 'text_sent',
                        text: text,
                        target_devices: ['ch32v307_demo'],
                        timestamp: new Date().toISOString(),
                        success: true,
                        demo_mode: true
                    });

                    webSocketClients.forEach(ws => {
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(demoMessage);
                        }
                    });

                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: true,
                        message: '���ַ��ͳɹ� (��ʾģʽ)',
                        text: text,
                        target_devices: ['ch32v307_demo'],
                        demo_mode: true,
                        timestamp: new Date().toISOString()
                    }));
                }

            } catch (error) {
                console.error('�������ַ�������ʱ����:', error.message);
                res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({
                    success: false,
                    error: '�������ڲ�����: ' + error.message
                }));
            }
        });
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({ error: 'Not found' }));
    }
});

const wss = new WebSocket.Server({ noServer: true });

let clientCounter = 0;

// 语音识别处理函数
async function processVoiceRecognition(audioData, clientId) {
    console.log(`[${clientId}] 开始处理语音识�? - 数据大小: ${audioData.length} bytes`);

    let result;

    if (config.xunfei.enabled && voiceRecognizer) {
        try {
            console.log(`[${clientId}] 使用讯飞API进行语音识别...`);

            // 验证音频数据
            if (audioData.length < 500) {
                console.log(`[${clientId}] 音频数据太小，可能无效`);
                result = {
                    type: 'voice_result',
                    recognized_text: '录音时间太短，请重新录制',
                    confidence: 0.1,
                    timestamp: new Date().toISOString(),
                    provider: 'xunfei',
                    status: 'warning'
                };
            } else {
                const recognitionResult = await voiceRecognizer.recognizeAudioSimple(audioData);

                // 确保中文字符正确编码
                const recognizedText = recognitionResult.text;
                console.log(`[${clientId}]识别结果: `, recognizedText);

                // 检查是否包含中文字符，确保正确编码
                const processedText = Buffer.from(recognizedText, 'utf8').toString('utf8');

                result = {
                    type: 'voice_result',
                    recognized_text: processedText,
                    confidence: recognitionResult.confidence || 0.8,
                    timestamp: new Date().toISOString(),
                    provider: 'xunfei',
                    status: recognitionResult.isError ? 'error' : 'success'
                };
            }
        } catch (error) {
            console.error(`[${clientId}]讯飞API调用失败: `, error.message);
            result = {
                type: 'voice_result',
                recognized_text: `[API调用异常] 讯飞语音识别: ${error.message}`,
                confidence: 0.1,
                timestamp: new Date().toISOString(),
                provider: 'xunfei',
                status: 'error'
            };
        }
    } else {
        // 演示模式
        console.log(`[${clientId}]使用演示模式...`);
        const demoTexts = [
            '打开客厅�?',
            '关闭空调',
            '查询温度',
            '设置温度�?25�?',
            '打开窗帘',
            '播放音乐'
        ];

        const randomText = demoTexts[Math.floor(Math.random() * demoTexts.length)];

        result = {
            type: 'voice_result',
            recognized_text: `${randomText}`,
            confidence: 0.95,
            timestamp: new Date().toISOString(),
            provider: 'demo',
            status: 'success'
        };
    }



    // 自动转发语音识别结果到DTU设备
    if (dtuManager && config.dtu.forwarding.autoForwardVoice &&
        result.confidence >= config.dtu.forwarding.confidenceThreshold &&
        result.status === 'success') {

        try {
            console.log(`🚀 转发语音识别结果到DTU设备: ${config.dtu.api.deviceId}`);

            const voiceData = {
                text: result.recognized_text,
                confidence: result.confidence,
                provider: result.provider,
                timestamp: result.timestamp
            };

            await dtuManager.sendVoiceResult(voiceData);
            console.log(`✅ 语音指令已发送到DTU设备: ${config.dtu.api.deviceId}`);

            // 添加DTU转发信息到结果中
            result.forwarded_to_dtu = {
                deviceId: config.dtu.api.deviceId,
                success: true,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ DTU语音结果转发失败:', error.message);
            result.forwarded_to_dtu = {
                deviceId: config.dtu.api.deviceId,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    return result;
}

wss.on('connection', (ws, req) => {
    clientCounter++;
    const clientId = `voice_client_${clientCounter}`;
    console.log(`[${new Date().toLocaleTimeString()}] 客户端连�?: ${clientId}`);

    // 添加到WebSocket客户端集�?
    webSocketClients.add(ws);

    // 发送配置信息给客户�?
    ws.send(JSON.stringify({
        type: 'config_info',
        voiceProvider: config.xunfei.enabled ? 'xunfei' : 'demo',
        dtuEnabled: config.dtu.enabled,
        dtuConnected: dtuManager ? dtuManager.isConnected : false,
        dtuDeviceId: config.dtu.api.deviceId,
        message: config.xunfei.enabled ? '讯飞语音识别已就�?' : '演示模式已就�?'
    }));

    ws.on('message', async (message) => {
        try {
            // 区分二进制音频数据和JSON命令
            if (Buffer.isBuffer(message)) {
                // 二进制音频数�?
                console.log(`[${clientId}] 收到音频数据: ${message.length} bytes`);

                // 使用新的语音识别处理函数
                const result = await processVoiceRecognition(message, clientId);

                // 确保消息正确编码后发�?
                const jsonString = JSON.stringify(result);
                ws.send(jsonString);

            } else {
                // JSON文本命令
                const messageText = message.toString('utf8');
                console.log(`[${clientId}] 收到消息: ${messageText}`);

                try {
                    const data = JSON.parse(messageText);

                    if (data.type === 'heartbeat') {
                        ws.send(JSON.stringify({ type: 'heartbeat_ack' }));
                    } else if (data.type === 'get_config') {
                        // 客户端请求配置信�?
                        ws.send(JSON.stringify({
                            type: 'config_info',
                            voiceProvider: config.xunfei.enabled ? 'xunfei' : 'demo',
                            dtuEnabled: config.dtu.enabled,
                            dtuConnected: dtuManager ? dtuManager.isConnected : false,
                            dtuDeviceId: config.dtu.api.deviceId,
                            message: config.xunfei.enabled ? '讯飞语音识别已启�?' : '演示模式（需配置讯飞API�?'
                        }));

                    } else if (data.type === 'get_dtu_status') {
                        // 客户端请求DTU状态
                        if (dtuManager) {
                            try {
                                const status = await dtuManager.getDeviceStatus();
                                const connectionStatus = dtuManager.getConnectionStatus();
                                ws.send(JSON.stringify({
                                    type: 'dtu_status',
                                    deviceStatus: status,
                                    connectionStatus: connectionStatus,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_status_error',
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_status_error',
                                error: 'DTU管理器未启用',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'send_dtu_command') {
                        // 客户端发送DTU命令
                        if (dtuManager && data.command) {
                            try {
                                const result = await dtuManager.sendControlCommand(data.command, data.params || {});
                                ws.send(JSON.stringify({
                                    type: 'dtu_command_result',
                                    success: true,
                                    command: data.command,
                                    result: result,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_command_result',
                                    success: false,
                                    command: data.command,
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_command_result',
                                success: false,
                                error: 'DTU管理器未启用或命令参数缺失',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'send_dtu_text') {
                        // 客户端发送文字到DTU设备
                        if (dtuManager && data.text) {
                            try {
                                const result = await dtuManager.sendToDevice({
                                    type: 'text_message',
                                    text: data.text,
                                    source: 'websocket_client'
                                });
                                ws.send(JSON.stringify({
                                    type: 'dtu_text_result',
                                    success: true,
                                    text: data.text,
                                    result: result,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_text_result',
                                    success: false,
                                    text: data.text,
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_text_result',
                                success: false,
                                error: 'DTU管理器未启用或文字参数缺失',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'translate_req') {
                        const translatedText = `[翻译�?${data.lang}] ${data.text}`;
                        ws.send(JSON.stringify({
                            type: 'translation_result',
                            text: translatedText,
                            lang: data.lang,
                            timestamp: new Date().toISOString(),
                            provider: 'demo'
                        }));
                    } else if (data.type === 'print_command') {
                        // 打印命令处理
                        await handlePrintCommand(ws, data, clientId);
                    } else if (data.type === 'print_status_request') {
                        // 打印状态请求
                        await handlePrintStatusRequest(ws, data, clientId);
                    } else if (data.type === 'print_queue_management') {
                        // 打印队列管理
                        await handlePrintQueueManagement(ws, data, clientId);
                    } else if (data.type === 'print_config_update') {
                        // 打印配置更新
                        await handlePrintConfigUpdate(ws, data, clientId);
                    }
                } catch (parseError) {
                    // 如果不是JSON，当作普通文本消息处�?
                    console.log(`[${clientId}] 收到文本: ${messageText}`);
                }
            }
        } catch (error) {
            console.error(`处理消息时出�?: ${error.message}`);
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息处理失败',
                detail: error.message
            }));
        }
    });

    ws.on('close', () => {
        console.log(`[${new Date().toLocaleTimeString()}] 客户端断开连接: ${clientId}`);
        webSocketClients.delete(ws);
    });

    ws.on('error', (error) => {
        console.error(`[${clientId}] WebSocket错误: ${error.message}`);
        webSocketClients.delete(ws);
    });
});

server.on('upgrade', (request, socket, head) => {
    const pathname = request.url;
    if (pathname === '/voice-ws') {
        wss.handleUpgrade(request, socket, head, (ws) => {
            wss.emit('connection', ws, request);
        });
    } else {
        socket.destroy();
    }
});

// ==================== 打印控制处理函数 ====================

/**
 * 处理打印命令
 */
async function handlePrintCommand(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到打印命令:`, data.command);

        switch (data.command) {
            case 'start_print':
                await handleStartPrint(ws, data, clientId);
                break;
            case 'pause_print':
                await handlePausePrint(ws, data, clientId);
                break;
            case 'resume_print':
                await handleResumePrint(ws, data, clientId);
                break;
            case 'cancel_print':
                await handleCancelPrint(ws, data, clientId);
                break;
            case 'emergency_stop':
                await handleEmergencyStop(ws, data, clientId);
                break;
            default:
                throw new Error(`未知的打印命令: ${data.command}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 打印命令处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_command_result',
            success: false,
            command: data.command,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理开始打印命令
 */
async function handleStartPrint(ws, data, clientId) {
    try {
        // 验证打印内容
        if (!data.content || !data.content.text) {
            throw new Error('打印内容不能为空');
        }

        // 创建打印任务
        const task = printQueue.addTask({
            type: 'text_print',
            content: data.content,
            priority: data.priority || 'normal',
            config: data.config || {},
            clientId: clientId
        });

        // 发送任务创建确认
        ws.send(JSON.stringify({
            type: 'print_command_result',
            success: true,
            command: 'start_print',
            taskId: task.id,
            queuePosition: printQueue.queue.length,
            estimatedWaitTime: printQueue.queue.length * 30, // 估算等待时间(秒)
            timestamp: new Date().toISOString()
        }));

        // 如果DTU可用，发送打印指令到设备
        if (dtuManager && dtuManager.isConnected) {
            const printCommand = {
                type: 'print_text',
                text: data.content.text,
                config: {
                    mode: data.config.mode || 'standard',
                    quality: data.config.quality || 'normal',
                    speed: data.config.speed || 'standard'
                },
                taskId: task.id
            };

            try {
                const result = await dtuManager.sendToDevice(printCommand);
                console.log(`[${clientId}] 打印指令已发送到DTU设备:`, result);

                // 更新任务状态
                task.status = 'printing';
                task.dtuResult = result;

                // 广播任务状态更新
                broadcastPrintStatus(task);

            } catch (dtuError) {
                console.error(`[${clientId}] DTU设备通信失败:`, dtuError.message);
                task.status = 'failed';
                task.error = dtuError.message;

                ws.send(JSON.stringify({
                    type: 'print_error',
                    taskId: task.id,
                    error: 'DTU设备通信失败: ' + dtuError.message,
                    timestamp: new Date().toISOString()
                }));
            }
        } else {
            // DTU不可用时的处理
            ws.send(JSON.stringify({
                type: 'print_warning',
                taskId: task.id,
                warning: 'DTU设备未连接，任务已加入队列等待设备恢复',
                timestamp: new Date().toISOString()
            }));
        }

    } catch (error) {
        throw error;
    }
}

/**
 * 处理暂停打印命令
 */
async function handlePausePrint(ws, data, clientId) {
    try {
        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendControlCommand('pause_print', { taskId: data.taskId });

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'pause_print',
                taskId: data.taskId,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            throw new Error('DTU设备未连接');
        }
    } catch (error) {
        throw error;
    }
}

/**
 * 处理恢复打印命令
 */
async function handleResumePrint(ws, data, clientId) {
    try {
        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendControlCommand('resume_print', { taskId: data.taskId });

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'resume_print',
                taskId: data.taskId,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            throw new Error('DTU设备未连接');
        }
    } catch (error) {
        throw error;
    }
}

/**
 * 处理取消打印命令
 */
async function handleCancelPrint(ws, data, clientId) {
    try {
        // 从队列中取消任务
        const canceledTask = printQueue.cancelTask(data.taskId);

        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendControlCommand('cancel_print', { taskId: data.taskId });

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'cancel_print',
                taskId: data.taskId,
                canceledFromQueue: !!canceledTask,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'cancel_print',
                taskId: data.taskId,
                canceledFromQueue: !!canceledTask,
                warning: 'DTU设备未连接，仅从队列中取消',
                timestamp: new Date().toISOString()
            }));
        }
    } catch (error) {
        throw error;
    }
}

/**
 * 处理紧急停止命令
 */
async function handleEmergencyStop(ws, data, clientId) {
    try {
        // 清空打印队列
        const canceledCount = printQueue.clearQueue();

        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendControlCommand('emergency_stop', {});

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'emergency_stop',
                canceledTasks: canceledCount,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'emergency_stop',
                canceledTasks: canceledCount,
                warning: 'DTU设备未连接，仅清空了本地队列',
                timestamp: new Date().toISOString()
            }));
        }

        // 广播紧急停止状态
        broadcastToAllClients({
            type: 'emergency_stop_broadcast',
            canceledTasks: canceledCount,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        throw error;
    }
}

/**
 * 处理打印状态请求
 */
async function handlePrintStatusRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到打印状态请求:`, data.request);

        switch (data.request) {
            case 'queue_status':
                const queueStatus = printQueue.getStatus();
                ws.send(JSON.stringify({
                    type: 'print_status_response',
                    request: 'queue_status',
                    data: queueStatus,
                    timestamp: new Date().toISOString()
                }));
                break;

            case 'device_status':
                if (dtuManager && dtuManager.isConnected) {
                    try {
                        const deviceStatus = await dtuManager.getDeviceStatus();
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'device_status',
                            data: deviceStatus,
                            timestamp: new Date().toISOString()
                        }));
                    } catch (error) {
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'device_status',
                            error: error.message,
                            timestamp: new Date().toISOString()
                        }));
                    }
                } else {
                    ws.send(JSON.stringify({
                        type: 'print_status_response',
                        request: 'device_status',
                        error: 'DTU设备未连接',
                        timestamp: new Date().toISOString()
                    }));
                }
                break;

            case 'task_status':
                if (data.taskId) {
                    const task = printQueue.queue.find(t => t.id === data.taskId) ||
                        printQueue.currentTask;
                    if (task && task.id === data.taskId) {
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'task_status',
                            data: {
                                id: task.id,
                                status: task.status,
                                progress: task.progress || 0,
                                createdAt: task.createdAt,
                                error: task.error
                            },
                            timestamp: new Date().toISOString()
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'task_status',
                            error: `任务不存在: ${data.taskId}`,
                            timestamp: new Date().toISOString()
                        }));
                    }
                } else {
                    ws.send(JSON.stringify({
                        type: 'print_status_response',
                        request: 'task_status',
                        error: '缺少任务ID参数',
                        timestamp: new Date().toISOString()
                    }));
                }
                break;

            default:
                throw new Error(`未知的状态请求: ${data.request}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 打印状态请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_status_response',
            request: data.request,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理打印队列管理
 */
async function handlePrintQueueManagement(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到队列管理请求:`, data.action);

        switch (data.action) {
            case 'get_queue':
                const queueStatus = printQueue.getStatus();
                const queueDetails = {
                    ...queueStatus,
                    tasks: printQueue.queue.map(task => ({
                        id: task.id,
                        type: task.type,
                        status: task.status,
                        priority: task.priority,
                        createdAt: task.createdAt,
                        content: task.content.text ? task.content.text.substring(0, 50) + '...' : 'N/A'
                    }))
                };

                ws.send(JSON.stringify({
                    type: 'print_queue_response',
                    action: 'get_queue',
                    data: queueDetails,
                    timestamp: new Date().toISOString()
                }));
                break;

            case 'clear_queue':
                const canceledCount = printQueue.clearQueue();
                ws.send(JSON.stringify({
                    type: 'print_queue_response',
                    action: 'clear_queue',
                    canceledTasks: canceledCount,
                    timestamp: new Date().toISOString()
                }));

                // 广播队列清空事件
                broadcastToAllClients({
                    type: 'queue_cleared',
                    canceledTasks: canceledCount,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'reorder_queue':
                if (data.taskOrder && Array.isArray(data.taskOrder)) {
                    // 重新排序队列
                    const newQueue = [];
                    data.taskOrder.forEach(taskId => {
                        const task = printQueue.queue.find(t => t.id === taskId);
                        if (task) {
                            newQueue.push(task);
                        }
                    });

                    // 添加未在排序列表中的任务
                    printQueue.queue.forEach(task => {
                        if (!newQueue.find(t => t.id === task.id)) {
                            newQueue.push(task);
                        }
                    });

                    printQueue.queue = newQueue;

                    ws.send(JSON.stringify({
                        type: 'print_queue_response',
                        action: 'reorder_queue',
                        success: true,
                        newOrder: printQueue.queue.map(t => t.id),
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('无效的任务排序参数');
                }
                break;

            case 'set_priority':
                if (data.taskId && data.priority) {
                    const task = printQueue.queue.find(t => t.id === data.taskId);
                    if (task) {
                        task.priority = data.priority;

                        // 如果设置为高优先级，移到队列前面
                        if (data.priority === 'high') {
                            const index = printQueue.queue.indexOf(task);
                            printQueue.queue.splice(index, 1);
                            printQueue.queue.unshift(task);
                        }

                        ws.send(JSON.stringify({
                            type: 'print_queue_response',
                            action: 'set_priority',
                            taskId: data.taskId,
                            priority: data.priority,
                            success: true,
                            timestamp: new Date().toISOString()
                        }));
                    } else {
                        throw new Error(`任务不存在: ${data.taskId}`);
                    }
                } else {
                    throw new Error('缺少任务ID或优先级参数');
                }
                break;

            default:
                throw new Error(`未知的队列管理操作: ${data.action}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 队列管理请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_queue_response',
            action: data.action,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理打印配置更新
 */
async function handlePrintConfigUpdate(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到配置更新请求:`, data.configType);

        switch (data.configType) {
            case 'speed_level':
                if (data.level && data.config) {
                    configManager.setPrintSpeedLevel(data.level, data.config);

                    ws.send(JSON.stringify({
                        type: 'print_config_response',
                        configType: 'speed_level',
                        level: data.level,
                        success: true,
                        message: `速度等级 ${data.level} 配置已更新`,
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('缺少速度等级或配置参数');
                }
                break;

            case 'print_mode':
                if (data.mode && data.config) {
                    configManager.setPrintMode(data.mode, data.config);

                    ws.send(JSON.stringify({
                        type: 'print_config_response',
                        configType: 'print_mode',
                        mode: data.mode,
                        success: true,
                        message: `打印模式 ${data.mode} 配置已更新`,
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('缺少打印模式或配置参数');
                }
                break;

            case 'precision_settings':
                if (data.category && data.settings) {
                    configManager.setPrintPrecisionSettings(data.category, data.settings);

                    ws.send(JSON.stringify({
                        type: 'print_config_response',
                        configType: 'precision_settings',
                        category: data.category,
                        success: true,
                        message: `精度参数 ${data.category} 配置已更新`,
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('缺少精度参数类别或设置');
                }
                break;

            case 'task_management':
                if (data.config) {
                    configManager.setPrintTaskManagement(data.config);

                    // 更新队列配置
                    printQueue.maxQueueSize = data.config.max_queue_size || printQueue.maxQueueSize;
                    printQueue.taskTimeout = data.config.timeout_ms || printQueue.taskTimeout;

                    ws.send(JSON.stringify({
                        type: 'print_config_response',
                        configType: 'task_management',
                        success: true,
                        message: '任务管理配置已更新',
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('缺少任务管理配置参数');
                }
                break;

            case 'get_config':
                const currentConfig = configManager.getPrintingConfig();
                ws.send(JSON.stringify({
                    type: 'print_config_response',
                    configType: 'get_config',
                    data: currentConfig,
                    timestamp: new Date().toISOString()
                }));
                break;

            default:
                throw new Error(`未知的配置类型: ${data.configType}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 配置更新请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_config_response',
            configType: data.configType,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

const PORT = 8081;
server.listen(PORT, '0.0.0.0', () => {
    console.log('🎉 语音识别后端服务器已启动!');
    console.log(`🔗 WebSocket: ws://0.0.0.0:${PORT}/voice-ws`);
    console.log(`📡 API接口: http://0.0.0.0:${PORT}/api/translate`);
    console.log(`❤️  健康检�?: http://0.0.0.0:${PORT}/health`);
    console.log(`⚙️  配置信息: http://0.0.0.0:${PORT}/api/voice-config`);
    console.log(`🌐 设备管理: http://0.0.0.0:${PORT}/api/devices`);
    console.log(`👥 当前连接�?: ${wss.clients.size}`);

    if (config.xunfei.enabled) {
        console.log('🌟 讯飞语音识别已启用，支持真实语音识别');
    } else {
        console.log('💡 提示：要启用真实语音识别，请编辑 config.js 文件配置讯飞API');
    }

    console.log('🚀 DTU设备管理已启用，支持自动转发语音指令到设备');
}); 