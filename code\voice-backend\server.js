const WebSocket = require('ws');
const http = require('http');
const config = require('./config');
const XunfeiVoiceRecognition = require('./xunfei-voice-config');
const DTUIntegration = require('./dtu-integration');
const configManager = require('./config-manager');
const PrintPerformanceMonitor = require('./print-performance-monitor');
const PrintErrorHandler = require('./print-error-handler');

// 设置字符编码
process.stdout.setEncoding('utf8');
if (process.platform === 'win32') {
    // Windows 平台设置控制台编�?
    try {
        const { spawn } = require('child_process');
        spawn('chcp', ['65001'], { stdio: 'ignore' });
    } catch (error) {
        console.log('无法设置控制台编码，继续运行...');
    }
}

// 初始化讯飞语音识�?
let voiceRecognizer = null;
if (config.xunfei.enabled) {
    voiceRecognizer = new XunfeiVoiceRecognition(config.xunfei);
    console.log('🔊 讯飞语音识别已启�?');
} else {
    console.log('🎭 演示模式已启用（要使用真实语音识别，请配置讯飞API�?');
}



// 初始化性能监控器
const performanceMonitor = new PrintPerformanceMonitor({
    dataRetentionDays: 30,
    sampleInterval: 1000,
    reportInterval: 300000, // 5分钟
    dataPath: './data/performance'
});

// 初始化错误处理器
const errorHandler = new PrintErrorHandler({
    maxRetryAttempts: 3,
    retryDelay: 1000,
    logPath: './data/error-logs',
    enableAutoRecovery: true,
    alertThresholds: {
        errorRate: 10, // 10% 错误率告警
        consecutiveErrors: 5 // 连续5次错误告警
    }
});

// 初始化银尔达DTU管理器
let dtuManager = null;
if (config.dtu.enabled) {
    dtuManager = new DTUIntegration(config.dtu.api);

    // 初始化DTU管理器
    dtuManager.initialize().then(() => {
        console.log('🌐 银尔达DTU管理器已启动');

        // 启动性能监控
        performanceMonitor.startMonitoring();
        console.log('📊 打印性能监控已启动');

        // 设置性能监控事件监听器
        setupPerformanceEventHandlers();

        setupDTUEventHandlers();
    }).catch(error => {
        console.error('❌ DTU管理器启动失败:', error.message);
        console.log('💡 将在语音识别模式下运行，不支持DTU设备通信');
    });
} else {
    console.log('📱 DTU设备管理未启用');
}

// 存储WebSocket连接
const webSocketClients = new Set();

// 打印任务队列管理
class PrintTaskQueue {
    constructor() {
        this.queue = [];
        this.currentTask = null;
        this.isProcessing = false;

        // 安全获取配置，如果配置未加载则使用默认值
        try {
            this.maxQueueSize = configManager.get('printing.task_management.max_queue_size') || 10;
            this.taskTimeout = configManager.get('printing.task_management.timeout_ms') || 30000;
        } catch (error) {
            console.log('⚠️  配置未加载，使用默认打印队列配置');
            this.maxQueueSize = 10;
            this.taskTimeout = 30000;
        }
        this.statistics = {
            totalTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            averageTime: 0
        };
    }

    // 添加打印任务
    addTask(task) {
        if (this.queue.length >= this.maxQueueSize) {
            throw new Error(`打印队列已满，最大容量: ${this.maxQueueSize}`);
        }

        const printTask = {
            id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: task.type || 'text_print',
            content: task.content,
            priority: task.priority || 'normal',
            config: task.config || {},
            status: 'queued',
            createdAt: new Date().toISOString(),
            clientId: task.clientId,
            retryCount: 0,
            maxRetries: 3
        };

        // 根据优先级插入队列
        if (printTask.priority === 'high') {
            this.queue.unshift(printTask);
        } else {
            this.queue.push(printTask);
        }

        this.statistics.totalTasks++;
        console.log(`[打印队列] 任务已添加: ${printTask.id}, 队列长度: ${this.queue.length}`);
        return printTask;
    }

    // 获取队列状态
    getStatus() {
        return {
            queueLength: this.queue.length,
            currentTask: this.currentTask ? {
                id: this.currentTask.id,
                type: this.currentTask.type,
                status: this.currentTask.status,
                progress: this.currentTask.progress || 0
            } : null,
            isProcessing: this.isProcessing,
            statistics: this.statistics
        };
    }

    // 取消任务
    cancelTask(taskId) {
        const index = this.queue.findIndex(task => task.id === taskId);
        if (index !== -1) {
            const task = this.queue.splice(index, 1)[0];
            console.log(`[打印队列] 任务已取消: ${taskId}`);
            return task;
        }
        return null;
    }

    // 清空队列
    clearQueue() {
        const canceledCount = this.queue.length;
        this.queue = [];
        console.log(`[打印队列] 队列已清空，取消了 ${canceledCount} 个任务`);
        return canceledCount;
    }
}

// 全局打印任务队列实例 - 延迟初始化
let printQueue = null;



/**
 * 设置性能监控事件处理器
 */
function setupPerformanceEventHandlers() {
    if (!performanceMonitor) return;

    // 监听数据更新事件
    performanceMonitor.on('data_updated', (realtimeData) => {
        // 广播实时性能数据到所有客户端
        broadcastToAllClients({
            type: 'performance_realtime_update',
            data: realtimeData,
            timestamp: new Date().toISOString()
        });
    });

    // 监听告警事件
    performanceMonitor.on('alert_created', (alert) => {
        console.log(`🚨 性能告警: ${alert.message}`);

        // 广播告警到所有客户端
        broadcastToAllClients({
            type: 'performance_alert',
            alert: alert,
            timestamp: new Date().toISOString()
        });
    });

    // 监听报告生成事件
    performanceMonitor.on('report_generated', (report) => {
        console.log(`📊 性能报告已生成: ${report.id}`);

        // 广播报告生成通知
        broadcastToAllClients({
            type: 'performance_report_generated',
            reportId: report.id,
            timeRange: report.timeRange,
            summary: {
                taskStats: report.taskStats,
                performance: report.performance,
                alertCount: report.alertStats.total,
                recommendationCount: report.recommendations.length
            },
            timestamp: new Date().toISOString()
        });
    });

    // 监听任务事件
    performanceMonitor.on('task_started', (task) => {
        console.log(`📝 性能监控: 任务开始 ${task.id}`);
    });

    performanceMonitor.on('task_completed', (task) => {
        console.log(`✅ 性能监控: 任务完成 ${task.id} (${task.status})`);
    });

    console.log('📊 性能监控事件处理器已设置');
}

// 设置错误处理器事件监听
function setupErrorHandlerEvents() {
    if (!errorHandler) return;

    // 错误事件监听
    errorHandler.on('error', (errorInfo) => {
        console.log(`🚨 错误处理: ${errorInfo.type} (${errorInfo.id})`);

        // 广播错误信息到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'print_error_detected',
            error: {
                id: errorInfo.id,
                code: errorInfo.code,
                type: errorInfo.type,
                level: errorInfo.level,
                message: errorInfo.message,
                component: errorInfo.context.component,
                timestamp: errorInfo.timestamp
            }
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // 告警事件监听
    errorHandler.on('alert', (alertInfo) => {
        console.log(`⚠️ 错误告警: ${alertInfo.type} - ${alertInfo.message}`);

        // 广播告警信息到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'print_error_alert',
            alert: alertInfo
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // 恢复事件监听
    errorHandler.on('recovery', (recoveryInfo) => {
        console.log(`🔧 错误恢复: ${recoveryInfo.strategy} - ${recoveryInfo.success ? '成功' : '失败'}`);

        // 广播恢复信息到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'print_error_recovery',
            recovery: recoveryInfo
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // 手动干预事件监听
    errorHandler.on('manualIntervention', (interventionInfo) => {
        console.log(`👤 需要手动干预: ${interventionInfo.errorInfo.type}`);

        // 广播手动干预请求到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'print_manual_intervention_required',
            intervention: {
                errorId: interventionInfo.errorInfo.id,
                errorType: interventionInfo.errorInfo.type,
                urgency: interventionInfo.urgency,
                instructions: interventionInfo.instructions,
                timestamp: new Date().toISOString()
            }
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // 重试事件监听
    errorHandler.on('retry', (retryInfo) => {
        console.log(`🔄 错误重试: ${retryInfo.errorInfo.id} (${retryInfo.retryCount}/${retryInfo.maxAttempts})`);

        // 根据错误类型执行相应的重试逻辑
        const errorCode = retryInfo.errorInfo.code;
        const context = retryInfo.errorInfo.context;

        if (errorCode >= 2000 && errorCode < 3000) {
            // 通信错误重试
            handleCommunicationRetry(retryInfo);
        } else if (errorCode >= 3000 && errorCode < 4000) {
            // 应用错误重试
            handleApplicationRetry(retryInfo);
        }
    });

    // 重置事件监听
    errorHandler.on('reset', (resetInfo) => {
        console.log(`🔄 执行重置: ${resetInfo.component} - ${resetInfo.resetType}`);

        // 根据组件类型执行重置
        handleComponentReset(resetInfo);
    });

    // 降级事件监听
    errorHandler.on('fallback', (fallbackInfo) => {
        console.log(`⬇️ 执行降级: ${fallbackInfo.fallbackMode}`);

        // 执行降级策略
        handleFallbackMode(fallbackInfo);
    });

    console.log('🛡️ 错误处理事件监听器已设置');
}

// 处理通信错误重试
function handleCommunicationRetry(retryInfo) {
    const errorCode = retryInfo.errorInfo.code;
    const context = retryInfo.errorInfo.context;

    switch (errorCode) {
        case 2001: // DTU连接丢失
            if (dtuManager) {
                console.log('🔄 重试DTU连接...');
                dtuManager.initialize();
            }
            break;

        case 2002: // WebSocket连接断开
            console.log('🔄 重试WebSocket连接...');
            // WebSocket会自动重连，这里只记录日志
            break;

        case 2003: // UART通信失败
            console.log('🔄 重试UART通信...');
            // 重新发送数据到设备
            if (context.taskId && context.data) {
                setTimeout(() => {
                    retryPrintTask(context.taskId, context.data);
                }, 2000);
            }
            break;

        default:
            console.log(`⚠️ 未处理的通信错误重试: ${errorCode}`);
    }
}

// 处理应用错误重试
function handleApplicationRetry(retryInfo) {
    const errorCode = retryInfo.errorInfo.code;
    const context = retryInfo.errorInfo.context;

    switch (errorCode) {
        case 3003: // 任务超时
            if (context.taskId) {
                console.log(`🔄 重试超时任务: ${context.taskId}`);
                // 重新启动任务
                retryPrintTask(context.taskId);
            }
            break;

        case 3002: // 队列溢出
            console.log('🔄 清理打印队列...');
            // 清理部分队列项目
            if (printQueue && printQueue.queue.length > 5) {
                printQueue.queue.splice(5); // 保留前5个任务
            }
            break;

        default:
            console.log(`⚠️ 未处理的应用错误重试: ${errorCode}`);
    }
}

// 处理组件重置
function handleComponentReset(resetInfo) {
    const component = resetInfo.component;

    switch (component) {
        case 'dtu':
            console.log('🔄 重置DTU组件...');
            if (dtuManager) {
                dtuManager.stopHeartbeat();
                setTimeout(() => {
                    dtuManager.initialize();
                }, 3000);
            }
            break;

        case 'print_queue':
            console.log('🔄 重置打印队列...');
            if (printQueue) {
                printQueue.currentTask = null;
                printQueue.isProcessing = false;
                // 保留队列但重置状态
            }
            break;

        case 'performance_monitor':
            console.log('🔄 重置性能监控...');
            if (performanceMonitor) {
                performanceMonitor.resetStats();
            }
            break;

        default:
            console.log(`⚠️ 未知组件重置请求: ${component}`);
    }
}

// 处理降级模式
function handleFallbackMode(fallbackInfo) {
    const fallbackMode = fallbackInfo.fallbackMode;

    switch (fallbackMode) {
        case 'safe_mode':
            console.log('⬇️ 进入安全模式...');
            // 禁用高级功能，只保留基本打印功能
            if (performanceMonitor) {
                performanceMonitor.setSafeMode(true);
            }
            break;

        case 'offline_mode':
            console.log('⬇️ 进入离线模式...');
            // 断开DTU连接，使用本地队列
            if (dtuManager) {
                dtuManager.stopHeartbeat();
            }
            break;

        case 'minimal_mode':
            console.log('⬇️ 进入最小功能模式...');
            // 只保留最基本的功能
            break;

        default:
            console.log(`⚠️ 未知降级模式: ${fallbackMode}`);
    }
}

// 重试打印任务
function retryPrintTask(taskId, data = null) {
    if (printQueue) {
        const task = printQueue.queue.find(t => t.id === taskId) || printQueue.currentTask;
        if (task) {
            console.log(`🔄 重试打印任务: ${taskId}`);
            task.retryCount = (task.retryCount || 0) + 1;
            task.status = 'retry';

            // 重新处理任务
            setTimeout(() => {
                printQueue.processQueue();
            }, 1000);
        }
    }
}

// 设置DTU事件处理器
function setupDTUEventHandlers() {
    if (!dtuManager) return;

    // DTU连接成功事件
    dtuManager.on('connected', (data) => {
        console.log('✅ DTU设备连接成功:', data);

        // 广播DTU连接状态到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'dtu_connected',
            deviceId: data.deviceId,
            status: data.status,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU数据发送成功事件
    dtuManager.on('dataSent', (data) => {
        console.log('📤 DTU数据发送成功:', data);

        // 通知前端数据发送成功
        const message = JSON.stringify({
            type: 'dtu_data_sent',
            deviceId: data.deviceId,
            data: data.data,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU状态更新事件
    dtuManager.on('statusUpdate', (status) => {
        console.log('📊 DTU设备状态更新:', status);

        // 广播设备状态到所有WebSocket客户端
        const message = JSON.stringify({
            type: 'dtu_status_update',
            ...status,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU连接丢失事件
    dtuManager.on('connectionLost', (data) => {
        console.log('⚠️ DTU连接丢失:', data);

        // 通知前端连接丢失
        const message = JSON.stringify({
            type: 'dtu_connection_lost',
            retryCount: data.retryCount,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });

    // DTU错误事件
    dtuManager.on('error', async (error) => {
        console.error('❌ DTU错误:', error);

        // 使用错误处理器处理DTU错误
        if (errorHandler) {
            await errorHandler.handleError({
                code: 2001, // DTU连接丢失
                message: error.message
            }, {
                component: 'dtu',
                operation: 'connection',
                deviceId: dtuManager.deviceId || 'unknown'
            });
        }

        // 通知前端错误信息
        const message = JSON.stringify({
            type: 'dtu_error',
            error: error.message,
            timestamp: new Date().toISOString()
        });

        webSocketClients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(message);
            }
        });
    });
}

const server = http.createServer((req, res) => {
    // 设置 CORS �?
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            status: 'ok',
            service: 'voice-backend',
            voiceApi: config.xunfei.enabled ? 'xunfei' : 'demo',
            iotEnabled: config.iot.enabled,
            iotConnected: iotManager ? iotManager.isConnected : false,
            deviceCount: iotManager ? iotManager.getDevices().length : 0,
            timestamp: new Date().toISOString()
        }));
    } else if (req.url === '/api/voice-config' && req.method === 'GET') {
        // 返回语音配置信息的API
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            provider: config.xunfei.enabled ? 'xunfei' : 'demo',
            status: config.xunfei.enabled ? 'active' : 'demo_mode',
            message: config.xunfei.enabled ? '讯飞语音识别已启�?' : '演示模式（需配置讯飞API启用真实识别�?'
        }));
    } else if (req.url === '/api/devices' && req.method === 'GET') {
        // 新增：获取设备列表API
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        const devices = iotManager ? iotManager.getDevices() : [];
        res.end(JSON.stringify({
            devices,
            count: devices.length,
            iotEnabled: config.iot.enabled,
            timestamp: new Date().toISOString()
        }));
    } else if (req.url.startsWith('/api/device/') && req.method === 'POST') {
        // 新增：向设备发送指令API
        const pathParts = req.url.split('/');
        const deviceId = pathParts[3];
        const action = pathParts[4]; // �? 'command'

        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', async () => {
            try {
                const data = JSON.parse(body);

                if (iotManager && action === 'command') {
                    await iotManager.sendCommandToDevice(deviceId, data);
                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: true,
                        message: '指令发送成�?',
                        deviceId,
                        command: data
                    }));
                } else {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ error: 'IoT管理器未启用或无效操�?' }));
                }
            } catch (error) {
                res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({ error: error.message }));
            }
        });
    } else if (req.url === '/api/translate' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const result = {
                    original_text: data.text,
                    translated_text: `[ģ�ⷭ�뵽${data.target_lang}] ${data.text}`,
                    source_lang: data.source_lang,
                    target_lang: data.target_lang,
                    confidence: 0.95,
                    provider: 'demo'
                };
                res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify(result));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
        });
    } else if (req.url === '/api/send/text' && req.method === 'POST') {
        // �������������ֵ�CH32V307��API
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', async () => {
            try {
                const data = JSON.parse(body);
                const { text, device_id } = data;

                if (!text || text.trim().length === 0) {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: false,
                        error: '�������ݲ���Ϊ��'
                    }));
                    return;
                }

                console.log(`? ��������: ${text}`);

                // ����������Ϣ
                const textMessage = {
                    type: 'text_message',
                    text: text,
                    timestamp: new Date().toISOString(),
                    sender: 'web_interface'
                };

                if (iotManager) {
                    try {
                        // ȷ��Ŀ���豸
                        const targetDeviceId = device_id || 'ch32v307_audio_01';

                        // ���͵�ָ���豸�����������豸
                        const targetDevices = device_id
                            ? [device_id]
                            : iotManager.getDevices()
                                .filter(device => iotManager.isDeviceOnline(device.id))
                                .map(device => device.id);

                        if (targetDevices.length === 0) {
                            targetDevices.push('ch32v307_audio_01'); // Ĭ���豸
                        }

                        for (const deviceId of targetDevices) {
                            await iotManager.sendCommandToDevice(deviceId, textMessage);
                            console.log(`? ���ַ��ͳɹ�: ${deviceId}`);
                        }

                        // ֪ͨ����WebSocket�ͻ���
                        const notificationMessage = JSON.stringify({
                            type: 'text_sent',
                            text: text,
                            target_devices: targetDevices,
                            timestamp: new Date().toISOString(),
                            success: true
                        });

                        webSocketClients.forEach(ws => {
                            if (ws.readyState === WebSocket.OPEN) {
                                ws.send(notificationMessage);
                            }
                        });

                        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            success: true,
                            message: '���ַ��ͳɹ�',
                            text: text,
                            target_devices: targetDevices,
                            timestamp: new Date().toISOString()
                        }));

                    } catch (error) {
                        console.error(`? ���ַ���ʧ��:`, error.message);
                        res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            success: false,
                            error: '����ʧ��: ' + error.message
                        }));
                    }
                } else {
                    // IoT������δ����ʱ����ʾģʽ
                    console.log(`? ��ʾģʽ - ��������: ${text}`);

                    // ֪ͨWebSocket�ͻ���
                    const demoMessage = JSON.stringify({
                        type: 'text_sent',
                        text: text,
                        target_devices: ['ch32v307_demo'],
                        timestamp: new Date().toISOString(),
                        success: true,
                        demo_mode: true
                    });

                    webSocketClients.forEach(ws => {
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(demoMessage);
                        }
                    });

                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        success: true,
                        message: '���ַ��ͳɹ� (��ʾģʽ)',
                        text: text,
                        target_devices: ['ch32v307_demo'],
                        demo_mode: true,
                        timestamp: new Date().toISOString()
                    }));
                }

            } catch (error) {
                console.error('�������ַ�������ʱ����:', error.message);
                res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                res.end(JSON.stringify({
                    success: false,
                    error: '�������ڲ�����: ' + error.message
                }));
            }
        });
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({ error: 'Not found' }));
    }
});

const wss = new WebSocket.Server({ noServer: true });

let clientCounter = 0;

// 语音识别处理函数
async function processVoiceRecognition(audioData, clientId) {
    console.log(`[${clientId}] 开始处理语音识�? - 数据大小: ${audioData.length} bytes`);

    let result;

    if (config.xunfei.enabled && voiceRecognizer) {
        try {
            console.log(`[${clientId}] 使用讯飞API进行语音识别...`);

            // 验证音频数据
            if (audioData.length < 500) {
                console.log(`[${clientId}] 音频数据太小，可能无效`);
                result = {
                    type: 'voice_result',
                    recognized_text: '录音时间太短，请重新录制',
                    confidence: 0.1,
                    timestamp: new Date().toISOString(),
                    provider: 'xunfei',
                    status: 'warning'
                };
            } else {
                const recognitionResult = await voiceRecognizer.recognizeAudioSimple(audioData);

                // 确保中文字符正确编码
                const recognizedText = recognitionResult.text;
                console.log(`[${clientId}]识别结果: `, recognizedText);

                // 检查是否包含中文字符，确保正确编码
                const processedText = Buffer.from(recognizedText, 'utf8').toString('utf8');

                result = {
                    type: 'voice_result',
                    recognized_text: processedText,
                    confidence: recognitionResult.confidence || 0.8,
                    timestamp: new Date().toISOString(),
                    provider: 'xunfei',
                    status: recognitionResult.isError ? 'error' : 'success'
                };
            }
        } catch (error) {
            console.error(`[${clientId}]讯飞API调用失败: `, error.message);
            result = {
                type: 'voice_result',
                recognized_text: `[API调用异常] 讯飞语音识别: ${error.message}`,
                confidence: 0.1,
                timestamp: new Date().toISOString(),
                provider: 'xunfei',
                status: 'error'
            };
        }
    } else {
        // 演示模式
        console.log(`[${clientId}]使用演示模式...`);
        const demoTexts = [
            '打开客厅�?',
            '关闭空调',
            '查询温度',
            '设置温度�?25�?',
            '打开窗帘',
            '播放音乐'
        ];

        const randomText = demoTexts[Math.floor(Math.random() * demoTexts.length)];

        result = {
            type: 'voice_result',
            recognized_text: `${randomText}`,
            confidence: 0.95,
            timestamp: new Date().toISOString(),
            provider: 'demo',
            status: 'success'
        };
    }



    // 自动转发语音识别结果到DTU设备
    if (dtuManager && config.dtu.forwarding.autoForwardVoice &&
        result.confidence >= config.dtu.forwarding.confidenceThreshold &&
        result.status === 'success') {

        try {
            console.log(`🚀 转发语音识别结果到DTU设备: ${config.dtu.api.deviceId}`);

            const voiceData = {
                text: result.recognized_text,
                confidence: result.confidence,
                provider: result.provider,
                timestamp: result.timestamp
            };

            await dtuManager.sendVoiceResult(voiceData);
            console.log(`✅ 语音指令已发送到DTU设备: ${config.dtu.api.deviceId}`);

            // 添加DTU转发信息到结果中
            result.forwarded_to_dtu = {
                deviceId: config.dtu.api.deviceId,
                success: true,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ DTU语音结果转发失败:', error.message);
            result.forwarded_to_dtu = {
                deviceId: config.dtu.api.deviceId,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    return result;
}

wss.on('connection', (ws, req) => {
    clientCounter++;
    const clientId = `voice_client_${clientCounter}`;
    console.log(`[${new Date().toLocaleTimeString()}] 客户端连�?: ${clientId}`);

    // 添加到WebSocket客户端集�?
    webSocketClients.add(ws);

    // 发送配置信息给客户�?
    ws.send(JSON.stringify({
        type: 'config_info',
        voiceProvider: config.xunfei.enabled ? 'xunfei' : 'demo',
        dtuEnabled: config.dtu.enabled,
        dtuConnected: dtuManager ? dtuManager.isConnected : false,
        dtuDeviceId: config.dtu.api.deviceId,
        message: config.xunfei.enabled ? '讯飞语音识别已就�?' : '演示模式已就�?'
    }));

    ws.on('message', async (message) => {
        try {
            // 首先尝试将消息解析为JSON文本命令
            let messageText;
            let isJsonMessage = false;

            try {
                messageText = message.toString('utf8');
                // 尝试解析JSON，如果成功说明是文本命令
                const testData = JSON.parse(messageText);
                isJsonMessage = true;
                console.log(`[${clientId}] 收到JSON消息: ${messageText}`);
            } catch (parseError) {
                // 解析失败，当作二进制音频数据处理
                isJsonMessage = false;
            }

            if (isJsonMessage) {
                // JSON文本命令
                try {
                    const data = JSON.parse(messageText);

                    if (data.type === 'heartbeat') {
                        ws.send(JSON.stringify({ type: 'heartbeat_ack' }));
                    } else if (data.type === 'get_config') {
                        // 客户端请求配置信�?
                        ws.send(JSON.stringify({
                            type: 'config_info',
                            voiceProvider: config.xunfei.enabled ? 'xunfei' : 'demo',
                            dtuEnabled: config.dtu.enabled,
                            dtuConnected: dtuManager ? dtuManager.isConnected : false,
                            dtuDeviceId: config.dtu.api.deviceId,
                            message: config.xunfei.enabled ? '讯飞语音识别已启�?' : '演示模式（需配置讯飞API�?'
                        }));

                    } else if (data.type === 'get_dtu_status') {
                        // 客户端请求DTU状态
                        if (dtuManager) {
                            try {
                                const status = await dtuManager.getDeviceStatus();
                                const connectionStatus = dtuManager.getConnectionStatus();
                                ws.send(JSON.stringify({
                                    type: 'dtu_status',
                                    deviceStatus: status,
                                    connectionStatus: connectionStatus,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_status_error',
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_status_error',
                                error: 'DTU管理器未启用',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'send_dtu_command') {
                        // 客户端发送DTU命令
                        if (dtuManager && data.command) {
                            try {
                                const result = await dtuManager.sendControlCommand(data.command, data.params || {});
                                ws.send(JSON.stringify({
                                    type: 'dtu_command_result',
                                    success: true,
                                    command: data.command,
                                    result: result,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_command_result',
                                    success: false,
                                    command: data.command,
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_command_result',
                                success: false,
                                error: 'DTU管理器未启用或命令参数缺失',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'send_dtu_text') {
                        // 客户端发送文字到DTU设备
                        if (dtuManager && data.text) {
                            try {
                                const result = await dtuManager.sendToDevice({
                                    type: 'text_message',
                                    text: data.text,
                                    source: 'websocket_client'
                                });
                                ws.send(JSON.stringify({
                                    type: 'dtu_text_result',
                                    success: true,
                                    text: data.text,
                                    result: result,
                                    timestamp: new Date().toISOString()
                                }));
                            } catch (error) {
                                ws.send(JSON.stringify({
                                    type: 'dtu_text_result',
                                    success: false,
                                    text: data.text,
                                    error: error.message,
                                    timestamp: new Date().toISOString()
                                }));
                            }
                        } else {
                            ws.send(JSON.stringify({
                                type: 'dtu_text_result',
                                success: false,
                                error: 'DTU管理器未启用或文字参数缺失',
                                timestamp: new Date().toISOString()
                            }));
                        }
                    } else if (data.type === 'translate_req') {
                        const translatedText = `[翻译�?${data.lang}] ${data.text}`;
                        ws.send(JSON.stringify({
                            type: 'translation_result',
                            text: translatedText,
                            lang: data.lang,
                            timestamp: new Date().toISOString(),
                            provider: 'demo'
                        }));
                    } else if (data.type === 'print_command') {
                        // 打印命令处理
                        await handlePrintCommand(ws, data, clientId);
                    } else if (data.type === 'print_status_request') {
                        // 打印状态请求
                        await handlePrintStatusRequest(ws, data, clientId);
                    } else if (data.type === 'print_queue_management') {
                        // 打印队列管理
                        await handlePrintQueueManagement(ws, data, clientId);
                    } else if (data.type === 'print_config_update') {
                        // 打印配置更新
                        await handlePrintConfigUpdate(ws, data, clientId);
                    } else if (data.type === 'performance_request') {
                        // 性能数据请求
                        await handlePerformanceRequest(ws, data, clientId);
                    } else if (data.type === 'performance_report_request') {
                        // 性能报告请求
                        await handlePerformanceReportRequest(ws, data, clientId);
                    } else if (data.type === 'error_stats_request') {
                        // 错误统计请求
                        await handleErrorStatsRequest(ws, data, clientId);
                    } else if (data.type === 'error_history_request') {
                        // 错误历史请求
                        await handleErrorHistoryRequest(ws, data, clientId);
                    } else if (data.type === 'error_analysis_request') {
                        // 错误分析报告请求
                        await handleErrorAnalysisRequest(ws, data, clientId);
                    } else if (data.type === 'error_resolve_request') {
                        // 手动解决错误请求
                        await handleErrorResolveRequest(ws, data, clientId);
                    } else if (data.type === 'error_retry_request') {
                        // 手动重试错误请求
                        await handleErrorRetryRequest(ws, data, clientId);
                    } else if (data.type === 'simulate_error') {
                        // 模拟错误（测试用）
                        await handleSimulateError(ws, data, clientId);
                    }
                } catch (parseError) {
                    // 如果不是JSON，当作普通文本消息处�?
                    console.log(`[${clientId}] 收到文本: ${messageText}`);
                }
            } else {
                // 二进制音频数据
                console.log(`[${clientId}] 收到音频数据: ${message.length} bytes`);

                // 使用新的语音识别处理函数
                const result = await processVoiceRecognition(message, clientId);

                // 确保消息正确编码后发送
                const jsonString = JSON.stringify(result);
                ws.send(jsonString);
            }
        } catch (error) {
            console.error(`处理消息时出错: ${error.message}`);
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息处理失败',
                detail: error.message
            }));
        }
    });

    ws.on('close', () => {
        console.log(`[${new Date().toLocaleTimeString()}] 客户端断开连接: ${clientId}`);
        webSocketClients.delete(ws);
    });

    ws.on('error', async (error) => {
        console.error(`[${clientId}] WebSocket错误: ${error.message}`);

        // 使用错误处理器处理WebSocket错误
        if (errorHandler) {
            await errorHandler.handleError({
                code: 2002, // WebSocket连接断开
                message: error.message
            }, {
                clientId: clientId,
                component: 'websocket',
                operation: 'connection',
                errorType: 'connection_error'
            });
        }

        webSocketClients.delete(ws);
    });
});

server.on('upgrade', (request, socket, head) => {
    const pathname = request.url;
    if (pathname === '/voice-ws') {
        wss.handleUpgrade(request, socket, head, (ws) => {
            wss.emit('connection', ws, request);
        });
    } else {
        socket.destroy();
    }
});

// ==================== 打印控制处理函数 ====================

/**
 * 处理打印命令
 */
async function handlePrintCommand(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到打印命令:`, data.command);

        switch (data.command) {
            case 'start_print':
                await handleStartPrint(ws, data, clientId);
                break;
            case 'pause_print':
                await handlePausePrint(ws, data, clientId);
                break;
            case 'resume_print':
                await handleResumePrint(ws, data, clientId);
                break;
            case 'cancel_print':
                await handleCancelPrint(ws, data, clientId);
                break;
            case 'emergency_stop':
                await handleEmergencyStop(ws, data, clientId);
                break;
            default:
                throw new Error(`未知的打印命令: ${data.command}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 打印命令处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_command_result',
            success: false,
            command: data.command,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理开始打印命令
 */
async function handleStartPrint(ws, data, clientId) {
    try {
        // 验证打印内容
        if (!data.content || !data.content.text) {
            throw new Error('打印内容不能为空');
        }

        // 创建打印任务
        const task = printQueue.addTask({
            type: 'text_print',
            content: data.content,
            priority: data.priority || 'normal',
            config: data.config || {},
            clientId: clientId
        });

        // 开始性能监控跟踪
        if (performanceMonitor) {
            performanceMonitor.startTask(task.id, {
                characterCount: data.content.text ? data.content.text.length : 0,
                mode: data.config?.mode || 'standard',
                priority: data.priority || 'normal',
                clientId: clientId,
                type: 'text_print'
            });
        }

        // 发送任务创建确认
        ws.send(JSON.stringify({
            type: 'print_command_result',
            success: true,
            command: 'start_print',
            taskId: task.id,
            queuePosition: printQueue.queue.length,
            estimatedWaitTime: printQueue.queue.length * 30, // 估算等待时间(秒)
            timestamp: new Date().toISOString()
        }));

        // 如果DTU可用，发送打印指令到设备
        if (dtuManager && dtuManager.isConnected) {
            try {
                const result = await dtuManager.sendPrintTask({
                    taskId: task.id,
                    text: data.content.text,
                    config: {
                        mode: data.config.mode || 'standard',
                        quality: data.config.quality || 'normal',
                        speed: data.config.speed || 'standard',
                        copies: data.config.copies || 1
                    }
                });

                console.log(`[${clientId}] 打印任务已发送到DTU设备:`, result);

                // 更新任务状态
                task.status = 'printing';
                task.dtuResult = result;
                task.startTime = new Date().toISOString();

                // 广播任务状态更新
                broadcastPrintStatus(task);

            } catch (dtuError) {
                console.error(`[${clientId}] DTU设备通信失败:`, dtuError.message);
                task.status = 'failed';
                task.error = dtuError.message;
                printQueue.statistics.failedTasks++;

                // 使用错误处理器处理DTU通信错误
                if (errorHandler) {
                    await errorHandler.handleError({
                        code: 2003, // UART通信失败
                        message: dtuError.message
                    }, {
                        taskId: task.id,
                        clientId: clientId,
                        component: 'dtu',
                        operation: 'print_command',
                        data: data.content
                    });
                }

                ws.send(JSON.stringify({
                    type: 'print_error',
                    taskId: task.id,
                    error: 'DTU设备通信失败: ' + dtuError.message,
                    timestamp: new Date().toISOString()
                }));
            }
        } else {
            // DTU不可用时的处理
            task.status = 'waiting';
            ws.send(JSON.stringify({
                type: 'print_warning',
                taskId: task.id,
                warning: 'DTU设备未连接，任务已加入队列等待设备恢复',
                timestamp: new Date().toISOString()
            }));
        }

    } catch (error) {
        throw error;
    }
}

/**
 * 处理暂停打印命令
 */
async function handlePausePrint(ws, data, clientId) {
    try {
        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendPrintControl('pause', data.taskId);

            // 更新任务状态
            const task = printQueue.queue.find(t => t.id === data.taskId) || printQueue.currentTask;
            if (task && task.id === data.taskId) {
                task.status = 'paused';
                broadcastPrintStatus(task);
            }

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'pause_print',
                taskId: data.taskId,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            throw new Error('DTU设备未连接');
        }
    } catch (error) {
        throw error;
    }
}

/**
 * 处理恢复打印命令
 */
async function handleResumePrint(ws, data, clientId) {
    try {
        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendPrintControl('resume', data.taskId);

            // 更新任务状态
            const task = printQueue.queue.find(t => t.id === data.taskId) || printQueue.currentTask;
            if (task && task.id === data.taskId) {
                task.status = 'printing';
                broadcastPrintStatus(task);
            }

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'resume_print',
                taskId: data.taskId,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            throw new Error('DTU设备未连接');
        }
    } catch (error) {
        throw error;
    }
}

/**
 * 处理取消打印命令
 */
async function handleCancelPrint(ws, data, clientId) {
    try {
        // 从队列中取消任务
        const canceledTask = printQueue.cancelTask(data.taskId);

        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendPrintControl('cancel', data.taskId);

            // 更新任务状态
            const task = printQueue.queue.find(t => t.id === data.taskId) || printQueue.currentTask;
            if (task && task.id === data.taskId) {
                task.status = 'cancelled';
                task.endTime = new Date().toISOString();
                broadcastPrintStatus(task);
            }

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'cancel_print',
                taskId: data.taskId,
                canceledFromQueue: !!canceledTask,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'cancel_print',
                taskId: data.taskId,
                canceledFromQueue: !!canceledTask,
                warning: 'DTU设备未连接，仅从队列中取消',
                timestamp: new Date().toISOString()
            }));
        }
    } catch (error) {
        throw error;
    }
}

/**
 * 处理紧急停止命令
 */
async function handleEmergencyStop(ws, data, clientId) {
    try {
        // 清空打印队列
        const canceledCount = printQueue.clearQueue();

        if (dtuManager && dtuManager.isConnected) {
            const result = await dtuManager.sendPrintControl('emergency_stop');

            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'emergency_stop',
                canceledTasks: canceledCount,
                result: result,
                timestamp: new Date().toISOString()
            }));
        } else {
            ws.send(JSON.stringify({
                type: 'print_command_result',
                success: true,
                command: 'emergency_stop',
                canceledTasks: canceledCount,
                warning: 'DTU设备未连接，仅清空了本地队列',
                timestamp: new Date().toISOString()
            }));
        }

        // 广播紧急停止状态
        broadcastToAllClients({
            type: 'emergency_stop_broadcast',
            canceledTasks: canceledCount,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        throw error;
    }
}

/**
 * 处理打印状态请求
 */
async function handlePrintStatusRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到打印状态请求:`, data.request);

        switch (data.request) {
            case 'queue_status':
                const queueStatus = printQueue.getStatus();
                ws.send(JSON.stringify({
                    type: 'print_status_response',
                    request: 'queue_status',
                    data: queueStatus,
                    timestamp: new Date().toISOString()
                }));
                break;

            case 'device_status':
                if (dtuManager && dtuManager.isConnected) {
                    try {
                        const deviceStatus = await dtuManager.getDeviceStatus();
                        const printStatus = await dtuManager.requestPrintStatus();

                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'device_status',
                            data: {
                                device: deviceStatus,
                                printing: printStatus,
                                connection: dtuManager.getConnectionStatus()
                            },
                            timestamp: new Date().toISOString()
                        }));
                    } catch (error) {
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'device_status',
                            error: error.message,
                            timestamp: new Date().toISOString()
                        }));
                    }
                } else {
                    ws.send(JSON.stringify({
                        type: 'print_status_response',
                        request: 'device_status',
                        error: 'DTU设备未连接',
                        timestamp: new Date().toISOString()
                    }));
                }
                break;

            case 'task_status':
                if (data.taskId) {
                    const task = printQueue.queue.find(t => t.id === data.taskId) ||
                        printQueue.currentTask;
                    if (task && task.id === data.taskId) {
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'task_status',
                            data: {
                                id: task.id,
                                status: task.status,
                                progress: task.progress || 0,
                                createdAt: task.createdAt,
                                error: task.error
                            },
                            timestamp: new Date().toISOString()
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'print_status_response',
                            request: 'task_status',
                            error: `任务不存在: ${data.taskId}`,
                            timestamp: new Date().toISOString()
                        }));
                    }
                } else {
                    ws.send(JSON.stringify({
                        type: 'print_status_response',
                        request: 'task_status',
                        error: '缺少任务ID参数',
                        timestamp: new Date().toISOString()
                    }));
                }
                break;

            default:
                throw new Error(`未知的状态请求: ${data.request}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 打印状态请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_status_response',
            request: data.request,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理打印队列管理
 */
async function handlePrintQueueManagement(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到队列管理请求:`, data.action);

        switch (data.action) {
            case 'get_queue':
                const queueStatus = printQueue.getStatus();
                const queueDetails = {
                    ...queueStatus,
                    tasks: printQueue.queue.map(task => ({
                        id: task.id,
                        type: task.type,
                        status: task.status,
                        priority: task.priority,
                        createdAt: task.createdAt,
                        content: task.content.text ? task.content.text.substring(0, 50) + '...' : 'N/A'
                    }))
                };

                ws.send(JSON.stringify({
                    type: 'print_queue_response',
                    action: 'get_queue',
                    data: queueDetails,
                    timestamp: new Date().toISOString()
                }));
                break;

            case 'clear_queue':
                const canceledCount = printQueue.clearQueue();
                ws.send(JSON.stringify({
                    type: 'print_queue_response',
                    action: 'clear_queue',
                    canceledTasks: canceledCount,
                    timestamp: new Date().toISOString()
                }));

                // 广播队列清空事件
                broadcastToAllClients({
                    type: 'queue_cleared',
                    canceledTasks: canceledCount,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'reorder_queue':
                if (data.taskOrder && Array.isArray(data.taskOrder)) {
                    // 重新排序队列
                    const newQueue = [];
                    data.taskOrder.forEach(taskId => {
                        const task = printQueue.queue.find(t => t.id === taskId);
                        if (task) {
                            newQueue.push(task);
                        }
                    });

                    // 添加未在排序列表中的任务
                    printQueue.queue.forEach(task => {
                        if (!newQueue.find(t => t.id === task.id)) {
                            newQueue.push(task);
                        }
                    });

                    printQueue.queue = newQueue;

                    ws.send(JSON.stringify({
                        type: 'print_queue_response',
                        action: 'reorder_queue',
                        success: true,
                        newOrder: printQueue.queue.map(t => t.id),
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('无效的任务排序参数');
                }
                break;

            case 'set_priority':
                if (data.taskId && data.priority) {
                    const task = printQueue.queue.find(t => t.id === data.taskId);
                    if (task) {
                        task.priority = data.priority;

                        // 如果设置为高优先级，移到队列前面
                        if (data.priority === 'high') {
                            const index = printQueue.queue.indexOf(task);
                            printQueue.queue.splice(index, 1);
                            printQueue.queue.unshift(task);
                        }

                        ws.send(JSON.stringify({
                            type: 'print_queue_response',
                            action: 'set_priority',
                            taskId: data.taskId,
                            priority: data.priority,
                            success: true,
                            timestamp: new Date().toISOString()
                        }));
                    } else {
                        throw new Error(`任务不存在: ${data.taskId}`);
                    }
                } else {
                    throw new Error('缺少任务ID或优先级参数');
                }
                break;

            default:
                throw new Error(`未知的队列管理操作: ${data.action}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 队列管理请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_queue_response',
            action: data.action,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}



/**
 * 处理打印队列管理
 */
async function handlePrintQueueManagement(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到队列管理请求:`, data.action);

        switch (data.action) {
            case 'get_queue':
                const queueStatus = printQueue.getStatus();
                const queueDetails = {
                    ...queueStatus,
                    tasks: printQueue.queue.map(task => ({
                        id: task.id,
                        type: task.type,
                        status: task.status,
                        priority: task.priority,
                        createdAt: task.createdAt,
                        content: task.content.text ? task.content.text.substring(0, 50) + '...' : 'N/A'
                    }))
                };

                ws.send(JSON.stringify({
                    type: 'print_queue_response',
                    action: 'get_queue',
                    data: queueDetails,
                    timestamp: new Date().toISOString()
                }));
                break;

            case 'clear_queue':
                const canceledCount = printQueue.clearQueue();
                ws.send(JSON.stringify({
                    type: 'print_queue_response',
                    action: 'clear_queue',
                    canceledTasks: canceledCount,
                    timestamp: new Date().toISOString()
                }));

                // 广播队列清空事件
                broadcastToAllClients({
                    type: 'queue_cleared',
                    canceledTasks: canceledCount,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'set_priority':
                if (data.taskId && data.priority) {
                    const task = printQueue.queue.find(t => t.id === data.taskId);
                    if (task) {
                        task.priority = data.priority;

                        // 如果设置为高优先级，移到队列前面
                        if (data.priority === 'high') {
                            const index = printQueue.queue.indexOf(task);
                            printQueue.queue.splice(index, 1);
                            printQueue.queue.unshift(task);
                        }

                        ws.send(JSON.stringify({
                            type: 'print_queue_response',
                            action: 'set_priority',
                            taskId: data.taskId,
                            priority: data.priority,
                            success: true,
                            timestamp: new Date().toISOString()
                        }));
                    } else {
                        throw new Error(`任务不存在: ${data.taskId}`);
                    }
                } else {
                    throw new Error('缺少任务ID或优先级参数');
                }
                break;

            default:
                throw new Error(`未知的队列管理操作: ${data.action}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 队列管理请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_queue_response',
            action: data.action,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理打印配置更新
 */
async function handlePrintConfigUpdate(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 收到配置更新请求:`, data.configType);

        switch (data.configType) {
            case 'speed_level':
                if (data.level && data.config) {
                    configManager.setPrintSpeedLevel(data.level, data.config);

                    // 同步配置到DTU设备
                    let dtuSynced = false;
                    if (dtuManager && dtuManager.isConnected) {
                        try {
                            await dtuManager.updatePrintConfig('speed_level', {
                                level: data.level,
                                config: data.config
                            });
                            dtuSynced = true;
                        } catch (dtuError) {
                            console.warn(`DTU配置同步失败: ${dtuError.message}`);
                        }
                    }

                    ws.send(JSON.stringify({
                        type: 'print_config_response',
                        configType: 'speed_level',
                        level: data.level,
                        success: true,
                        message: `速度等级 ${data.level} 配置已更新`,
                        dtuSynced: dtuSynced,
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    throw new Error('缺少速度等级或配置参数');
                }
                break;

            case 'get_config':
                const currentConfig = configManager.getPrintingConfig();
                ws.send(JSON.stringify({
                    type: 'print_config_response',
                    configType: 'get_config',
                    data: currentConfig,
                    timestamp: new Date().toISOString()
                }));
                break;

            default:
                throw new Error(`未知的配置类型: ${data.configType}`);
        }
    } catch (error) {
        console.error(`[${clientId}] 配置更新请求处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'print_config_response',
            configType: data.configType,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 广播消息到所有客户端
 */
function broadcastToAllClients(message) {
    const messageStr = JSON.stringify(message);
    webSocketClients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(messageStr);
        }
    });
}

/**
 * 广播打印状态更新
 */
function broadcastPrintStatus(task) {
    // 更新性能监控
    if (performanceMonitor && task) {
        if (task.status === 'printing' && task.progress !== undefined) {
            // 更新任务进度和速度
            const speed = task.currentSpeed || 0;
            performanceMonitor.updateTaskProgress(task.id, task.progress, speed);
        } else if (task.status === 'completed' || task.status === 'cancelled' || task.status === 'failed') {
            // 完成任务监控
            performanceMonitor.completeTask(task.id, {
                success: task.status === 'completed',
                error: task.error,
                finalProgress: task.progress || 0
            });
        }
    }

    broadcastToAllClients({
        type: 'print_status_update',
        taskId: task.id,
        status: task.status,
        progress: task.progress || 0,
        timestamp: new Date().toISOString()
    });
}

// 初始化打印队列（在配置加载后）
printQueue = new PrintTaskQueue();
console.log('📋 打印任务队列已初始化');

const PORT = 8081;
server.listen(PORT, '0.0.0.0', () => {
    console.log('🎉 语音识别后端服务器已启动!');
    console.log(`🔗 WebSocket: ws://0.0.0.0:${PORT}/voice-ws`);
    console.log(`📡 API接口: http://0.0.0.0:${PORT}/api/translate`);
    console.log(`❤️  健康检�?: http://0.0.0.0:${PORT}/health`);
    console.log(`⚙️  配置信息: http://0.0.0.0:${PORT}/api/voice-config`);
    console.log(`🌐 设备管理: http://0.0.0.0:${PORT}/api/devices`);
    console.log(`👥 当前连接�?: ${wss.clients.size}`);

    if (config.xunfei.enabled) {
        console.log('🌟 讯飞语音识别已启用，支持真实语音识别');
    } else {
        console.log('💡 提示：要启用真实语音识别，请编辑 config.js 文件配置讯飞API');
    }

    console.log('🚀 DTU设备管理已启用，支持自动转发语音指令到设备');

    // 初始化错误处理器事件监听
    setupErrorHandlerEvents();
    console.log('🛡️ 错误处理系统已启用，支持智能错误检测和恢复');
});

/**
 * 处理性能数据请求
 */
async function handlePerformanceRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理性能数据请求:`, data.request);

        let responseData = {};

        switch (data.request) {
            case 'summary':
                responseData = performanceMonitor.getPerformanceSummary();
                break;

            case 'historical':
                responseData = {
                    historical: performanceMonitor.getHistoricalData(
                        data.timeRange || '1h',
                        data.sampleCount || 100
                    )
                };
                break;

            case 'alerts':
                responseData = {
                    alerts: performanceMonitor.performanceData.alerts.slice(0, data.limit || 20)
                };
                break;

            case 'statistics':
                responseData = {
                    statistics: performanceMonitor.performanceData.statistics,
                    realtime: performanceMonitor.performanceData.realtime
                };
                break;

            default:
                responseData = performanceMonitor.getPerformanceSummary();
        }

        ws.send(JSON.stringify({
            type: 'performance_data',
            request: data.request,
            data: responseData,
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 性能数据请求失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'performance_data',
            request: data.request,
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理性能报告请求
 */
async function handlePerformanceReportRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理性能报告请求:`, data.timeRange);

        // 生成性能报告
        const report = await performanceMonitor.generatePerformanceReport(data.timeRange || '1h');

        ws.send(JSON.stringify({
            type: 'performance_report',
            report: report,
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 性能报告生成失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'performance_report',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理错误统计请求
 */
async function handleErrorStatsRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理错误统计请求`);

        if (!errorHandler) {
            throw new Error('错误处理器未初始化');
        }

        const stats = errorHandler.getErrorStats();

        ws.send(JSON.stringify({
            type: 'error_stats',
            success: true,
            data: stats,
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 错误统计获取失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'error_stats',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理错误历史请求
 */
async function handleErrorHistoryRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理错误历史请求:`, data.limit, data.level);

        if (!errorHandler) {
            throw new Error('错误处理器未初始化');
        }

        const history = errorHandler.getErrorHistory(data.limit || 50, data.level);

        ws.send(JSON.stringify({
            type: 'error_history',
            success: true,
            data: history,
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 错误历史获取失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'error_history',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理错误分析报告请求
 */
async function handleErrorAnalysisRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理错误分析请求:`, data.timeRange);

        if (!errorHandler) {
            throw new Error('错误处理器未初始化');
        }

        const analysis = errorHandler.getErrorAnalysisReport(data.timeRange || '24h');

        ws.send(JSON.stringify({
            type: 'error_analysis',
            success: true,
            data: analysis,
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 错误分析生成失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'error_analysis',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理手动解决错误请求
 */
async function handleErrorResolveRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理错误解决请求:`, data.errorId);

        if (!errorHandler) {
            throw new Error('错误处理器未初始化');
        }

        if (!data.errorId) {
            throw new Error('错误ID不能为空');
        }

        const success = errorHandler.markErrorResolved(data.errorId, data.resolution || '手动解决');

        ws.send(JSON.stringify({
            type: 'error_resolve_result',
            success: success,
            errorId: data.errorId,
            message: success ? '错误已标记为已解决' : '错误未找到',
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 错误解决处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'error_resolve_result',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理手动重试错误请求
 */
async function handleErrorRetryRequest(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理错误重试请求:`, data.errorId);

        if (!errorHandler) {
            throw new Error('错误处理器未初始化');
        }

        if (!data.errorId) {
            throw new Error('错误ID不能为空');
        }

        // 查找错误信息
        const errorInfo = errorHandler.errorHistory.find(err => err.id === data.errorId);
        if (!errorInfo) {
            throw new Error('错误信息未找到');
        }

        // 执行手动重试
        const retryResult = await errorHandler.executeRecoveryStrategy(errorInfo);

        ws.send(JSON.stringify({
            type: 'error_retry_result',
            success: retryResult,
            errorId: data.errorId,
            message: retryResult ? '重试执行成功' : '重试执行失败',
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 错误重试处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'error_retry_result',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

/**
 * 处理模拟错误请求（测试用）
 */
async function handleSimulateError(ws, data, clientId) {
    try {
        console.log(`[${clientId}] 处理模拟错误请求:`, data.error);

        if (!errorHandler) {
            throw new Error('错误处理器未初始化');
        }

        if (!data.error) {
            throw new Error('错误信息不能为空');
        }

        // 构造错误信息
        const errorInfo = {
            message: data.error.message || '模拟错误',
            code: data.error.code || 9999,
            level: data.error.level || 'error',
            type: data.error.type || 'SYSTEM'
        };

        const context = {
            taskId: data.error.context?.taskId || `test_${Date.now()}`,
            clientId: clientId,
            component: data.error.context?.component || 'test',
            operation: data.error.context?.operation || 'simulate',
            timestamp: new Date().toISOString()
        };

        // 触发错误处理
        await errorHandler.handleError(errorInfo, context);

        ws.send(JSON.stringify({
            type: 'simulate_error_result',
            success: true,
            message: '错误模拟成功',
            errorInfo: errorInfo,
            context: context,
            timestamp: new Date().toISOString()
        }));

    } catch (error) {
        console.error(`[${clientId}] 模拟错误处理失败:`, error.message);
        ws.send(JSON.stringify({
            type: 'simulate_error_result',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}