<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>&#26234;&#33021;&#35821;&#38899;&#35782;&#21035;&#31995;&#32479; - d-j-q.xyz</title>
    <link rel="icon"
        href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='0.9em' font-size='90'%3E?%3C/text%3E%3C/svg%3E">
    <style>
        :root {
            --primary-color: #6a11cb;
            --secondary-color: #2575fc;
            --background-color: #f0f2f5;
            --container-bg: #ffffff;
            --text-color: #333;
            --light-text-color: #f8f9fa;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --success-color: #28a745;
            --error-color: #dc3545;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--background-color);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            background: var(--container-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px var(--shadow-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 40px);
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--light-text-color);
            padding: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .main-content {
            display: flex;
            flex-grow: 1;
            overflow: hidden;
        }

        .left-panel,
        .right-panel {
            padding: 20px;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .left-panel {
            width: 35%;
            border-right: 1px solid var(--border-color);
        }

        .right-panel {
            width: 65%;
        }

        .control-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .control-section h2 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 5px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .input-group input,
        .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 1rem;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--light-text-color);
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            background: #a9a9a9;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status-light {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background-color: var(--error-color);
            display: inline-block;
            margin-right: 10px;
            transition: background-color 0.5s ease;
        }

        #status-text {
            vertical-align: middle;
        }

        .log-panel,
        .api-panel {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .log-box,
        .api-box {
            flex-grow: 1;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 15px;
            background-color: #fdfdfd;
            overflow-y: auto;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            color: #444;
        }

        .log-box p,
        .api-box p {
            margin-bottom: 5px;
            border-bottom: 1px dashed #eee;
            padding-bottom: 5px;
        }

        .log-box p:last-child,
        .api-box p:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #999;
            margin-left: 10px;
            font-size: 0.8em;
        }

        .log-type-connect {
            color: var(--success-color);
        }

        .log-type-disconnect {
            color: var(--error-color);
        }

        .log-type-info {
            color: var(--info-color);
        }

        .log-type-sent {
            color: #6f42c1;
        }

        .log-type-server {
            color: #007bff;
        }

        .log-type-error {
            color: var(--error-color);
            font-weight: bold;
        }

        /* DTU状态样式 */
        #dtu-device-id {
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        .dtu-status-online {
            color: var(--success-color);
            font-weight: bold;
        }

        .dtu-status-offline {
            color: var(--error-color);
            font-weight: bold;
        }

        .dtu-status-checking {
            color: var(--info-color);
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <header class="header">
            <h1>? &#26234;&#33021;&#35821;&#38899;&#35782;&#21035;&#31995;&#32479;</h1>
            <p>d-j-q.xyz</p>
        </header>

        <main class="main-content">
            <div class="left-panel">
                <div class="control-section">
                    <h2>&#25511;&#21046;&#38754;&#26495;</h2>
                    <div class="input-group">
                        <label for="server-url">&#26381;&#21153;&#22120;&#22320;&#22336;:</label>
                        <input type="text" id="server-url" value="ws://localhost:8081/voice-ws">
                    </div>
                    <div class="input-group">
                        <label for="status">
                            <span id="status-light" class="status-light"></span>
                            <span id="status-text">&#26410;&#36830;&#25509;</span>
                        </label>
                    </div>
                    <button id="connect-btn" class="btn">&#36830;&#25509;</button>
                </div>

                <div class="control-section">
                    <h2>&#35821;&#38899;&#35782;&#21035;</h2>
                    <div class="input-group">
                        <label for="source-lang">&#28304;&#35821;&#35328;:</label>
                        <select id="source-lang">
                            <option value="zh-CN">&#20013;&#25991; (&#26222;&#36890;&#35805;)</option>
                            <option value="en-US">English (US)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="target-lang">&#30446;&#26631;&#35821;&#35328;:</label>
                        <select id="target-lang">
                            <option value="en">English</option>
                            <option value="zh">&#20013;&#25991;</option>
                        </select>
                    </div>
                    <button id="record-btn" class="btn" disabled>?? &#24320;&#22987;&#24405;&#38899;</button>
                </div>

                <div class="control-section api-panel">
                    <h2>API&#27979;&#35797;</h2>
                    <div class="input-group">
                        <input type="text" id="api-text"
                            placeholder="&#36755;&#20837;&#35201;&#32763;&#35793;&#30340;&#25991;&#26412;">
                    </div>
                    <button id="api-btn" class="btn">? &#21457;&#36865;API&#35831;&#27714;</button>
                    <div id="api-log" class="api-box" style="margin-top: 15px;"></div>
                </div>

                <div class="control-section">
                    <h2>🌐 DTU设备控制</h2>
                    <div class="input-group">
                        <label for="dtu-status">
                            <span id="dtu-status-light" class="status-light"></span>
                            <span id="dtu-status-text">DTU状态检查中...</span>
                        </label>
                    </div>
                    <div class="input-group">
                        <label for="dtu-device-id">设备ID:</label>
                        <input type="text" id="dtu-device-id" value="869080075169294" readonly>
                    </div>
                    <button id="refresh-dtu-btn" class="btn">🔄 刷新DTU状态</button>
                </div>

                <div class="control-section">
                    <h2>📤 DTU文字传输</h2>
                    <div class="input-group">
                        <label for="dtu-text-input">发送到CH32V307:</label>
                        <input type="text" id="dtu-text-input" placeholder="输入要发送的文字..." maxlength="200">
                    </div>
                    <button id="send-dtu-text-btn" class="btn">📤 发送文字</button>
                    <div id="dtu-text-log" class="api-box" style="margin-top: 15px;">
                        <p>请输入文字并点击发送按钮测试DTU文字传输功能...</p>
                    </div>
                </div>

                <div class="control-section">
                    <h2>🖨️ 打印控制面板</h2>
                    <div class="input-group">
                        <label for="print-status">
                            <span id="print-status-light" class="status-light"></span>
                            <span id="print-status-text">打印系统就绪</span>
                        </label>
                    </div>
                    <div class="input-group">
                        <label for="print-text-input">打印内容:</label>
                        <textarea id="print-text-input" placeholder="输入要打印的文字..." rows="3"
                            style="width: 100%; padding: 10px; border: 1px solid var(--border-color); border-radius: 5px; font-size: 1rem; resize: vertical;"></textarea>
                    </div>
                    <div class="input-group">
                        <label for="print-mode">打印模式:</label>
                        <select id="print-mode">
                            <option value="standard">标准模式</option>
                            <option value="fast">快速模式</option>
                            <option value="high_quality">高质量模式</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="print-priority">优先级:</label>
                        <select id="print-priority">
                            <option value="normal">普通</option>
                            <option value="high">高优先级</option>
                        </select>
                    </div>
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="start-print-btn" class="btn" style="flex: 1;">🖨️ 开始打印</button>
                        <button id="pause-print-btn" class="btn" style="flex: 1; background: #ffc107;" disabled>⏸️
                            暂停</button>
                    </div>
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="resume-print-btn" class="btn" style="flex: 1; background: #28a745;" disabled>▶️
                            恢复</button>
                        <button id="cancel-print-btn" class="btn" style="flex: 1; background: #dc3545;" disabled>❌
                            取消</button>
                    </div>
                </div>

                <div class="control-section">
                    <h2>📊 打印队列管理</h2>
                    <div class="input-group">
                        <label>队列状态: <span id="queue-size">0</span> 个任务</label>
                        <div id="queue-progress"
                            style="width: 100%; height: 8px; background: #e9ecef; border-radius: 4px; margin-top: 5px;">
                            <div id="queue-progress-bar"
                                style="height: 100%; background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)); border-radius: 4px; width: 0%; transition: width 0.3s ease;">
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="refresh-queue-btn" class="btn" style="flex: 1; background: #17a2b8;">🔄
                            刷新队列</button>
                        <button id="clear-queue-btn" class="btn" style="flex: 1; background: #6c757d;">🗑️ 清空队列</button>
                    </div>
                    <div id="queue-list" class="api-box" style="margin-top: 15px; max-height: 150px;">
                        <p>队列为空</p>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <div class="control-section" style="margin-bottom: 20px;">
                    <h2>🖨️ 打印状态监控</h2>
                    <div class="input-group">
                        <label>当前任务: <span id="current-task-id">无</span></label>
                        <div id="print-progress"
                            style="width: 100%; height: 12px; background: #e9ecef; border-radius: 6px; margin-top: 5px;">
                            <div id="print-progress-bar"
                                style="height: 100%; background: linear-gradient(90deg, #28a745, #20c997); border-radius: 6px; width: 0%; transition: width 0.3s ease;">
                            </div>
                        </div>
                        <div
                            style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 0.9rem; color: #666;">
                            <span id="print-progress-text">0%</span>
                            <span id="print-speed">速度: 0 字符/秒</span>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                        <div>
                            <label style="font-size: 0.9rem; color: #666;">成功率:</label>
                            <div style="font-size: 1.2rem; font-weight: bold; color: var(--success-color);"
                                id="success-rate">100%</div>
                        </div>
                        <div>
                            <label style="font-size: 0.9rem; color: #666;">压缩比:</label>
                            <div style="font-size: 1.2rem; font-weight: bold; color: var(--info-color);"
                                id="compression-ratio">0%</div>
                        </div>
                        <div>
                            <label style="font-size: 0.9rem; color: #666;">已完成:</label>
                            <div style="font-size: 1.2rem; font-weight: bold; color: var(--primary-color);"
                                id="completed-tasks">0</div>
                        </div>
                        <div>
                            <label style="font-size: 0.9rem; color: #666;">失败数:</label>
                            <div style="font-size: 1.2rem; font-weight: bold; color: var(--error-color);"
                                id="failed-tasks">0</div>
                        </div>
                    </div>
                </div>

                <div class="control-section" style="margin-bottom: 20px;">
                    <h2>📄 打印预览</h2>
                    <div id="print-preview"
                        style="border: 1px solid var(--border-color); border-radius: 8px; padding: 15px; background: #f8f9fa; min-height: 120px; font-family: 'Courier New', monospace; font-size: 0.9rem; line-height: 1.4; white-space: pre-wrap; overflow-y: auto; max-height: 200px;">
                        <div style="color: #999; text-align: center; padding: 20px;">
                            输入打印内容后将在此显示预览...
                        </div>
                    </div>
                    <div
                        style="margin-top: 10px; display: flex; justify-content: space-between; font-size: 0.9rem; color: #666;">
                        <span>字符数: <span id="preview-char-count">0</span></span>
                        <span>预计时间: <span id="preview-time">0秒</span></span>
                    </div>
                </div>

                <div class="log-panel">
                    <h2>&#28040;&#24687;&#26085;&#24535;</h2>
                    <div id="log-box" class="log-box"></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // DOM Elements
        const connectBtn = document.getElementById('connect-btn');
        const recordBtn = document.getElementById('record-btn');
        const apiBtn = document.getElementById('api-btn');
        const serverUrlInput = document.getElementById('server-url');
        const statusLight = document.getElementById('status-light');
        const statusText = document.getElementById('status-text');
        const sourceLangSelect = document.getElementById('source-lang');
        const targetLangSelect = document.getElementById('target-lang');
        const logBox = document.getElementById('log-box');
        const apiLog = document.getElementById('api-log');
        const apiTextInput = document.getElementById('api-text');

        // DTU相关DOM元素
        const dtuStatusLight = document.getElementById('dtu-status-light');
        const dtuStatusText = document.getElementById('dtu-status-text');
        const dtuDeviceIdInput = document.getElementById('dtu-device-id');
        const refreshDtuBtn = document.getElementById('refresh-dtu-btn');
        const dtuTextInput = document.getElementById('dtu-text-input');
        const sendDtuTextBtn = document.getElementById('send-dtu-text-btn');
        const dtuTextLog = document.getElementById('dtu-text-log');

        // 打印控制相关DOM元素
        const printStatusLight = document.getElementById('print-status-light');
        const printStatusText = document.getElementById('print-status-text');
        const printTextInput = document.getElementById('print-text-input');
        const printModeSelect = document.getElementById('print-mode');
        const printPrioritySelect = document.getElementById('print-priority');
        const startPrintBtn = document.getElementById('start-print-btn');
        const pausePrintBtn = document.getElementById('pause-print-btn');
        const resumePrintBtn = document.getElementById('resume-print-btn');
        const cancelPrintBtn = document.getElementById('cancel-print-btn');
        const refreshQueueBtn = document.getElementById('refresh-queue-btn');
        const clearQueueBtn = document.getElementById('clear-queue-btn');
        const queueSizeSpan = document.getElementById('queue-size');
        const queueProgressBar = document.getElementById('queue-progress-bar');
        const queueList = document.getElementById('queue-list');

        // 打印监控相关DOM元素
        const currentTaskIdSpan = document.getElementById('current-task-id');
        const printProgressBar = document.getElementById('print-progress-bar');
        const printProgressText = document.getElementById('print-progress-text');
        const printSpeedSpan = document.getElementById('print-speed');
        const successRateSpan = document.getElementById('success-rate');
        const compressionRatioSpan = document.getElementById('compression-ratio');
        const completedTasksSpan = document.getElementById('completed-tasks');
        const failedTasksSpan = document.getElementById('failed-tasks');
        const printPreview = document.getElementById('print-preview');
        const previewCharCount = document.getElementById('preview-char-count');
        const previewTime = document.getElementById('preview-time');

        let ws = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;

        // DTU状态管理
        let dtuStatus = {
            connected: false,
            deviceId: '869080075169294',
            lastUpdate: null,
            connectionStatus: 'unknown'
        };

        // 打印状态管理
        let printStatus = {
            isReady: false,
            currentTask: null,
            queueSize: 0,
            totalTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            successRate: 100,
            compressionRatio: 0,
            printSpeed: 0,
            progress: 0,
            lastUpdate: null
        };

        // 打印队列管理
        let printQueue = [];

        // 打印配置
        let printConfig = {
            mode: 'standard',
            priority: 'normal',
            compressionEnabled: true,
            statusReportInterval: 5000
        };

        // Logging function
        function log(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            logBox.innerHTML += `<p class="log-type-${type}">${message}<span class="log-time">${time}</span></p>`;
            logBox.scrollTop = logBox.scrollHeight;
        }

        function logApi(message) {
            apiLog.innerHTML = `<p>${message}</p>`;
        }

        function logDtu(message) {
            const time = new Date().toLocaleTimeString();
            dtuTextLog.innerHTML += `<p>[${time}] ${message}</p>`;
            dtuTextLog.scrollTop = dtuTextLog.scrollHeight;
        }

        // 更新DTU状态显示
        function updateDtuStatus(connected, statusText, deviceInfo = null) {
            dtuStatus.connected = connected;
            dtuStatus.lastUpdate = new Date();

            if (connected) {
                dtuStatusLight.style.backgroundColor = 'var(--success-color)';
                dtuStatusText.innerHTML = `✅ ${statusText}`;
                sendDtuTextBtn.disabled = false;
            } else {
                dtuStatusLight.style.backgroundColor = 'var(--error-color)';
                dtuStatusText.innerHTML = `❌ ${statusText}`;
                // 允许在DTU未连接时发送文字（用于测试和错误处理演示）
                sendDtuTextBtn.disabled = false;
            }

            if (deviceInfo) {
                dtuStatus.connectionStatus = deviceInfo.status || 'unknown';
                log(`DTU设备信息更新: ${JSON.stringify(deviceInfo)}`, 'info');
            }
        }

        // 更新打印状态显示
        function updatePrintStatus(status, statusText = null) {
            printStatus.lastUpdate = new Date();

            if (status === 'ready') {
                printStatusLight.style.backgroundColor = 'var(--success-color)';
                printStatusText.innerHTML = statusText || '打印系统就绪';
                printStatus.isReady = true;
                startPrintBtn.disabled = false;
            } else if (status === 'printing') {
                printStatusLight.style.backgroundColor = 'var(--info-color)';
                printStatusText.innerHTML = statusText || '正在打印...';
                printStatus.isReady = false;
                startPrintBtn.disabled = true;
                pausePrintBtn.disabled = false;
                cancelPrintBtn.disabled = false;
            } else if (status === 'paused') {
                printStatusLight.style.backgroundColor = '#ffc107';
                printStatusText.innerHTML = statusText || '打印已暂停';
                pausePrintBtn.disabled = true;
                resumePrintBtn.disabled = false;
                cancelPrintBtn.disabled = false;
            } else if (status === 'error') {
                printStatusLight.style.backgroundColor = 'var(--error-color)';
                printStatusText.innerHTML = statusText || '打印错误';
                printStatus.isReady = false;
                resetPrintButtons();
            } else if (status === 'offline') {
                printStatusLight.style.backgroundColor = 'var(--error-color)';
                printStatusText.innerHTML = statusText || '打印系统离线';
                printStatus.isReady = false;
                startPrintBtn.disabled = true;
            }
        }

        // 重置打印按钮状态
        function resetPrintButtons() {
            pausePrintBtn.disabled = true;
            resumePrintBtn.disabled = true;
            cancelPrintBtn.disabled = true;
            if (printStatus.isReady) {
                startPrintBtn.disabled = false;
            }
        }

        // 更新打印进度
        function updatePrintProgress(progress, taskId = null, speed = 0) {
            printStatus.progress = progress;
            printStatus.printSpeed = speed;

            if (taskId) {
                printStatus.currentTask = taskId;
                currentTaskIdSpan.textContent = taskId;
            }

            printProgressBar.style.width = `${progress}%`;
            printProgressText.textContent = `${Math.round(progress)}%`;
            printSpeedSpan.textContent = `速度: ${speed} 字符/秒`;
        }

        // 更新打印统计
        function updatePrintStats(stats) {
            if (stats.completedTasks !== undefined) {
                printStatus.completedTasks = stats.completedTasks;
                completedTasksSpan.textContent = stats.completedTasks;
            }

            if (stats.failedTasks !== undefined) {
                printStatus.failedTasks = stats.failedTasks;
                failedTasksSpan.textContent = stats.failedTasks;
            }

            if (stats.successRate !== undefined) {
                printStatus.successRate = stats.successRate;
                successRateSpan.textContent = `${Math.round(stats.successRate)}%`;
            }

            if (stats.compressionRatio !== undefined) {
                printStatus.compressionRatio = stats.compressionRatio;
                compressionRatioSpan.textContent = `${Math.round(stats.compressionRatio * 100)}%`;
            }

            if (stats.queueSize !== undefined) {
                printStatus.queueSize = stats.queueSize;
                queueSizeSpan.textContent = stats.queueSize;

                // 更新队列进度条
                const maxQueue = 10; // 假设最大队列容量为10
                const queueProgress = Math.min((stats.queueSize / maxQueue) * 100, 100);
                queueProgressBar.style.width = `${queueProgress}%`;
            }
        }

        // 更新打印预览
        function updatePrintPreview() {
            const text = printTextInput.value.trim();

            if (!text) {
                printPreview.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">输入打印内容后将在此显示预览...</div>';
                previewCharCount.textContent = '0';
                previewTime.textContent = '0秒';
                return;
            }

            // 简单的盲文预览（实际应用中需要更复杂的转换）
            const braillePreview = text.split('').map(char => {
                // 简化的盲文字符映射示例
                const brailleMap = {
                    'a': '⠁', 'b': '⠃', 'c': '⠉', 'd': '⠙', 'e': '⠑',
                    'f': '⠋', 'g': '⠛', 'h': '⠓', 'i': '⠊', 'j': '⠚',
                    'k': '⠅', 'l': '⠇', 'm': '⠍', 'n': '⠝', 'o': '⠕',
                    'p': '⠏', 'q': '⠟', 'r': '⠗', 's': '⠎', 't': '⠞',
                    'u': '⠥', 'v': '⠧', 'w': '⠺', 'x': '⠭', 'y': '⠽', 'z': '⠵',
                    ' ': ' ', '\n': '\n'
                };

                return brailleMap[char.toLowerCase()] || char;
            }).join('');

            printPreview.innerHTML = `<div style="color: #333;">${braillePreview}</div>`;

            // 更新字符统计
            const charCount = text.length;
            previewCharCount.textContent = charCount;

            // 估算打印时间（假设每秒打印10个字符）
            const estimatedTime = Math.ceil(charCount / 10);
            previewTime.textContent = `${estimatedTime}秒`;
        }

        // 更新队列显示
        function updateQueueDisplay(queue = null) {
            if (queue) {
                printQueue = queue;
            }

            if (printQueue.length === 0) {
                queueList.innerHTML = '<p>队列为空</p>';
                return;
            }

            let queueHtml = '';
            printQueue.forEach((task, index) => {
                const statusIcon = {
                    'queued': '⏳',
                    'printing': '🖨️',
                    'completed': '✅',
                    'failed': '❌',
                    'paused': '⏸️'
                }[task.status] || '❓';

                const priorityColor = task.priority === 'high' ? '#dc3545' : '#6c757d';

                queueHtml += `
                    <div style="padding: 8px; border-bottom: 1px dashed #ddd; display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span style="margin-right: 8px;">${statusIcon}</span>
                            <span style="font-weight: bold;">${task.id}</span>
                            <div style="font-size: 0.8rem; color: #666; margin-top: 2px;">
                                ${task.type} | ${task.size || 0} 字符
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="color: ${priorityColor}; font-size: 0.8rem; font-weight: bold;">
                                ${task.priority === 'high' ? '高优先级' : '普通'}
                            </div>
                            <div style="font-size: 0.8rem; color: #999;">
                                ${new Date(task.created_time * 1000).toLocaleTimeString()}
                            </div>
                        </div>
                    </div>
                `;
            });

            queueList.innerHTML = queueHtml;
        }

        log('? &#27426;&#36814;&#20351;&#29992;&#26234;&#33021;&#35821;&#38899;&#35782;&#21035;&#31995;&#32479;');
        log('&#35831;&#28857;&#20987; "&#36830;&#25509;" &#25353;&#38062;&#36830;&#25509;&#21040;&#35821;&#38899;&#26381;&#21153;&#22120;');

        // WebSocket connection
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('? &#24050;&#32463;&#36830;&#25509;&#65292;&#26080;&#38656;&#37325;&#22797;&#25805;&#20316;', 'info');
                return;
            }

            const serverUrl = serverUrlInput.value;
            ws = new WebSocket(serverUrl);

            ws.onopen = () => {
                statusLight.style.backgroundColor = 'var(--success-color)';
                statusText.innerHTML = '连接成功';
                connectBtn.innerHTML = '断开连接';
                recordBtn.disabled = false;
                log('🔗 服务器连接成功', 'connect');

                // 连接成功后立即查询DTU状态和打印状态
                setTimeout(() => {
                    requestDtuStatus();
                    updatePrintStatus('ready', '打印系统已连接');
                    requestPrintStatus();
                    refreshQueue();
                    startPrintStatusMonitoring();
                }, 1000);
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);

                    // 处理DTU相关消息
                    if (data.type === 'config_info') {
                        // 服务器配置信息
                        log(`服务器配置: ${data.message}`, 'info');
                        if (data.dtuEnabled) {
                            updateDtuStatus(data.dtuConnected,
                                data.dtuConnected ? 'DTU已连接' : 'DTU未连接',
                                { deviceId: data.dtuDeviceId });
                            log(`DTU设备: ${data.dtuDeviceId} (${data.dtuConnected ? '已连接' : '未连接'})`, 'info');
                        } else {
                            updateDtuStatus(false, 'DTU未启用');
                        }
                    } else if (data.type === 'dtu_connected') {
                        // DTU连接成功
                        updateDtuStatus(true, 'DTU连接成功', data);
                        log(`✅ DTU设备连接成功: ${data.deviceId}`, 'connect');
                        logDtu(`✅ DTU连接成功 - 设备ID: ${data.deviceId}`);
                    } else if (data.type === 'dtu_connection_lost') {
                        // DTU连接丢失
                        updateDtuStatus(false, 'DTU连接丢失');
                        log(`⚠️ DTU连接丢失，重试次数: ${data.retryCount}`, 'error');
                        logDtu(`⚠️ DTU连接丢失，正在重试... (${data.retryCount})`);
                    } else if (data.type === 'dtu_status_update') {
                        // DTU状态更新
                        updateDtuStatus(true, 'DTU状态已更新', data);
                        log(`📊 DTU状态更新`, 'info');
                        logDtu(`📊 DTU状态更新: ${JSON.stringify(data)}`);
                    } else if (data.type === 'dtu_data_sent') {
                        // DTU数据发送成功
                        log(`📤 DTU数据发送成功: ${data.data}`, 'sent');
                        logDtu(`📤 数据发送成功: ${JSON.stringify(data.data)}`);
                    } else if (data.type === 'dtu_text_result') {
                        // DTU文字发送结果
                        if (data.success) {
                            logDtu(`✅ 文字发送成功: ${data.text}`);
                            log(`✅ DTU文字发送成功: ${data.text}`, 'sent');
                        } else {
                            logDtu(`❌ 文字发送失败: ${data.error}`);
                            log(`❌ DTU文字发送失败: ${data.error}`, 'error');
                        }
                    } else if (data.type === 'dtu_status') {
                        // DTU状态查询结果
                        updateDtuStatus(data.connectionStatus.connected,
                            data.connectionStatus.connected ? 'DTU在线' : 'DTU离线',
                            data.deviceStatus);
                        logDtu(`📊 DTU状态查询结果: ${JSON.stringify(data)}`);
                    } else if (data.type === 'dtu_status_error') {
                        // DTU状态查询错误
                        updateDtuStatus(false, 'DTU状态查询失败');
                        logDtu(`❌ DTU状态查询失败: ${data.error}`);
                        log(`❌ DTU状态查询失败: ${data.error}`, 'error');
                    } else if (data.type === 'dtu_error') {
                        // DTU错误
                        log(`❌ DTU错误: ${data.error}`, 'error');
                        logDtu(`❌ DTU错误: ${data.error}`);
                    } else if (data.type === 'text_sent') {
                        // 处理文字发送确认消息（保持兼容性）
                        logDtu(`✅ 文字转发确认: ${data.text}`);
                        if (data.demo_mode) {
                            logDtu(`🎭 演示模式 - 目标设备: ${data.target_devices.join(', ')}`);
                        } else {
                            logDtu(`📍 实际设备: ${data.target_devices.join(', ')}`);
                        }
                    } else if (data.type === 'print_status_report') {
                        // 打印状态报告
                        log(`🖨️ 打印状态更新: ${data.status_type}`, 'info');

                        if (data.status_type === 'task_started') {
                            updatePrintStatus('printing', `正在打印任务: ${data.print_status.current_task?.id || '未知'}`);
                            updatePrintProgress(0, data.print_status.current_task?.id);
                        } else if (data.status_type === 'task_progress') {
                            const progress = data.print_status.progress || 0;
                            updatePrintProgress(progress, data.print_status.current_task?.id, data.performance?.transmission_speed || 0);
                        } else if (data.status_type === 'task_completed') {
                            updatePrintStatus('ready', '打印完成');
                            updatePrintProgress(100);
                            printStatus.currentTask = null;
                            currentTaskIdSpan.textContent = '无';
                            resetPrintButtons();
                        } else if (data.status_type === 'error') {
                            updatePrintStatus('error', `打印错误: ${data.error || '未知错误'}`);
                            resetPrintButtons();
                        }

                        // 更新统计信息
                        if (data.print_status) {
                            updatePrintStats({
                                queueSize: data.print_status.queue_size,
                                completedTasks: data.print_status.completed_tasks,
                                failedTasks: data.print_status.failed_tasks,
                                successRate: data.performance?.success_rate,
                                compressionRatio: data.performance?.compression_ratio
                            });
                        }
                    } else if (data.type === 'print_queue_info') {
                        // 打印队列信息
                        log(`📊 队列信息更新: ${data.queue_size} 个任务`, 'info');
                        updateQueueDisplay(data.queue);
                        updatePrintStats({ queueSize: data.queue_size });
                    } else if (data.type === 'print_command_result') {
                        // 打印指令执行结果
                        if (data.success) {
                            log(`✅ 打印指令执行成功: ${data.command}`, 'sent');
                            if (data.command === 'start_print') {
                                updatePrintStatus('printing', `开始打印任务: ${data.task_id}`);
                            }
                        } else {
                            log(`❌ 打印指令执行失败: ${data.error}`, 'error');
                            updatePrintStatus('error', `指令失败: ${data.error}`);
                        }
                    } else {
                        log(`服务器: ${JSON.stringify(data)}`, 'server');
                    }
                } catch (error) {
                    log(`服务器原始消息: ${event.data}`, 'server');
                }
            };

            ws.onerror = (error) => {
                log(`? WebSocket &#38169;&#35823;: ${error.message}`, 'error');
                handleDisconnect();
            };

            ws.onclose = () => {
                handleDisconnect();
                log('? &#26381;&#21153;&#22120;&#36830;&#25509;&#24050;&#26029;&#24320;', 'disconnect');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function handleDisconnect() {
            statusLight.style.backgroundColor = 'var(--error-color)';
            statusText.innerHTML = '未连接';
            connectBtn.innerHTML = '连接';
            recordBtn.disabled = true;
            isRecording = false;
            recordBtn.innerHTML = '🎤 开始录音';
            recordBtn.style.background = '';
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
            }

            // 断开连接时重置DTU和打印状态
            updateDtuStatus(false, 'WebSocket未连接');
            updatePrintStatus('offline', 'WebSocket未连接');
            stopPrintStatusMonitoring();

            // 重置打印相关状态
            printStatus.currentTask = null;
            currentTaskIdSpan.textContent = '无';
            updatePrintProgress(0);
            resetPrintButtons();
        }

        // DTU相关函数
        function requestDtuStatus() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'get_dtu_status' }));
                log('📡 请求DTU状态...', 'info');
            }
        }

        function sendDtuText(text) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'send_dtu_text',
                    text: text
                }));
                log(`📤 发送DTU文字: ${text}`, 'sent');
                logDtu(`📤 发送文字: ${text}`);
            } else {
                logDtu('❌ WebSocket未连接，无法发送文字');
                log('❌ WebSocket未连接，无法发送DTU文字', 'error');
            }
        }

        // 打印控制函数
        function startPrint() {
            const text = printTextInput.value.trim();
            if (!text) {
                alert('请输入要打印的内容');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                const printCommand = {
                    type: 'print_command',
                    command: 'start_print',
                    content: {
                        text: text,
                        format: 'braille',
                        encoding: 'utf-8'
                    },
                    config: {
                        mode: printModeSelect.value,
                        priority: printPrioritySelect.value,
                        compression_enabled: printConfig.compressionEnabled
                    }
                };

                ws.send(JSON.stringify(printCommand));
                log(`🖨️ 发送打印指令: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`, 'sent');
                updatePrintStatus('printing', '正在发送打印任务...');
            } else {
                alert('WebSocket未连接，无法发送打印指令');
                log('❌ WebSocket未连接，无法发送打印指令', 'error');
            }
        }

        function pausePrint() {
            if (ws && ws.readyState === WebSocket.OPEN && printStatus.currentTask) {
                ws.send(JSON.stringify({
                    type: 'print_command',
                    command: 'pause_print',
                    task_id: printStatus.currentTask
                }));
                log(`⏸️ 暂停打印任务: ${printStatus.currentTask}`, 'sent');
            }
        }

        function resumePrint() {
            if (ws && ws.readyState === WebSocket.OPEN && printStatus.currentTask) {
                ws.send(JSON.stringify({
                    type: 'print_command',
                    command: 'resume_print',
                    task_id: printStatus.currentTask
                }));
                log(`▶️ 恢复打印任务: ${printStatus.currentTask}`, 'sent');
            }
        }

        function cancelPrint() {
            if (ws && ws.readyState === WebSocket.OPEN && printStatus.currentTask) {
                if (confirm('确定要取消当前打印任务吗？')) {
                    ws.send(JSON.stringify({
                        type: 'print_command',
                        command: 'cancel_print',
                        task_id: printStatus.currentTask
                    }));
                    log(`❌ 取消打印任务: ${printStatus.currentTask}`, 'sent');
                }
            }
        }

        function refreshQueue() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'print_queue_management',
                    operation: 'get_queue'
                }));
                log('🔄 刷新打印队列', 'info');
            }
        }

        function clearQueue() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                if (confirm('确定要清空打印队列吗？这将删除所有等待中的任务。')) {
                    ws.send(JSON.stringify({
                        type: 'print_queue_management',
                        operation: 'clear_queue'
                    }));
                    log('🗑️ 清空打印队列', 'sent');
                }
            }
        }

        function requestPrintStatus() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'print_status_request',
                    query_type: 'all'
                }));
                log('📊 请求打印状态', 'info');
            }
        }

        connectBtn.addEventListener('click', () => {
            if (!ws || ws.readyState === WebSocket.CLOSED) {
                connect();
            } else {
                disconnect();
            }
        });

        // Recording logic
        async function startRecording() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                alert('&#24744;&#30340;&#27983;&#35272;&#22120;&#19981;&#25903;&#25345;&#24405;&#38899;&#21151;&#33021;&#12290;');
                return;
            }

            try {
                // �Ż���Ƶ¼������
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,  // Ѷ��APIҪ��Ĳ�����
                        sampleSize: 16,     // 16λ����
                        channelCount: 1,    // ������
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                // �Ż�MediaRecorder����
                const options = {
                    mimeType: 'audio/webm;codecs=opus',  // ʹ��opus����
                    audioBitsPerSecond: 16000           // ������
                };

                // ��������֧�ֵĸ�ʽ
                if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                    // ������Ĭ�ϸ�ʽ
                    options.mimeType = 'audio/webm';
                    console.log('ʹ�ý�����Ƶ��ʽ: audio/webm');
                }

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];

                let recordingStartTime = Date.now();
                let recordingTimer = null;

                // ����¼��ʱ����ʾ
                recordingTimer = setInterval(() => {
                    const duration = Math.floor((Date.now() - recordingStartTime) / 1000);
                    recordBtn.innerHTML = `?? &#20572;&#27490;&#24405;&#38899; (${duration}s)`;
                }, 1000);

                mediaRecorder.ondataavailable = event => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        console.log('¼����Ƶ��:', event.data.size, 'bytes');
                    }
                };

                mediaRecorder.onstop = async () => {
                    clearInterval(recordingTimer);

                    if (audioChunks.length === 0) {
                        log('? &#38169;&#35823;&#65306;&#27809;&#26377;&#24405;&#21046;&#21040;&#38899;&#39057;&#25968;&#25454;', 'error');
                        stream.getTracks().forEach(track => track.stop());
                        return;
                    }

                    const audioBlob = new Blob(audioChunks, { type: options.mimeType });
                    console.log('������ƵBlob:', audioBlob.size, 'bytes', audioBlob.type);

                    if (ws && ws.readyState === WebSocket.OPEN) {
                        try {
                            // ��Blobת��ΪArrayBuffer����
                            const arrayBuffer = await audioBlob.arrayBuffer();
                            ws.send(arrayBuffer);
                            log('? &#35821;&#38899;&#25968;&#25454;&#24050;&#21457;&#36865; (' + Math.round(audioBlob.size / 1024) + 'KB)', 'sent');
                        } catch (error) {
                            console.error('������Ƶ����ʧ��:', error);
                            log('? &#21457;&#36865;&#22833;&#36133;&#65306;' + error.message, 'error');
                        }
                    } else {
                        log('? &#21457;&#36865;&#22833;&#36133;&#65306;&#26381;&#21153;&#22120;&#26410;&#36830;&#25509;', 'error');
                    }
                    stream.getTracks().forEach(track => track.stop()); // Stop microphone
                };

                mediaRecorder.onerror = (event) => {
                    console.error('¼������:', event.error);
                    log('? &#24405;&#38899;&#38169;&#35823;&#65306;' + event.error.message, 'error');
                    clearInterval(recordingTimer);
                };

                // ��ʼ¼�ƣ�����ʱ��Ƭ��Ϊ1000ms
                mediaRecorder.start(1000);
                isRecording = true;
                recordBtn.innerHTML = '?? &#20572;&#27490;&#24405;&#38899; (0s)';
                recordBtn.style.background = 'linear-gradient(135deg, #f54ea2 0%, #ff7676 100%)';
                log('? &#24320;&#22987;&#24405;&#38899;... (&#20248;&#21270;&#37197;&#32622;: 16kHz, &#21333;&#22768;&#36947;)', 'info');

                // �Զ�ֹͣ¼�����30�룩
                setTimeout(() => {
                    if (isRecording) {
                        stopRecording();
                        log('? &#24405;&#38899;&#33258;&#21160;&#20572;&#27490; (&#26368;&#38271;30&#31186;)', 'info');
                    }
                }, 30000);

            } catch (err) {
                console.error("&#24405;&#38899;&#38169;&#35823;:", err);
                let errorMsg = err.message;
                if (err.name === 'NotAllowedError') {
                    errorMsg = '&#40614;&#20811;&#39118;&#26435;&#38480;&#34987;&#25298;&#32477;&#65292;&#35831;&#20801;&#35768;&#27983;&#35272;&#22120;&#35775;&#38382;&#40614;&#20811;&#39118;';
                } else if (err.name === 'NotFoundError') {
                    errorMsg = '&#26410;&#25214;&#21040;&#40614;&#20811;&#39118;&#35774;&#22791;';
                }
                alert(`&#26080;&#27861;&#24320;&#22987;&#24405;&#38899;: ${errorMsg}`);
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                isRecording = false;
                recordBtn.innerHTML = '?? &#24320;&#22987;&#24405;&#38899;';
                recordBtn.style.background = '';
                log('? &#20572;&#27490;&#24405;&#38899;', 'info');
            }
        }

        recordBtn.addEventListener('click', () => {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });

        // API Test Logic
        apiBtn.addEventListener('click', async () => {
            const textToTranslate = apiTextInput.value;
            if (!textToTranslate) {
                logApi('&#38169;&#35823;: &#35831;&#36755;&#20837;&#35201;&#32763;&#35793;&#30340;&#25991;&#26412;&#12290;');
                return;
            }

            logApi('&#27491;&#22312;&#21457;&#36865;API&#35831;&#27714;...');
            try {
                const response = await fetch('http://localhost:8080/api/translate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        text: textToTranslate,
                        source_lang: sourceLangSelect.value,
                        target_lang: targetLangSelect.value
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP&#38169;&#35823;! &#29366;&#24577;: ${response.status}`);
                }

                const result = await response.json();
                logApi(`&#25104;&#21151;: <pre>${JSON.stringify(result, null, 2)}</pre>`);

            } catch (error) {
                logApi(`API&#35831;&#27714;&#22833;&#36133;: ${error.message}`);
            }
        });

        // DTU按钮事件监听器
        refreshDtuBtn.addEventListener('click', () => {
            requestDtuStatus();
            logDtu('🔄 手动刷新DTU状态...');
        });

        sendDtuTextBtn.addEventListener('click', () => {
            const text = dtuTextInput.value.trim();
            if (!text) {
                logDtu('❌ 错误: 请输入要发送的文字...');
                return;
            }

            sendDtuText(text);
            dtuTextInput.value = ''; // 清空输入框
        });

        // 添加Enter键支持
        dtuTextInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendDtuTextBtn.click();
            }
        });

        // 打印控制事件监听器
        startPrintBtn.addEventListener('click', startPrint);
        pausePrintBtn.addEventListener('click', pausePrint);
        resumePrintBtn.addEventListener('click', resumePrint);
        cancelPrintBtn.addEventListener('click', cancelPrint);
        refreshQueueBtn.addEventListener('click', refreshQueue);
        clearQueueBtn.addEventListener('click', clearQueue);

        // 打印内容输入监听器
        printTextInput.addEventListener('input', updatePrintPreview);
        printTextInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                startPrint();
            }
        });

        // 打印配置变更监听器
        printModeSelect.addEventListener('change', (e) => {
            printConfig.mode = e.target.value;
            log(`🔧 打印模式变更: ${e.target.value}`, 'info');
        });

        printPrioritySelect.addEventListener('change', (e) => {
            printConfig.priority = e.target.value;
            log(`⚡ 优先级变更: ${e.target.value}`, 'info');
        });

        // 初始化状态
        updateDtuStatus(false, 'WebSocket未连接');
        updatePrintStatus('offline', 'WebSocket未连接');
        updatePrintPreview();

        // 定期刷新打印状态（连接后启动）
        let printStatusInterval = null;

        function startPrintStatusMonitoring() {
            if (printStatusInterval) {
                clearInterval(printStatusInterval);
            }

            printStatusInterval = setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    requestPrintStatus();
                    refreshQueue();
                }
            }, printConfig.statusReportInterval);

            log('📊 打印状态监控已启动', 'info');
        }

        function stopPrintStatusMonitoring() {
            if (printStatusInterval) {
                clearInterval(printStatusInterval);
                printStatusInterval = null;
                log('📊 打印状态监控已停止', 'info');
            }
        }

    </script>
</body>

</html>