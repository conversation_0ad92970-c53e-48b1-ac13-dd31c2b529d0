@echo off
chcp 65001 >nul
title WebSocket+DTU系统状态检查

echo.
echo ========================================
echo   WebSocket+DTU系统状态检查工具
echo   版本: 2.0.0
echo ========================================
echo.

:: 检查Node.js环境
echo [1] Node.js环境检查:
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或未添加到PATH
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js版本: %%i
)

echo.

:: 检查端口占用情况
echo [2] 端口占用检查:
netstat -an | findstr ":8081" >nul && echo ✅ 端口8081 (WebSocket后端) - 已占用 || echo ❌ 端口8081 (WebSocket后端) - 空闲
netstat -an | findstr ":4000" >nul && echo ✅ 端口4000 (前端Web) - 已占用 || echo ❌ 端口4000 (前端Web) - 空闲

echo.

:: 检查进程状态
echo [3] 进程状态检查:
tasklist | findstr "node.exe" >nul && echo ✅ Node.js进程运行中 || echo ❌ 未发现Node.js进程

echo.

:: 检查项目文件
echo [4] 项目文件检查:
if exist "code\voice-backend\server.js" (echo ✅ WebSocket后端文件存在) else (echo ❌ WebSocket后端文件缺失)
if exist "code\voice-frontend\server.js" (echo ✅ 前端服务器文件存在) else (echo ❌ 前端服务器文件缺失)
if exist "code\voice-backend\config.js" (echo ✅ 配置文件存在) else (echo ❌ 配置文件缺失)

echo.

:: 检查依赖包
echo [5] 依赖包检查:
if exist "code\voice-backend\node_modules" (echo ✅ 后端依赖包已安装) else (echo ❌ 后端依赖包未安装)
if exist "code\voice-frontend\node_modules" (echo ✅ 前端依赖包已安装) else (echo ❌ 前端依赖包未安装)

echo.

:: 网络连接测试
echo [6] 网络连接测试:
ping -n 1 dtu.yinled.com >nul 2>&1 && echo ✅ 银尔达DTU平台连接正常 || echo ❌ 银尔达DTU平台连接异常

echo.

:: HTTP服务测试
echo [7] HTTP服务测试:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ 前端HTTP服务响应正常' } catch { Write-Host '❌ 前端HTTP服务无响应' }" 2>nul

echo.

:: WebSocket服务测试
echo [8] WebSocket服务测试:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ WebSocket服务响应正常' } catch { Write-Host '❌ WebSocket服务无响应' }" 2>nul

echo.

:: 系统资源检查
echo [9] 系统资源检查:
for /f "tokens=2 delims=:" %%i in ('tasklist /fi "imagename eq node.exe" ^| findstr /c:" K"') do (
    echo ✅ Node.js内存使用: %%i
    goto :found
)
echo ❌ 未找到Node.js进程
:found

echo.

:: 配置文件验证
echo [10] 配置验证:
if exist "code\voice-backend\config.js" (
    echo ✅ 正在验证配置文件...
    node -e "try { const config = require('./code/voice-backend/config.js'); console.log('✅ 配置文件语法正确'); console.log('✅ WebSocket端口:', config.server.websocket.port); } catch(e) { console.log('❌ 配置文件错误:', e.message); }" 2>nul
)

echo.
echo ========================================
echo 状态检查完成！
echo.
echo 💡 如果发现问题:
echo   • 端口被占用: 重启系统或更改端口
echo   • 依赖包缺失: 运行 npm install
echo   • 网络异常: 检查防火墙和网络连接
echo   • 文件缺失: 重新下载项目文件
echo ========================================
echo.

pause
