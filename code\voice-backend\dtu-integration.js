// 银尔达DTU平台集成模块
const EventEmitter = require('events');
const https = require('https');
const http = require('http');

class DTUIntegration extends EventEmitter {
    constructor(config) {
        super();
        this.config = {
            apiBase: config.apiBase || 'http://api.yinled.com',
            serverHost: config.serverHost || 'dtu.yinled.com',
            serverPort: config.serverPort || 8888,
            deviceId: config.deviceId || 'CH32V307_001',
            deviceKey: config.deviceKey || '',
            timeout: config.timeout || 10000,
            retryAttempts: config.retryAttempts || 3,
            retryDelay: config.retryDelay || 1000
        };

        this.isConnected = false;
        this.lastHeartbeat = null;
        this.heartbeatInterval = null;
        this.retryCount = 0;

        console.log('🌐 银尔达DTU集成模块已初始化');
        console.log('📡 API地址:', this.config.apiBase);
        console.log('🔧 设备ID:', this.config.deviceId);
    }

    // 初始化DTU连接
    async initialize() {
        try {
            console.log('🚀 正在初始化DTU连接...');

            // 检查设备状态
            const status = await this.getDeviceStatus();
            if (status) {
                this.isConnected = true;
                this.startHeartbeat();
                this.emit('connected', { deviceId: this.config.deviceId, status });
                console.log('✅ DTU连接初始化成功');
                return true;
            } else {
                throw new Error('设备状态检查失败');
            }
        } catch (error) {
            console.error('❌ DTU连接初始化失败:', error.message);
            this.emit('error', error);
            return false;
        }
    }

    // 发送数据到设备
    async sendToDevice(data) {
        if (!this.config.deviceKey) {
            throw new Error('设备密钥未配置');
        }

        const sendData = {
            device_id: this.config.deviceId,
            data: typeof data === 'string' ? data : JSON.stringify(data),
            timestamp: Date.now()
        };

        try {
            console.log(`📤 发送数据到设备 [${this.config.deviceId}]:`, data);

            const response = await this.makeAPIRequest('POST', '/device/send', sendData);

            if (response.success) {
                console.log('✅ 数据发送成功');
                this.emit('dataSent', { deviceId: this.config.deviceId, data, response });
                return response;
            } else {
                throw new Error(response.message || '发送失败');
            }
        } catch (error) {
            console.error('❌ 数据发送失败:', error.message);
            this.emit('sendError', { deviceId: this.config.deviceId, data, error });
            throw error;
        }
    }

    // 获取设备状态
    async getDeviceStatus() {
        try {
            console.log(`🔍 查询设备状态 [${this.config.deviceId}]`);

            const response = await this.makeAPIRequest('GET', `/device/status/${this.config.deviceId}`);

            if (response) {
                const status = {
                    deviceId: this.config.deviceId,
                    online: response.online || false,
                    lastSeen: response.lastSeen || new Date().toISOString(),
                    signal: response.signal || 'unknown',
                    battery: response.battery || 'unknown',
                    firmware: response.firmware || 'unknown'
                };

                console.log('📊 设备状态:', status);
                this.emit('statusUpdate', status);
                return status;
            }
            return null;
        } catch (error) {
            console.error('❌ 获取设备状态失败:', error.message);
            this.emit('statusError', { deviceId: this.config.deviceId, error });
            return null;
        }
    }

    // 获取设备数据
    async getDeviceData(limit = 10) {
        try {
            console.log(`📥 获取设备数据 [${this.config.deviceId}]`);

            const response = await this.makeAPIRequest('GET', `/device/data/${this.config.deviceId}?limit=${limit}`);

            if (response && Array.isArray(response)) {
                console.log(`📦 获取到 ${response.length} 条设备数据`);
                this.emit('dataReceived', { deviceId: this.config.deviceId, data: response });
                return response;
            }
            return [];
        } catch (error) {
            console.error('❌ 获取设备数据失败:', error.message);
            this.emit('dataError', { deviceId: this.config.deviceId, error });
            return [];
        }
    }

    // 发送语音识别结果到设备
    async sendVoiceResult(voiceData) {
        const dtuMessage = {
            type: 'voice_recognition',
            text: voiceData.text,
            confidence: voiceData.confidence || 0.9,
            timestamp: new Date().toISOString(),
            source: 'websocket_voice_backend'
        };

        return await this.sendToDevice(dtuMessage);
    }

    // 发送控制命令到设备
    async sendControlCommand(command, params = {}) {
        const controlMessage = {
            type: 'control_command',
            command: command,
            params: params,
            timestamp: new Date().toISOString(),
            source: 'websocket_control'
        };

        return await this.sendToDevice(controlMessage);
    }

    // 发送打印任务到设备
    async sendPrintTask(printData) {
        if (!printData.text && !printData.content) {
            throw new Error('打印内容不能为空');
        }

        const printMessage = {
            type: 'print_task',
            taskId: printData.taskId || `print_${Date.now()}`,
            content: {
                text: printData.text || printData.content.text,
                format: printData.format || 'braille',
                encoding: 'utf-8'
            },
            config: {
                mode: printData.config?.mode || 'standard',
                quality: printData.config?.quality || 'normal',
                speed: printData.config?.speed || 'standard',
                copies: printData.config?.copies || 1
            },
            timestamp: new Date().toISOString(),
            source: 'websocket_print_system'
        };

        console.log(`🖨️ 发送打印任务到设备 [${this.config.deviceId}]:`, printMessage.taskId);
        return await this.sendToDevice(printMessage);
    }

    // 请求设备打印状态
    async requestPrintStatus(taskId = null) {
        const statusRequest = {
            type: 'status_request',
            request: 'print_status',
            taskId: taskId,
            timestamp: new Date().toISOString(),
            source: 'websocket_status_monitor'
        };

        console.log(`📊 请求设备打印状态 [${this.config.deviceId}]:`, taskId || 'all');
        return await this.sendToDevice(statusRequest);
    }

    // 发送打印控制命令（暂停、恢复、取消等）
    async sendPrintControl(action, taskId = null, params = {}) {
        const validActions = ['pause', 'resume', 'cancel', 'emergency_stop', 'calibrate'];
        if (!validActions.includes(action)) {
            throw new Error(`无效的打印控制动作: ${action}`);
        }

        const controlMessage = {
            type: 'print_control',
            action: action,
            taskId: taskId,
            params: params,
            timestamp: new Date().toISOString(),
            source: 'websocket_print_control'
        };

        console.log(`🎛️ 发送打印控制命令 [${this.config.deviceId}]: ${action}`, taskId || '');
        return await this.sendToDevice(controlMessage);
    }

    // 更新设备打印配置
    async updatePrintConfig(configType, configData) {
        const configMessage = {
            type: 'config_update',
            configType: configType,
            config: configData,
            timestamp: new Date().toISOString(),
            source: 'websocket_config_manager'
        };

        console.log(`⚙️ 更新设备打印配置 [${this.config.deviceId}]: ${configType}`);
        return await this.sendToDevice(configMessage);
    }

    // 执行API请求
    async makeAPIRequest(method, endpoint, data = null) {
        return new Promise((resolve, reject) => {
            const url = new URL(endpoint, this.config.apiBase);
            const isHttps = url.protocol === 'https:';
            const httpModule = isHttps ? https : http;

            const options = {
                hostname: url.hostname,
                port: url.port || (isHttps ? 443 : 80),
                path: url.pathname + url.search,
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'WebSocket-DTU-Backend/1.0'
                },
                timeout: this.config.timeout
            };

            // 添加认证头
            if (this.config.deviceKey) {
                options.headers['Authorization'] = `Bearer ${this.config.deviceKey}`;
            }

            const req = httpModule.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            const parsed = responseData ? JSON.parse(responseData) : {};
                            resolve(parsed);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                        }
                    } catch (error) {
                        reject(new Error(`解析响应失败: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`请求失败: ${error.message}`));
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            // 发送请求数据
            if (data && (method === 'POST' || method === 'PUT')) {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    // 启动心跳检测
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        this.heartbeatInterval = setInterval(async () => {
            try {
                const status = await this.getDeviceStatus();
                this.lastHeartbeat = new Date();

                if (status && status.online) {
                    this.isConnected = true;
                    this.retryCount = 0;
                } else {
                    this.handleConnectionLoss();
                }
            } catch (error) {
                this.handleConnectionLoss();
            }
        }, 30000); // 每30秒检查一次

        console.log('💓 DTU心跳检测已启动');
    }

    // 处理连接丢失
    handleConnectionLoss() {
        this.isConnected = false;
        this.retryCount++;

        console.log(`⚠️ DTU连接丢失，重试次数: ${this.retryCount}/${this.config.retryAttempts}`);
        this.emit('connectionLost', { retryCount: this.retryCount });

        if (this.retryCount >= this.config.retryAttempts) {
            console.error('❌ DTU连接重试次数已达上限');
            this.emit('connectionFailed');
            this.stopHeartbeat();
        }
    }

    // 停止心跳检测
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
            console.log('💔 DTU心跳检测已停止');
        }
    }

    // 获取连接状态
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            lastHeartbeat: this.lastHeartbeat,
            retryCount: this.retryCount,
            deviceId: this.config.deviceId,
            serverHost: this.config.serverHost
        };
    }

    // 关闭连接
    async disconnect() {
        console.log('🔌 正在断开DTU连接...');
        this.stopHeartbeat();
        this.isConnected = false;
        this.emit('disconnected');
        console.log('✅ DTU连接已断开');
    }
}

module.exports = DTUIntegration;
