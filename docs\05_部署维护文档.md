# WebSocket+DTU智能语音识别盲文打印系统 - 部署维护文档

## 📋 目录

1. [部署概述](#部署概述)
2. [环境搭建](#环境搭建)
3. [系统部署](#系统部署)
4. [配置管理](#配置管理)
5. [启动脚本系统](#启动脚本系统)
6. [日常维护](#日常维护)
7. [监控与告警](#监控与告警)
8. [备份与恢复](#备份与恢复)
9. [升级指南](#升级指南)
10. [故障排除](#故障排除)

## 🎯 部署概述

### 系统架构
WebSocket+DTU智能语音识别盲文打印系统采用分布式架构：

```
前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
```

### 部署模式
- **开发环境**: 本地开发和测试
- **生产环境**: 正式运行环境
- **测试环境**: 功能验证和集成测试

### 核心组件
- **WebSocket后端服务**: Node.js + WebSocket + DTU集成
- **Web前端界面**: HTML5 + JavaScript + 实时通信
- **DTU通信模块**: 银尔达DTU平台集成
- **硬件控制层**: CH32V307 + Air780e + 步进电机

## 🔧 环境搭建

### 硬件要求

#### 最低配置
- **CPU**: Intel i3 或 AMD 同等性能
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **网络**: 稳定的互联网连接

#### 推荐配置
- **CPU**: Intel i5 或 AMD 同等性能
- **内存**: 8GB RAM
- **存储**: 20GB 可用空间 (SSD推荐)
- **网络**: 100Mbps 带宽

#### 硬件设备
- **CH32V307开发板**: 主控制器
- **Air780e 4G模块**: DTU通信模块
- **INMP441麦克风**: 音频采集
- **Keyes CNC Shield v3.0**: 电机驱动扩展板
- **TMC2209步进电机驱动**: 3个
- **17HS4401步进电机**: 3个
- **4G SIM卡**: 支持数据流量

### 软件环境

#### 操作系统要求
- **Windows**: Windows 10/11 (推荐)
- **Linux**: Ubuntu 18.04+ / CentOS 7+
- **macOS**: macOS 10.15+ (开发环境)

#### 必需软件
```bash
# Node.js环境 (必需)
Node.js >= 16.0.0
npm >= 8.0.0

# 开发工具 (可选)
Git >= 2.20.0
Visual Studio Code
MounRiver Studio (CH32开发)
```

#### 安装Node.js
```bash
# Windows - 下载安装包
https://nodejs.org/

# Linux - 使用包管理器
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 网络配置

#### 端口要求
- **8081**: WebSocket后端服务
- **4000**: 前端Web界面
- **8888**: 银尔达DTU平台通信 (外部)

#### 防火墙设置
```bash
# Windows防火墙
netsh advfirewall firewall add rule name="WebSocket Backend" dir=in action=allow protocol=TCP localport=8081
netsh advfirewall firewall add rule name="Frontend Web" dir=in action=allow protocol=TCP localport=4000

# Linux防火墙 (ufw)
sudo ufw allow 8081/tcp
sudo ufw allow 4000/tcp
```

#### DTU平台连接
- **平台地址**: dtu.yinled.com:8888
- **设备IMEI**: 869080075169294
- **设备ICCID**: 898604021024C0050919
- **数据套餐**: 4G 30M/月

## 🚀 系统部署

### 1. 项目获取

#### 从源码部署
```bash
# 克隆项目
git clone https://github.com/your-repo/websocket-dtu-braille-printer.git
cd websocket-dtu-braille-printer

# 检查项目结构
dir /b
```

#### 项目结构验证
```
WebSocket+DTU智能语音识别盲文打印系统/
├── 📁 code/voice-backend/          # WebSocket后端服务
├── 📁 code/voice-frontend/         # Web前端界面
├── 📁 code/01.0/                   # CH32V307固件
├── 📁 docs/                        # 项目文档
├── start_websocket_dtu_system.bat  # 主启动脚本
├── quick_start_websocket_dtu.bat   # 快速启动脚本
├── check_websocket_dtu_status.bat  # 状态检查脚本
└── stop_websocket_dtu_system.bat   # 系统停止脚本
```

### 2. 依赖安装

#### 后端依赖
```bash
cd code/voice-backend
npm install

# 验证依赖
npm list --depth=0
```

#### 前端依赖
```bash
cd ../voice-frontend
npm install

# 验证依赖
npm list --depth=0
```

#### 依赖包说明
**后端核心依赖**:
- `ws`: WebSocket服务器
- `crypto`: 加密功能
- `formdata-node`: 表单数据处理

**前端核心依赖**:
- `express`: Web服务器
- `http-proxy-middleware`: 代理中间件

### 3. 配置文件设置

#### 环境变量配置
```bash
# 复制环境变量模板
cd code/voice-backend
cp .env.example .env

# 编辑环境变量
notepad .env  # Windows
nano .env     # Linux
```

#### 环境变量示例
```bash
# 系统环境
NODE_ENV=production

# WebSocket服务器
WS_HOST=0.0.0.0
WS_PORT=8081

# 讯飞语音识别
XUNFEI_ENABLED=true
XUNFEI_APP_ID=your_app_id
XUNFEI_API_KEY=your_api_key
XUNFEI_API_SECRET=your_api_secret

# DTU平台
DTU_ENABLED=true
DTU_DEVICE_ID=869080075169294
DTU_DEVICE_KEY=898604021024C0050919
```

### 4. 系统启动

#### 完整启动流程
```bash
# 返回项目根目录
cd ../..

# 使用主启动脚本
start_websocket_dtu_system.bat
```

#### 启动流程说明
1. **环境检查**: Node.js版本验证
2. **目录验证**: 检查关键文件存在性
3. **依赖检查**: 自动安装npm包
4. **后端启动**: WebSocket+DTU服务器 (端口8081)
5. **前端启动**: Web界面服务器 (端口4000)
6. **状态显示**: 完整的系统信息和访问地址

#### 验证部署
```bash
# 检查系统状态
check_websocket_dtu_status.bat

# 访问Web界面
http://localhost:4000

# 测试WebSocket连接
# 在浏览器控制台执行
const ws = new WebSocket('ws://localhost:8081/voice-ws');
ws.onopen = () => console.log('WebSocket连接成功');
```

## ⚙️ 配置管理

### 配置文件结构

#### 配置层次结构
```
code/voice-backend/
├── config.js              # 主配置文件
├── config.dev.js          # 开发环境配置
├── config.prod.js         # 生产环境配置
├── config-manager.js      # 配置管理器
├── .env.example           # 环境变量模板
└── .env                   # 环境变量文件
```

#### 配置优先级
1. **环境变量** (.env) - 最高优先级
2. **环境特定配置** (config.{env}.js) - 中等优先级
3. **基础配置** (config.js) - 默认配置

### 主要配置项

#### 系统配置
```javascript
system: {
    name: 'CH32V307_WebSocket_DTU_System',
    version: '2.0.0',
    environment: 'production',
    debug: false,
    logLevel: 'info'
}
```

#### 服务器配置
```javascript
server: {
    websocket: {
        host: '0.0.0.0',
        port: 8081,
        maxConnections: 1000,
        heartbeatInterval: 30000,
        connectionTimeout: 120000
    }
}
```

#### DTU平台配置
```javascript
dtu: {
    enabled: true,
    api: {
        baseUrl: 'https://dtu.yinled.com:8888',
        timeout: 10000,
        retryAttempts: 3
    },
    device: {
        imei: '869080075169294',
        iccid: '898604021024C0050919'
    }
}
```

### 配置管理最佳实践

#### 1. 环境分离
```bash
# 开发环境
NODE_ENV=development node server.js

# 生产环境
NODE_ENV=production node server.js
```

#### 2. 敏感信息保护
- 使用环境变量存储API密钥
- 不要将.env文件提交到版本控制
- 生产环境使用专门的密钥管理服务

#### 3. 配置验证
```javascript
// 启动时验证配置
const config = require('./config');
if (!config.validateConfig()) {
    console.error('配置验证失败');
    process.exit(1);
}
```

## 🎮 启动脚本系统

### 脚本文件概览

#### 1. 主启动脚本 (start_websocket_dtu_system.bat)
**功能**: 完整的系统启动和管理
**特性**:
- ✅ 完整的环境检查
- ✅ 自动依赖安装
- ✅ 分步启动服务
- ✅ 交互式控制菜单
- ✅ 实时状态监控

**使用场景**: 生产环境、首次部署、完整功能验证

#### 2. 快速启动脚本 (quick_start_websocket_dtu.bat)
**功能**: 简化的快速启动
**特性**:
- ✅ 最小化环境检查
- ✅ 快速启动服务
- ✅ 自动打开浏览器
- ✅ 一键停止功能

**使用场景**: 开发环境、快速测试、频繁重启

#### 3. 状态检查脚本 (check_websocket_dtu_status.bat)
**功能**: 全面的系统诊断
**检查项目**:
1. Node.js环境版本
2. 端口占用状态 (8081, 4000)
3. Node.js进程状态
4. 项目文件完整性
5. npm依赖包状态
6. 银尔达DTU平台连通性
7. HTTP服务响应测试
8. WebSocket服务响应测试
9. 系统资源使用情况
10. 配置文件语法检查

**使用场景**: 故障诊断、健康检查、部署验证

#### 4. 系统停止脚本 (stop_websocket_dtu_system.bat)
**功能**: 安全的系统关闭
**特性**:
- ✅ 进程检测和确认
- ✅ 优雅停止机制
- ✅ 强制停止备用方案
- ✅ 临时文件清理
- ✅ 停止状态验证

**使用场景**: 系统维护、安全关闭、资源清理

### 脚本使用指南

#### 生产环境部署流程
```bash
# 1. 完整启动系统
start_websocket_dtu_system.bat

# 2. 验证系统状态
check_websocket_dtu_status.bat

# 3. 访问系统界面
# 浏览器访问: http://localhost:4000

# 4. 系统维护时安全停止
stop_websocket_dtu_system.bat
```

#### 开发环境使用流程
```bash
# 1. 快速启动
quick_start_websocket_dtu.bat

# 2. 开发调试
# 修改代码后重启服务

# 3. 状态检查
check_websocket_dtu_status.bat

# 4. 停止服务
stop_websocket_dtu_system.bat
```

### 脚本自定义

#### 修改端口配置
```bash
# 编辑配置文件
notepad code\voice-backend\config.js

# 修改端口设置
server: {
    websocket: { port: 8082 },  # 修改WebSocket端口
    http: { port: 4001 }        # 修改HTTP端口
}
```

#### 添加自定义检查
```bash
# 编辑状态检查脚本
notepad check_websocket_dtu_status.bat

# 添加自定义检查项
echo [11] 自定义检查:
# 添加您的检查逻辑

## 🔧 日常维护

### 维护计划

#### 每日维护任务 (自动化)
```bash
# 1. 系统健康检查
check_websocket_dtu_status.bat

# 2. 日志文件检查
type data\error-logs\error-*.log | findstr "ERROR"

# 3. 磁盘空间检查
dir /s data\ | findstr "bytes"

# 4. 进程状态监控
tasklist | findstr "node.exe"
```

#### 每周维护任务 (半自动)
```bash
# 1. 系统重启
stop_websocket_dtu_system.bat
start_websocket_dtu_system.bat

# 2. 日志文件轮转
move data\error-logs\*.log data\error-logs\archive\

# 3. 临时文件清理
del /q temp\*.*
del /q code\voice-backend\*.tmp

# 4. 依赖包更新检查
cd code\voice-backend
npm outdated
```

#### 每月维护任务 (手动)
1. **系统更新检查**
   - 检查Node.js版本更新
   - 检查npm包安全更新
   - 检查系统补丁

2. **性能优化**
   - 分析系统性能日志
   - 优化配置参数
   - 清理数据库和缓存

3. **安全检查**
   - 检查访问日志异常
   - 更新安全配置
   - 验证备份完整性

### 日志管理

#### 日志文件结构
```
data/
├── error-logs/
│   ├── error-2025-07-04.log      # 错误日志
│   ├── error-2025-07-03.log
│   └── archive/                  # 归档日志
├── system-logs/
│   ├── system-2025-07-04.log     # 系统日志
│   └── websocket-2025-07-04.log  # WebSocket日志
└── performance-logs/
    ├── performance-2025-07-04.log # 性能日志
    └── performance-summary.json   # 性能摘要
```

#### 日志轮转策略
```bash
# 创建日志轮转脚本 (rotate_logs.bat)
@echo off
set LOG_DIR=data\error-logs
set ARCHIVE_DIR=%LOG_DIR%\archive
set DATE_7_DAYS_AGO=%date:~-4,4%%date:~-10,2%%date:~-7,2%

# 创建归档目录
if not exist "%ARCHIVE_DIR%" mkdir "%ARCHIVE_DIR%"

# 移动7天前的日志到归档
forfiles /p "%LOG_DIR%" /m *.log /d -7 /c "cmd /c move @path %ARCHIVE_DIR%\"

# 删除30天前的归档日志
forfiles /p "%ARCHIVE_DIR%" /m *.log /d -30 /c "cmd /c del @path"
```

#### 日志分析工具
```bash
# 错误统计
findstr /c:"ERROR" data\error-logs\*.log | find /c ":"

# 警告统计
findstr /c:"WARNING" data\error-logs\*.log | find /c ":"

# 最近错误
findstr /c:"ERROR" data\error-logs\error-*.log | sort | tail -10

# 错误趋势分析
powershell -Command "Get-Content data\error-logs\*.log | Select-String 'ERROR' | Group-Object {$_.Line.Substring(0,10)} | Sort-Object Count -Descending"
```

### 性能监控

#### 系统资源监控
```bash
# CPU使用率
wmic cpu get loadpercentage /value

# 内存使用情况
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value

# 磁盘空间
wmic logicaldisk get size,freespace,caption

# 网络连接
netstat -an | findstr ":8081\|:4000"
```

#### 应用性能监控
```bash
# Node.js进程监控
tasklist /fi "imagename eq node.exe" /fo table

# WebSocket连接数
netstat -an | findstr ":8081" | find /c "ESTABLISHED"

# 响应时间测试
powershell -Command "Measure-Command { Invoke-WebRequest -Uri 'http://localhost:4000' }"

# DTU连接状态
ping dtu.yinled.com
```

#### 性能基准
- **WebSocket响应时间**: < 100ms
- **HTTP响应时间**: < 500ms
- **内存使用**: < 512MB
- **CPU使用率**: < 50%
- **磁盘使用**: < 80%

## 📊 监控与告警

### 监控指标

#### 系统级指标
```javascript
const systemMetrics = {
    cpu_usage: { threshold: 80, unit: '%' },
    memory_usage: { threshold: 85, unit: '%' },
    disk_usage: { threshold: 90, unit: '%' },
    network_latency: { threshold: 100, unit: 'ms' }
};
```

#### 应用级指标
```javascript
const applicationMetrics = {
    websocket_connections: { threshold: 100, unit: 'connections' },
    error_rate: { threshold: 5, unit: '%' },
    response_time: { threshold: 2000, unit: 'ms' },
    dtu_connection_status: { threshold: 1, unit: 'boolean' }
};
```

#### 业务级指标
```javascript
const businessMetrics = {
    print_success_rate: { threshold: 95, unit: '%' },
    voice_recognition_accuracy: { threshold: 90, unit: '%' },
    task_completion_time: { threshold: 30, unit: 'seconds' },
    queue_size: { threshold: 20, unit: 'tasks' }
};
```

### 告警机制

#### 告警级别
1. **信息 (INFO)**: 系统状态变化
2. **警告 (WARNING)**: 需要关注的问题
3. **错误 (ERROR)**: 需要立即处理的问题
4. **严重 (CRITICAL)**: 系统不可用

#### 告警规则
```javascript
const alertRules = {
    // 系统资源告警
    high_cpu_usage: {
        condition: "cpu_usage > 80%",
        level: "WARNING",
        action: "notify_admin"
    },

    // 应用错误告警
    high_error_rate: {
        condition: "error_rate > 5%",
        level: "ERROR",
        action: "immediate_notification"
    },

    // 连接异常告警
    dtu_connection_lost: {
        condition: "dtu_connection_status == false",
        level: "CRITICAL",
        action: "escalate_to_emergency"
    }
};
```

#### 告警通知方式
1. **日志记录**: 所有告警记录到日志文件
2. **控制台输出**: 实时显示在系统控制台
3. **邮件通知**: 发送告警邮件给管理员
4. **短信通知**: 严重告警发送短信

### 监控脚本

#### 创建监控脚本 (monitor_system.bat)
```bash
@echo off
title 系统监控 - WebSocket+DTU系统

:MONITOR_LOOP
cls
echo ========================================
echo WebSocket+DTU系统监控面板
echo 监控时间: %date% %time%
echo ========================================

# CPU使用率检查
for /f "tokens=2 delims==" %%i in ('wmic cpu get loadpercentage /value ^| findstr "="') do set CPU_USAGE=%%i
echo CPU使用率: %CPU_USAGE%%%

# 内存使用检查
for /f "tokens=2 delims==" %%i in ('wmic OS get FreePhysicalMemory /value ^| findstr "="') do set FREE_MEM=%%i
echo 可用内存: %FREE_MEM% KB

# 进程状态检查
tasklist | findstr "node.exe" >nul && echo Node.js进程: 运行中 || echo Node.js进程: 未运行

# 端口状态检查
netstat -an | findstr ":8081" >nul && echo WebSocket端口: 监听中 || echo WebSocket端口: 未监听
netstat -an | findstr ":4000" >nul && echo 前端端口: 监听中 || echo 前端端口: 未监听

# DTU连接检查
ping dtu.yinled.com -n 1 >nul && echo DTU平台: 连接正常 || echo DTU平台: 连接异常

echo.
echo 按 Ctrl+C 停止监控，或等待30秒自动刷新...
timeout /t 30 /nobreak >nul
goto MONITOR_LOOP
```

## 💾 备份与恢复

### 备份策略

#### 备份内容
1. **配置文件**
   - `code/voice-backend/config.js`
   - `code/voice-backend/.env`
   - `code/voice-backend/config.*.js`

2. **数据文件**
   - `data/` 目录下所有文件
   - 日志文件和统计数据
   - 用户配置和偏好设置

3. **应用代码**
   - 自定义修改的源代码
   - 第三方集成代码
   - 脚本文件

#### 备份频率
- **配置文件**: 每次修改后立即备份
- **数据文件**: 每日自动备份
- **应用代码**: 版本控制管理
- **完整系统**: 每周完整备份

### 备份脚本

#### 创建备份脚本 (backup_system.bat)
```bash
@echo off
set BACKUP_DIR=backup\%date:~-4,4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%

echo 开始系统备份...
echo 备份目录: %BACKUP_DIR%

# 创建备份目录
mkdir "%BACKUP_DIR%"

# 备份配置文件
echo 备份配置文件...
xcopy "code\voice-backend\config*.js" "%BACKUP_DIR%\config\" /Y /I
xcopy "code\voice-backend\.env*" "%BACKUP_DIR%\config\" /Y /I

# 备份数据文件
echo 备份数据文件...
xcopy "data\*" "%BACKUP_DIR%\data\" /E /Y /I

# 备份脚本文件
echo 备份脚本文件...
xcopy "*.bat" "%BACKUP_DIR%\scripts\" /Y /I

# 备份文档
echo 备份文档...
xcopy "docs\*" "%BACKUP_DIR%\docs\" /E /Y /I

# 创建备份清单
echo 创建备份清单...
dir "%BACKUP_DIR%" /s > "%BACKUP_DIR%\backup_manifest.txt"

echo 备份完成: %BACKUP_DIR%
```

### 恢复流程

#### 配置文件恢复
```bash
# 1. 停止系统
stop_websocket_dtu_system.bat

# 2. 恢复配置文件
copy backup\[备份日期]\config\*.* code\voice-backend\

# 3. 验证配置
cd code\voice-backend
node -e "console.log('配置验证:', require('./config'))"

# 4. 重启系统
start_websocket_dtu_system.bat
```

#### 数据文件恢复
```bash
# 1. 停止系统
stop_websocket_dtu_system.bat

# 2. 备份当前数据
move data data_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%

# 3. 恢复数据文件
xcopy backup\[备份日期]\data\* data\ /E /Y /I

# 4. 重启系统
start_websocket_dtu_system.bat
```

#### 完整系统恢复
```bash
# 1. 全新安装环境
# 安装Node.js和必要软件

# 2. 恢复项目文件
xcopy backup\[备份日期]\* . /E /Y /I

# 3. 安装依赖
cd code\voice-backend && npm install
cd ..\voice-frontend && npm install

# 4. 启动系统
start_websocket_dtu_system.bat
```

## 🔄 升级指南

### 升级准备

#### 升级前检查
1. **系统状态确认**
   ```bash
   check_websocket_dtu_status.bat
   ```

2. **完整备份**
   ```bash
   backup_system.bat
   ```

3. **依赖兼容性检查**
   ```bash
   cd code\voice-backend
   npm audit
   npm outdated
   ```

#### 升级类型
- **补丁升级** (1.0.1 → 1.0.2): 错误修复
- **次要升级** (1.0.x → 1.1.0): 新功能添加
- **主要升级** (1.x.x → 2.0.0): 重大变更

### 升级流程

#### 1. 补丁升级流程
```bash
# 1. 停止系统
stop_websocket_dtu_system.bat

# 2. 备份系统
backup_system.bat

# 3. 更新代码
git pull origin main

# 4. 更新依赖
cd code\voice-backend && npm update
cd ..\voice-frontend && npm update

# 5. 启动系统
start_websocket_dtu_system.bat

# 6. 验证功能
check_websocket_dtu_status.bat
```

#### 2. 次要升级流程
```bash
# 1. 停止系统并备份
stop_websocket_dtu_system.bat
backup_system.bat

# 2. 更新代码
git pull origin main

# 3. 检查配置变更
git diff HEAD~1 code/voice-backend/config.js

# 4. 更新配置文件
# 根据变更日志更新配置

# 5. 更新依赖
cd code\voice-backend && npm install
cd ..\voice-frontend && npm install

# 6. 数据库迁移 (如需要)
# 执行数据迁移脚本

# 7. 启动和验证
start_websocket_dtu_system.bat
check_websocket_dtu_status.bat
```

#### 3. 主要升级流程
```bash
# 1. 创建升级计划
# 详细阅读升级文档和变更日志

# 2. 测试环境验证
# 在测试环境完整验证升级流程

# 3. 生产环境升级
# 按照测试验证的流程执行

# 4. 回滚准备
# 准备快速回滚方案
```

### 升级验证

#### 功能验证清单
- [ ] WebSocket连接正常
- [ ] 前端界面可访问
- [ ] DTU平台连接正常
- [ ] 语音识别功能正常
- [ ] 打印控制功能正常
- [ ] 错误处理机制正常
- [ ] 性能监控正常
- [ ] 配置管理正常

#### 性能验证
```bash
# 响应时间测试
powershell -Command "Measure-Command { Invoke-WebRequest -Uri 'http://localhost:4000' }"

# 内存使用检查
tasklist /fi "imagename eq node.exe" /fo table

# 连接数测试
netstat -an | findstr ":8081" | find /c "ESTABLISHED"
```

### 回滚方案

#### 快速回滚
```bash
# 1. 停止当前系统
stop_websocket_dtu_system.bat

# 2. 恢复备份
xcopy backup\[升级前备份]\* . /E /Y /I

# 3. 重启系统
start_websocket_dtu_system.bat

# 4. 验证回滚
check_websocket_dtu_status.bat
```

#### 数据回滚
```bash
# 1. 停止系统
stop_websocket_dtu_system.bat

# 2. 恢复数据文件
rmdir /s /q data
xcopy backup\[升级前备份]\data\* data\ /E /Y /I

# 3. 恢复配置文件
copy backup\[升级前备份]\config\*.* code\voice-backend\

# 4. 重启验证
start_websocket_dtu_system.bat
```

---

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **紧急联系电话**: 400-123-4567
- **在线技术文档**: http://docs.websocket-dtu-system.com
- **问题反馈平台**: http://issues.websocket-dtu-system.com

### 支持级别
- **L1 基础支持**: 部署指导，基础配置
- **L2 技术支持**: 复杂问题分析，性能优化
- **L3 专家支持**: 架构咨询，定制化解决方案

---

## 🔧 故障排除

### 常见问题诊断

#### 1. 系统启动失败

**问题症状**:
- 启动脚本执行失败
- 服务无法启动
- 端口绑定失败

**诊断步骤**:
```bash
# 1. 检查Node.js环境
node --version
npm --version

# 2. 检查端口占用
netstat -ano | findstr ":8081"
netstat -ano | findstr ":4000"

# 3. 检查项目文件
dir code\voice-backend\server.js
dir code\voice-frontend\server.js

# 4. 检查依赖安装
cd code\voice-backend && npm list
cd ..\voice-frontend && npm list
```

**解决方案**:
```bash
# Node.js环境问题
# 重新安装Node.js 16+版本

# 端口占用问题
taskkill /f /pid [占用进程PID]

# 依赖问题
cd code\voice-backend
npm cache clean --force
rmdir /s node_modules
npm install
```

#### 2. WebSocket连接失败

**问题症状**:
- 前端无法连接WebSocket
- 连接超时或拒绝连接
- 频繁断线重连

**诊断步骤**:
```bash
# 1. 检查WebSocket服务状态
netstat -an | findstr ":8081"

# 2. 测试WebSocket连接
powershell -Command "Test-NetConnection -ComputerName localhost -Port 8081"

# 3. 检查防火墙设置
netsh advfirewall firewall show rule name="WebSocket Backend"

# 4. 查看WebSocket日志
type data\system-logs\websocket-*.log | findstr "ERROR\|WARNING"
```

**解决方案**:
```bash
# 防火墙配置
netsh advfirewall firewall add rule name="WebSocket Backend" dir=in action=allow protocol=TCP localport=8081

# 重启WebSocket服务
taskkill /f /im node.exe
start_websocket_dtu_system.bat

# 检查配置文件
notepad code\voice-backend\config.js
```

#### 3. DTU平台连接异常

**问题症状**:
- 无法连接银尔达DTU平台
- DTU设备显示离线
- 数据传输失败

**诊断步骤**:
```bash
# 1. 网络连通性测试
ping dtu.yinled.com
telnet dtu.yinled.com 8888

# 2. 检查设备信息
echo IMEI: 869080075169294
echo ICCID: 898604021024C0050919

# 3. 检查DTU配置
findstr "dtu" code\voice-backend\config.js

# 4. 查看DTU日志
type data\system-logs\*.log | findstr "DTU\|dtu"
```

**解决方案**:
```bash
# 网络问题
# 检查网络连接和DNS设置

# 设备配置问题
# 验证IMEI和ICCID是否正确

# 平台服务问题
# 联系银尔达技术支持确认平台状态
```

#### 4. 语音识别功能异常

**问题症状**:
- 语音识别无响应
- 识别准确率低
- 音频采集失败

**诊断步骤**:
```bash
# 1. 检查讯飞配置
findstr "xunfei" code\voice-backend\config.js

# 2. 测试音频设备
# 在浏览器中测试麦克风权限

# 3. 检查API密钥
echo %XUNFEI_APP_ID%
echo %XUNFEI_API_KEY%

# 4. 查看识别日志
type data\system-logs\*.log | findstr "voice\|recognition"
```

**解决方案**:
```bash
# API配置问题
# 检查讯飞API密钥是否正确和有效

# 音频设备问题
# 检查麦克风设备和浏览器权限

# 网络问题
# 检查到讯飞服务器的网络连接
```

#### 5. 打印控制异常

**问题症状**:
- 打印指令无响应
- 步进电机不工作
- 打印质量异常

**诊断步骤**:
```bash
# 1. 检查硬件连接
# 验证CH32V307和步进电机连接

# 2. 检查固件状态
# 确认CH32V307固件正常运行

# 3. 检查通信链路
# 验证Air780e到CH32V307的通信

# 4. 查看打印日志
type data\system-logs\*.log | findstr "print\|motor"
```

**解决方案**:
```bash
# 硬件问题
# 检查电源、连接线和驱动板

# 固件问题
# 重新烧录CH32V307固件

# 通信问题
# 检查Air780e模块和4G网络状态
```

### 性能问题诊断

#### 1. 系统响应慢

**诊断方法**:
```bash
# CPU使用率检查
wmic cpu get loadpercentage /value

# 内存使用检查
tasklist /fi "imagename eq node.exe" /fo table

# 磁盘I/O检查
wmic logicaldisk get size,freespace,caption

# 网络延迟检查
ping dtu.yinled.com -t
```

**优化方案**:
```bash
# 1. 增加系统资源
# 升级内存和CPU

# 2. 优化配置参数
# 调整WebSocket连接数和超时时间

# 3. 清理系统垃圾
# 删除临时文件和日志

# 4. 优化网络配置
# 使用更稳定的网络连接
```

#### 2. 内存泄漏

**诊断方法**:
```bash
# 长期内存监控
for /l %%i in (1,1,100) do (
    tasklist /fi "imagename eq node.exe" /fo csv | findstr "node.exe"
    timeout /t 60 /nobreak >nul
)

# 内存使用趋势分析
powershell -Command "Get-Process node | Select-Object ProcessName,WorkingSet,VirtualMemorySize"
```

**解决方案**:
```bash
# 1. 重启服务
stop_websocket_dtu_system.bat
start_websocket_dtu_system.bat

# 2. 检查代码
# 查找可能的内存泄漏点

# 3. 升级Node.js版本
# 使用最新稳定版本

# 4. 配置内存限制
node --max-old-space-size=512 server.js
```

### 错误代码参考

#### 系统错误代码
- **E1001**: Node.js环境未安装
- **E1002**: 项目文件缺失
- **E1003**: 依赖包安装失败
- **E1004**: 端口被占用
- **E1005**: 配置文件错误

#### 网络错误代码
- **E2001**: WebSocket连接失败
- **E2002**: DTU平台连接超时
- **E2003**: 网络不可达
- **E2004**: DNS解析失败
- **E2005**: 防火墙阻止连接

#### 应用错误代码
- **E3001**: 语音识别API错误
- **E3002**: 音频采集失败
- **E3003**: 打印指令发送失败
- **E3004**: 设备响应超时
- **E3005**: 数据格式错误

#### 硬件错误代码
- **E4001**: CH32V307通信失败
- **E4002**: Air780e模块离线
- **E4003**: 步进电机故障
- **E4004**: 传感器异常
- **E4005**: 电源供电不足

### 紧急处理流程

#### 系统完全不可用
```bash
# 1. 立即停止所有服务
taskkill /f /im node.exe

# 2. 检查系统资源
tasklist | findstr "node.exe"
netstat -an | findstr ":8081\|:4000"

# 3. 清理环境
del /q temp\*.*
del /q code\voice-backend\*.tmp

# 4. 从备份恢复
xcopy backup\[最新备份]\* . /E /Y /I

# 5. 重新启动
start_websocket_dtu_system.bat

# 6. 验证恢复
check_websocket_dtu_status.bat
```

#### 数据丢失处理
```bash
# 1. 停止写入操作
stop_websocket_dtu_system.bat

# 2. 评估数据损失
dir data\ /s
findstr "ERROR" data\error-logs\*.log

# 3. 从备份恢复
xcopy backup\[最新备份]\data\* data\ /E /Y /I

# 4. 验证数据完整性
# 检查关键数据文件

# 5. 重启系统
start_websocket_dtu_system.bat
```

### 预防措施

#### 1. 定期维护
- 每日系统健康检查
- 每周重启服务
- 每月完整备份
- 季度性能优化

#### 2. 监控告警
- 设置关键指标监控
- 配置自动告警机制
- 建立应急响应流程
- 定期演练故障处理

#### 3. 文档更新
- 记录所有故障和解决方案
- 更新故障排除文档
- 分享经验和最佳实践
- 培训运维人员

---

**📝 注意**: 本部署维护文档涵盖了WebSocket+DTU智能语音识别盲文打印系统的完整部署和运维流程。建议定期更新本文档，确保与系统实际状态保持同步。遇到本文档未涵盖的问题，请及时联系技术支持团队。

**🔄 文档版本**: v2.0.0
**📅 最后更新**: 2025年7月4日
**👥 维护团队**: WebSocket+DTU系统开发团队
**📧 技术支持**: <EMAIL>
