-- CH32V307 WebSocket+DTU混合模式Air780e脚本
-- 同时支持WebSocket服务器通信和银尔达DTU平台透传
-- 版本: 1.0.0

PROJECT = "CH32V307_WEBSOCKET_DTU"
VERSION = "1.0.0"

log.info("main", PROJECT, VERSION)

-- 系统库
sys = require("sys")
websocket = require("websocket")
socket = require("socket")
uart = require("uart")
pm = require("pm")
json = require("json")
mobile = require("mobile")

-- 配置参数
local config = {
    -- WebSocket服务器配置
    websocket = {
        server_host = "***********",       -- WebSocket服务器IP
        server_port = 8081,                 -- WebSocket服务器端口
        websocket_path = "/voice-ws",       -- WebSocket路径
        device_id = "ch32v307_dtu_01",      -- 设备ID
        reconnect_interval = 5000,          -- 重连间隔(ms)
        heartbeat_interval = 30000,         -- 心跳间隔(ms)
    },
    
    -- DTU平台配置
    dtu = {
        server_host = "dtu.yinled.com",     -- DTU服务器地址
        server_port = 8888,                 -- DTU服务器端口
        device_id = "869080075169294",      -- DTU设备ID (IMEI)
        device_key = "898604021024C0050919", -- DTU设备密钥 (ICCID)
        protocol = "tcp",                   -- 协议类型
        reconnect_interval = 10000,         -- DTU重连间隔(ms)
        heartbeat_interval = 60000,         -- DTU心跳间隔(ms)
    },
    
    -- 串口配置
    uart = {
        uart_id = 1,                        -- 串口ID
        baud_rate = 115200,                 -- 波特率
    },
    
    -- 网络配置
    network = {
        apn = "CMNET",                      -- APN设置
    },

    -- 打印控制配置
    printing = {
        enable_print_processing = true,     -- 启用打印处理
        print_buffer_size = 1024,           -- 打印缓冲区大小
        print_timeout = 30000,              -- 打印超时时间(ms)
        retry_count = 3,                    -- 重传次数
        retry_interval = 2000,              -- 重传间隔(ms)
        batch_size = 5,                     -- 批量传输大小
        compression_enabled = true,         -- 启用数据压缩
        status_report_interval = 5000,      -- 状态上报间隔(ms)
        performance_monitoring = true,      -- 启用性能监控
    }
}

-- 全局变量
local ws_client = nil
local dtu_client = nil
local is_ws_connected = false
local is_dtu_connected = false
local uart_buffer = ""
local device_status = {
    signal_strength = 0,
    battery_level = 100,
    temperature = 25,
    uptime = 0,
    ws_status = "disconnected",
    dtu_status = "disconnected"
}

-- 打印相关全局变量
local print_queue = {}                      -- 打印任务队列
local print_buffer = {}                     -- 打印数据缓冲区
local print_status = {
    current_task = nil,                     -- 当前打印任务
    queue_size = 0,                         -- 队列大小
    total_tasks = 0,                        -- 总任务数
    completed_tasks = 0,                    -- 已完成任务数
    failed_tasks = 0,                       -- 失败任务数
    last_print_time = 0,                    -- 最后打印时间
    print_speed = 0,                        -- 打印速度(字符/秒)
    error_count = 0,                        -- 错误计数
    retry_count = 0,                        -- 重试计数
}
local print_performance = {
    transmission_delay = 0,                 -- 传输延迟
    success_rate = 100,                     -- 成功率
    average_speed = 0,                      -- 平均速度
    total_bytes = 0,                        -- 总传输字节数
    compressed_bytes = 0,                   -- 压缩后字节数
    compression_ratio = 0,                  -- 压缩比
}

-- WebSocket消息发送函数
local function send_websocket(data)
    if ws_client and is_ws_connected then
        local json_str = json.encode(data)
        log.info("WebSocket发送", json_str)
        ws_client:send(json_str)
        return true
    else
        log.warn("WebSocket", "连接未建立，消息丢弃")
        return false
    end
end

-- DTU数据发送函数
local function send_dtu(data)
    if dtu_client and is_dtu_connected then
        log.info("DTU发送", string.format("%d字节", #data))
        dtu_client:send(data)
        return true
    else
        log.warn("DTU", "连接未建立，数据丢弃")
        return false
    end
end

-- 打印数据压缩函数
local function compress_print_data(data)
    if not config.printing.compression_enabled then
        return data
    end

    -- 简单的重复字符压缩算法
    local compressed = ""
    local i = 1
    while i <= #data do
        local char = string.sub(data, i, i)
        local count = 1

        -- 计算重复字符数量
        while i + count <= #data and string.sub(data, i + count, i + count) == char do
            count = count + 1
        end

        -- 如果重复超过3次，使用压缩格式
        if count > 3 then
            compressed = compressed .. string.format("@%s%d", char, count)
        else
            compressed = compressed .. string.rep(char, count)
        end

        i = i + count
    end

    return compressed
end

-- 打印任务创建函数
local function create_print_task(task_type, content, config_params)
    local task = {
        id = string.format("print_%d_%d", os.time(), math.random(1000, 9999)),
        type = task_type,
        content = content,
        config = config_params or {},
        status = "queued",
        created_time = os.time(),
        retry_count = 0,
        priority = config_params and config_params.priority or "normal",
        size = #(content.text or content.data or ""),
        compressed_size = 0,
    }

    -- 数据压缩
    if task.content.text then
        local compressed = compress_print_data(task.content.text)
        task.content.compressed_text = compressed
        task.compressed_size = #compressed
        task.compression_ratio = task.size > 0 and (task.compressed_size / task.size) or 1
    end

    return task
end

-- 打印任务队列管理
local function add_print_task(task)
    -- 检查队列大小限制
    if #print_queue >= config.printing.print_buffer_size then
        log.warn("打印队列", "队列已满，丢弃最旧任务")
        table.remove(print_queue, 1)
    end

    -- 根据优先级插入任务
    if task.priority == "high" then
        table.insert(print_queue, 1, task)
    else
        table.insert(print_queue, task)
    end

    print_status.queue_size = #print_queue
    print_status.total_tasks = print_status.total_tasks + 1

    log.info("打印队列", string.format("添加任务 %s，队列大小: %d", task.id, print_status.queue_size))
    return task.id
end

-- 获取下一个打印任务
local function get_next_print_task()
    if #print_queue > 0 then
        local task = table.remove(print_queue, 1)
        print_status.queue_size = #print_queue
        print_status.current_task = task
        return task
    end
    return nil
end

-- 打印状态上报函数
local function report_print_status(status_type, task_info)
    local status_data = {
        type = "print_status_report",
        device_id = config.websocket.device_id,
        status_type = status_type,
        timestamp = os.time(),
        print_status = {
            current_task = print_status.current_task and {
                id = print_status.current_task.id,
                type = print_status.current_task.type,
                status = print_status.current_task.status,
                progress = print_status.current_task.progress or 0,
            } or nil,
            queue_size = print_status.queue_size,
            total_tasks = print_status.total_tasks,
            completed_tasks = print_status.completed_tasks,
            failed_tasks = print_status.failed_tasks,
            error_count = print_status.error_count,
            success_rate = print_status.total_tasks > 0 and
                          (print_status.completed_tasks / print_status.total_tasks * 100) or 100,
        },
        performance = {
            transmission_delay = print_performance.transmission_delay,
            average_speed = print_performance.average_speed,
            total_bytes = print_performance.total_bytes,
            compressed_bytes = print_performance.compressed_bytes,
            compression_ratio = print_performance.compression_ratio,
        },
        task_info = task_info
    }

    -- 双通道上报
    send_websocket(status_data)

    -- DTU格式状态上报
    local dtu_status = string.format("PRINT_STATUS:%s:%s:%d:%d\r\n",
        config.dtu.device_id,
        status_type,
        print_status.queue_size,
        os.time())
    send_dtu(dtu_status)

    log.info("状态上报", string.format("类型: %s, 队列: %d", status_type, print_status.queue_size))
end

-- 打印性能监控更新
local function update_print_performance(task, transmission_time)
    if not task then return end

    -- 更新传输延迟
    print_performance.transmission_delay = transmission_time or 0

    -- 更新总字节数
    print_performance.total_bytes = print_performance.total_bytes + task.size
    print_performance.compressed_bytes = print_performance.compressed_bytes + task.compressed_size

    -- 计算压缩比
    if print_performance.total_bytes > 0 then
        print_performance.compression_ratio = print_performance.compressed_bytes / print_performance.total_bytes
    end

    -- 计算平均速度
    if transmission_time > 0 then
        local speed = task.size / (transmission_time / 1000)  -- 字节/秒
        print_performance.average_speed = (print_performance.average_speed + speed) / 2
    end

    -- 计算成功率
    if print_status.total_tasks > 0 then
        print_performance.success_rate = (print_status.completed_tasks / print_status.total_tasks) * 100
    end
end

-- 打印错误处理函数
local function handle_print_error(task, error_msg)
    if not task then return end

    task.status = "error"
    task.error_msg = error_msg
    task.retry_count = task.retry_count + 1

    print_status.error_count = print_status.error_count + 1

    log.error("打印错误", string.format("任务 %s: %s (重试: %d/%d)",
        task.id, error_msg, task.retry_count, config.printing.retry_count))

    -- 判断是否需要重试
    if task.retry_count < config.printing.retry_count then
        -- 重新加入队列
        task.status = "retry"
        add_print_task(task)
        log.info("打印重试", string.format("任务 %s 重新加入队列", task.id))
    else
        -- 标记为失败
        task.status = "failed"
        print_status.failed_tasks = print_status.failed_tasks + 1
        log.error("打印失败", string.format("任务 %s 超过最大重试次数", task.id))
    end

    -- 上报错误状态
    report_print_status("error", {
        task_id = task.id,
        error_msg = error_msg,
        retry_count = task.retry_count
    })
end

-- 串口数据解析处理
local function parse_uart_data(data)
    uart_buffer = uart_buffer .. data

    -- 查找完整的JSON消息
    local start_pos = 1
    while start_pos <= #uart_buffer do
        local json_start = string.find(uart_buffer, "{", start_pos)
        if not json_start then break end

        local json_end = string.find(uart_buffer, "}\r\n", json_start)
        if not json_end then break end

        local json_str = string.sub(uart_buffer, json_start, json_end)
        log.info("解析JSON", json_str)

        -- 尝试解析JSON
        local success, parsed_data = pcall(json.decode, json_str)
        if success and parsed_data then
            -- 添加设备标识和时间戳
            parsed_data.device_id = config.websocket.device_id
            parsed_data.timestamp = os.time()
            parsed_data.signal_strength = device_status.signal_strength

            -- 打印相关消息处理
            if parsed_data.type == "print_task_start" then
                log.info("打印开始", string.format("任务ID: %s", parsed_data.task_id or "unknown"))
                if print_status.current_task and print_status.current_task.id == parsed_data.task_id then
                    print_status.current_task.status = "printing"
                    print_status.current_task.start_time = os.time()
                    report_print_status("task_started", {task_id = parsed_data.task_id})
                end

            elseif parsed_data.type == "print_task_progress" then
                log.info("打印进度", string.format("任务ID: %s, 进度: %d%%",
                    parsed_data.task_id or "unknown", parsed_data.progress or 0))
                if print_status.current_task and print_status.current_task.id == parsed_data.task_id then
                    print_status.current_task.progress = parsed_data.progress or 0
                    print_status.current_task.status = "printing"
                    report_print_status("task_progress", {
                        task_id = parsed_data.task_id,
                        progress = parsed_data.progress
                    })
                end

            elseif parsed_data.type == "print_task_complete" then
                log.info("打印完成", string.format("任务ID: %s", parsed_data.task_id or "unknown"))
                if print_status.current_task and print_status.current_task.id == parsed_data.task_id then
                    print_status.current_task.status = "completed"
                    print_status.current_task.end_time = os.time()
                    print_status.completed_tasks = print_status.completed_tasks + 1

                    -- 更新性能统计
                    local transmission_time = print_status.current_task.end_time - print_status.current_task.start_time
                    update_print_performance(print_status.current_task, transmission_time * 1000)

                    report_print_status("task_completed", {task_id = parsed_data.task_id})
                    print_status.current_task = nil
                end

            elseif parsed_data.type == "print_task_error" then
                log.error("打印错误", string.format("任务ID: %s, 错误: %s",
                    parsed_data.task_id or "unknown", parsed_data.error or "未知错误"))
                if print_status.current_task and print_status.current_task.id == parsed_data.task_id then
                    handle_print_error(print_status.current_task, parsed_data.error or "未知错误")
                    print_status.current_task = nil
                end

            elseif parsed_data.type == "text_ack" then
                log.info("处理确认", string.format("CH32V307已处理: %s", parsed_data.forwarded_text or ""))
            end

            -- 同时发送到WebSocket服务器和DTU平台
            send_websocket(parsed_data)

            -- DTU透传：将JSON数据转换为字符串发送
            local dtu_data = json_str .. "\r\n"
            send_dtu(dtu_data)

        else
            log.warn("JSON解析失败", json_str)
        end

        start_pos = json_end + 3  -- 跳过 "}\r\n"
    end

    -- 清理已处理的数据
    if start_pos > 1 then
        uart_buffer = string.sub(uart_buffer, start_pos)
    end

    -- 防止缓冲区溢出
    if #uart_buffer > 2048 then
        log.warn("串口缓冲区", "清空溢出")
        uart_buffer = ""
    end
end

-- 串口数据接收回调
uart.on(config.uart.uart_id, "receive", function(id, len)
    local data = uart.read(config.uart.uart_id, len)
    if data and #data > 0 then
        log.info("串口接收", string.format("%d字节", #data))
        parse_uart_data(data)
    end
end)

-- DTU连接函数
local function connect_dtu()
    log.info("DTU", "连接银尔达平台...")
    
    dtu_client = socket.create(nil, config.dtu.protocol)
    dtu_client:host(config.dtu.server_host, config.dtu.server_port)
    
    dtu_client:on("connect", function()
        log.info("DTU", "连接成功！")
        is_dtu_connected = true
        device_status.dtu_status = "connected"
        
        -- 发送设备注册信息
        local register_data = string.format("DEVICE_ID:%s,KEY:%s\r\n", 
            config.dtu.device_id, config.dtu.device_key)
        dtu_client:send(register_data)
        log.info("DTU注册", "设备注册信息已发送")
        
        -- 通知WebSocket服务器DTU连接状态
        send_websocket({
            type = "dtu_status_update",
            device_id = config.websocket.device_id,
            dtu_connected = true,
            dtu_device_id = config.dtu.device_id,
            timestamp = os.time()
        })
    end)
    
    dtu_client:on("recv", function(data)
        log.info("DTU接收", string.format("%d字节: %s", #data, data))
        
        -- DTU透传：将接收到的数据转发到CH32V307
        uart.write(config.uart.uart_id, data)
        
        -- 同时通知WebSocket服务器
        send_websocket({
            type = "dtu_data_received",
            device_id = config.websocket.device_id,
            data = data,
            timestamp = os.time()
        })
    end)
    
    dtu_client:on("close", function()
        log.warn("DTU", "连接断开")
        is_dtu_connected = false
        device_status.dtu_status = "disconnected"
        
        -- 通知WebSocket服务器DTU断开
        send_websocket({
            type = "dtu_status_update",
            device_id = config.websocket.device_id,
            dtu_connected = false,
            timestamp = os.time()
        })
        
        -- 定时重连
        sys.timerStart(connect_dtu, config.dtu.reconnect_interval)
    end)
    
    dtu_client:on("error", function(err)
        log.error("DTU错误", err)
        is_dtu_connected = false
        device_status.dtu_status = "error"
        
        -- 定时重连
        sys.timerStart(connect_dtu, config.dtu.reconnect_interval)
    end)
    
    dtu_client:connect()
end

-- WebSocket连接函数
local function connect_websocket()
    log.info("WebSocket", "连接服务器...")
    
    -- 构建完整的WebSocket URL
    local ws_url = string.format("ws://%s:%d%s", 
        config.websocket.server_host, 
        config.websocket.server_port, 
        config.websocket.websocket_path)
    log.info("WebSocket", string.format("连接URL: %s", ws_url))
    
    ws_client = websocket.create(nil, ws_url)
    
    ws_client:on("open", function()
        log.info("WebSocket", "连接成功！")
        is_ws_connected = true
        device_status.ws_status = "connected"
        
        -- 发送设备注册信息
        local register_msg = {
            type = "device_register",
            device_id = config.websocket.device_id,
            device_info = {
                device_type = "ch32v307_dtu_hybrid",
                firmware_version = VERSION,
                capabilities = {"text_processing", "dtu_transparent", "dual_communication"},
                hardware = {
                    mcu = "CH32V307",
                    modem = "Air780e",
                    dtu_platform = "yinled"
                }
            },
            network_info = {
                signal_strength = device_status.signal_strength,
                network_type = "4G",
                local_ip = mobile.getIP() or "unknown"
            },
            dtu_info = {
                dtu_connected = is_dtu_connected,
                dtu_device_id = config.dtu.device_id,
                dtu_server = config.dtu.server_host
            }
        }
        send_websocket(register_msg)
        log.info("设备注册", "注册信息已发送")
    end)

    ws_client:on("message", function(data)
        log.info("WebSocket接收", data)

        -- 解析服务器发送的消息
        local success, cmd_data = pcall(json.decode, data)
        if success and cmd_data then
            -- 处理打印指令
            if cmd_data.type == "print_command" then
                log.info("收到打印指令", string.format("命令: %s", cmd_data.command or "unknown"))

                if cmd_data.command == "start_print" and cmd_data.content then
                    -- 创建打印任务
                    local print_task = create_print_task("print_text", cmd_data.content, cmd_data.config)
                    local task_id = add_print_task(print_task)

                    -- 发送打印任务到CH32V307
                    local print_json = {
                        type = "print_task",
                        task_id = task_id,
                        content = {
                            text = print_task.content.compressed_text or print_task.content.text,
                            format = cmd_data.content.format or "braille",
                            encoding = "utf-8"
                        },
                        config = print_task.config,
                        timestamp = os.time(),
                        source = "websocket_print_command"
                    }

                    local json_str = json.encode(print_json)
                    uart.write(config.uart.uart_id, json_str .. "\r\n")
                    log.info("转发打印任务", string.format("任务ID: %s", task_id))

                    -- 同时通过DTU发送
                    if is_dtu_connected then
                        send_dtu(json_str .. "\r\n")
                        log.info("DTU打印转发", "任务已通过DTU发送")
                    end

                    -- 上报任务创建状态
                    report_print_status("task_created", {task_id = task_id})

                elseif cmd_data.command == "pause_print" then
                    local control_json = {
                        type = "print_control",
                        action = "pause",
                        task_id = cmd_data.task_id,
                        timestamp = os.time()
                    }
                    local json_str = json.encode(control_json)
                    uart.write(config.uart.uart_id, json_str .. "\r\n")
                    if is_dtu_connected then send_dtu(json_str .. "\r\n") end
                    log.info("打印控制", "暂停指令已发送")

                elseif cmd_data.command == "resume_print" then
                    local control_json = {
                        type = "print_control",
                        action = "resume",
                        task_id = cmd_data.task_id,
                        timestamp = os.time()
                    }
                    local json_str = json.encode(control_json)
                    uart.write(config.uart.uart_id, json_str .. "\r\n")
                    if is_dtu_connected then send_dtu(json_str .. "\r\n") end
                    log.info("打印控制", "恢复指令已发送")

                elseif cmd_data.command == "cancel_print" then
                    local control_json = {
                        type = "print_control",
                        action = "cancel",
                        task_id = cmd_data.task_id,
                        timestamp = os.time()
                    }
                    local json_str = json.encode(control_json)
                    uart.write(config.uart.uart_id, json_str .. "\r\n")
                    if is_dtu_connected then send_dtu(json_str .. "\r\n") end
                    log.info("打印控制", "取消指令已发送")
                end

            -- 处理打印状态查询
            elseif cmd_data.type == "print_status_request" then
                log.info("收到状态查询", string.format("查询类型: %s", cmd_data.query_type or "all"))
                report_print_status("status_query_response", {
                    query_type = cmd_data.query_type,
                    request_id = cmd_data.request_id
                })

            -- 处理打印队列管理
            elseif cmd_data.type == "print_queue_management" then
                log.info("收到队列管理", string.format("操作: %s", cmd_data.operation or "unknown"))

                if cmd_data.operation == "clear_queue" then
                    print_queue = {}
                    print_status.queue_size = 0
                    log.info("队列管理", "打印队列已清空")
                    report_print_status("queue_cleared", {})

                elseif cmd_data.operation == "get_queue" then
                    local queue_info = {}
                    for i, task in ipairs(print_queue) do
                        table.insert(queue_info, {
                            id = task.id,
                            type = task.type,
                            status = task.status,
                            priority = task.priority,
                            size = task.size,
                            created_time = task.created_time
                        })
                    end

                    send_websocket({
                        type = "print_queue_info",
                        device_id = config.websocket.device_id,
                        queue = queue_info,
                        queue_size = print_status.queue_size,
                        timestamp = os.time()
                    })
                end

            -- 处理文字消息（语音识别结果）
            elseif cmd_data.type == "text_message" or cmd_data.recognized_text then
                local text_content = cmd_data.text or cmd_data.recognized_text or cmd_data.content or ""
                log.info("收到语音指令", string.format("内容: %s", text_content))

                -- 构建JSON格式发送到CH32V307
                local text_json = {
                    type = cmd_data.recognized_text and "voice_command" or "text_message",
                    text = text_content,
                    confidence = cmd_data.confidence or 0.8,
                    timestamp = os.time(),
                    source = cmd_data.recognized_text and "voice_recognition" or "websocket_client"
                }

                local json_str = json.encode(text_json)
                uart.write(config.uart.uart_id, json_str .. "\r\n")
                log.info("转发消息", string.format("发送到CH32V307: %s", text_content))

                -- 同时通过DTU透传发送
                if is_dtu_connected then
                    send_dtu(json_str .. "\r\n")
                    log.info("DTU透传", "消息已通过DTU发送")
                end

            -- 处理DTU相关指令
            elseif cmd_data.type == "send_dtu_text" then
                local dtu_text = cmd_data.text or ""
                log.info("收到DTU文字指令", string.format("内容: %s", dtu_text))

                -- 构建DTU消息格式
                local dtu_json = {
                    type = "dtu_text_message",
                    text = dtu_text,
                    timestamp = os.time(),
                    source = "websocket_dtu_command"
                }

                local json_str = json.encode(dtu_json)

                -- 发送到CH32V307
                uart.write(config.uart.uart_id, json_str .. "\r\n")

                -- 通过DTU透传发送
                if is_dtu_connected then
                    send_dtu(json_str .. "\r\n")

                    -- 发送DTU文字发送结果
                    send_websocket({
                        type = "dtu_text_result",
                        success = true,
                        text = dtu_text,
                        timestamp = os.time()
                    })
                else
                    -- DTU未连接，发送错误结果
                    send_websocket({
                        type = "dtu_text_result",
                        success = false,
                        error = "DTU未连接",
                        text = dtu_text,
                        timestamp = os.time()
                    })
                end

            -- 处理DTU状态查询
            elseif cmd_data.type == "get_dtu_status" then
                log.info("收到DTU状态查询")

                -- 发送DTU状态信息
                send_websocket({
                    type = "dtu_status",
                    connectionStatus = {
                        connected = is_dtu_connected,
                        server = config.dtu.server_host,
                        port = config.dtu.server_port
                    },
                    deviceStatus = {
                        device_id = config.dtu.device_id,
                        signal_strength = device_status.signal_strength,
                        uptime = device_status.uptime,
                        last_heartbeat = os.time()
                    },
                    timestamp = os.time()
                })

            -- 处理控制命令
            elseif cmd_data.type == "control_command" then
                local cmd = cmd_data.command
                local full_cmd = cmd .. "\r\n"
                uart.write(config.uart.uart_id, full_cmd)
                log.info("转发控制", string.format("命令: %s", cmd))

                -- 同时通过DTU发送
                if is_dtu_connected then
                    send_dtu(full_cmd)
                end
            end
        else
            log.warn("WebSocket消息解析失败", data)
        end
    end)

    ws_client:on("close", function(code, reason)
        log.warn("WebSocket", string.format("连接关闭: %s (%s)", code, reason or "未知原因"))
        is_ws_connected = false
        device_status.ws_status = "disconnected"
        ws_client = nil

        -- 定时重连
        sys.timerStart(function()
            log.info("重连", "重新连接WebSocket...")
            connect_websocket()
        end, config.websocket.reconnect_interval)
    end)

    ws_client:on("error", function(err)
        log.error("WebSocket错误", err)
        is_ws_connected = false
        device_status.ws_status = "error"
        ws_client = nil

        -- 定时重连
        sys.timerStart(function()
            log.info("重连", "重新连接WebSocket...")
            connect_websocket()
        end, config.websocket.reconnect_interval)
    end)

    -- 开始连接
    ws_client:connect()
end

-- 网络状态监控
local function monitor_network()
    -- 更新设备状态
    device_status.signal_strength = mobile.rssi()
    device_status.uptime = mcu.tick64() // 1000

    log.info("网络状态", string.format("信号强度: %d dBm, WebSocket: %s, DTU: %s",
        device_status.signal_strength,
        device_status.ws_status,
        device_status.dtu_status))

    -- 检查网络连接
    if not socket.isReady() then
        log.warn("网络", "网络未就绪")
        return
    end

    -- 检查WebSocket连接
    if not is_ws_connected then
        log.info("重连", "检测到WebSocket断开，正在重连...")
        connect_websocket()
    end

    -- 检查DTU连接
    if not is_dtu_connected then
        log.info("重连", "检测到DTU断开，正在重连...")
        connect_dtu()
    end
end

-- WebSocket心跳发送
local function send_websocket_heartbeat()
    if is_ws_connected then
        local heartbeat_msg = {
            type = "device_heartbeat",
            device_id = config.websocket.device_id,
            timestamp = os.time(),
            status = {
                uptime = device_status.uptime,
                signal_strength = device_status.signal_strength,
                battery_level = device_status.battery_level,
                temperature = device_status.temperature,
                memory_usage = collectgarbage("count"),
                network_ready = socket.isReady(),
                websocket_connected = is_ws_connected,
                dtu_connected = is_dtu_connected,
                dtu_device_id = config.dtu.device_id
            }
        }
        send_websocket(heartbeat_msg)
        log.info("WebSocket心跳", "发送成功")
    else
        log.info("WebSocket心跳", "连接未建立，跳过发送")
    end
end

-- DTU心跳发送
local function send_dtu_heartbeat()
    if is_dtu_connected then
        -- DTU心跳格式（根据银尔达平台要求）
        local heartbeat_data = string.format("HEARTBEAT:%s:%d\r\n",
            config.dtu.device_id, os.time())
        send_dtu(heartbeat_data)
        log.info("DTU心跳", "发送成功")
    else
        log.info("DTU心跳", "DTU未连接，跳过发送")
    end
end

-- 打印性能监控和状态上报
local function print_performance_monitor()
    if not config.printing.performance_monitoring then
        return
    end

    -- 定期上报打印状态
    if config.printing.enable_print_processing then
        report_print_status("periodic_report", {
            monitor_time = os.time(),
            queue_health = print_status.queue_size < config.printing.print_buffer_size * 0.8,
            performance_summary = {
                avg_speed = print_performance.average_speed,
                success_rate = print_performance.success_rate,
                compression_ratio = print_performance.compression_ratio,
                total_processed = print_status.total_tasks,
            }
        })
    end

    -- 检查打印任务超时
    if print_status.current_task then
        local current_time = os.time()
        local task_duration = current_time - (print_status.current_task.start_time or current_time)

        if task_duration > (config.printing.print_timeout / 1000) then
            log.warn("打印超时", string.format("任务 %s 超时，强制结束", print_status.current_task.id))
            handle_print_error(print_status.current_task, "打印任务超时")
            print_status.current_task = nil
        end
    end

    -- 自动处理队列中的任务
    if not print_status.current_task and #print_queue > 0 then
        local next_task = get_next_print_task()
        if next_task then
            log.info("自动处理", string.format("开始处理任务 %s", next_task.id))
            -- 这里可以添加自动发送逻辑，或者等待外部触发
        end
    end

    log.info("性能监控", string.format("队列: %d, 完成: %d, 失败: %d, 成功率: %.1f%%",
        print_status.queue_size,
        print_status.completed_tasks,
        print_status.failed_tasks,
        print_performance.success_rate))
end

-- 主程序
sys.taskInit(function()
    log.info("系统", "CH32V307 WebSocket+DTU混合系统启动")
    log.info("配置", string.format("WebSocket: %s:%d%s",
        config.websocket.server_host,
        config.websocket.server_port,
        config.websocket.websocket_path))
    log.info("配置", string.format("DTU: %s:%d (设备ID: %s)",
        config.dtu.server_host,
        config.dtu.server_port,
        config.dtu.device_id))

    -- 初始化串口
    uart.setup(config.uart.uart_id, config.uart.baud_rate, 8, 1, uart.PAR_NONE)
    log.info("串口", string.format("初始化完成 %d bps", config.uart.baud_rate))

    -- 等待网络就绪
    log.info("网络", "等待网络连接...")
    sys.waitUntil("IP_READY", 30000)

    if socket.isReady() then
        log.info("网络", "网络连接成功")
        log.info("IP地址", mobile.getIP() or "获取失败")

        -- 延时后连接服务器
        sys.wait(3000)

        -- 同时连接WebSocket和DTU
        connect_websocket()
        sys.wait(2000)  -- 间隔2秒
        connect_dtu()

        -- 启动定时器
        -- 网络状态监控 (15秒)
        sys.timerLoopStart(monitor_network, 15000)

        -- WebSocket心跳 (30秒)
        sys.timerLoopStart(send_websocket_heartbeat, config.websocket.heartbeat_interval)

        -- DTU心跳 (60秒)
        sys.timerLoopStart(send_dtu_heartbeat, config.dtu.heartbeat_interval)

        -- 打印性能监控 (5秒)
        if config.printing.enable_print_processing then
            sys.timerLoopStart(print_performance_monitor, config.printing.status_report_interval)
            log.info("打印监控", "打印性能监控已启动")
        end

        log.info("系统", "所有服务已启动，系统运行正常")
        log.info("打印功能", string.format("打印处理: %s, 缓冲区: %d, 压缩: %s",
            config.printing.enable_print_processing and "启用" or "禁用",
            config.printing.print_buffer_size,
            config.printing.compression_enabled and "启用" or "禁用"))

    else
        log.error("网络", "网络连接失败，系统重启")
        pm.reboot()
    end
end)

-- 系统运行
sys.run()
