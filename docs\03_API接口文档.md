# WebSocket+DTU智能语音识别盲文打印系统 - API接口文档

## 📋 目录

1. [API概述](#API概述)
2. [WebSocket基础API](#WebSocket基础API)
3. [🆕 错误处理API](#错误处理API)
4. [🆕 性能监控API](#性能监控API)
5. [🆕 打印控制API](#打印控制API)
6. [DTU平台API](#DTU平台API)
7. [串口通信协议](#串口通信协议)
8. [错误代码定义](#错误代码定义)
9. [API使用示例](#API使用示例)

## 🎯 API概述

### 系统架构
```
前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
```

### 🆕 新增功能模块
- **错误处理系统**: 智能错误检测、分类和自动恢复
- **性能监控系统**: 实时性能指标监控和报告生成
- **打印控制系统**: 高级打印任务管理和控制

### 通信层级
1. **前端 ↔ WebSocket服务器**: WebSocket协议 (ws://localhost:8081/voice-ws)
2. **WebSocket服务器 ↔ DTU平台**: HTTP/HTTPS + DTU API (dtu.yinled.com:8888)
3. **DTU平台 ↔ Air780e**: 4G网络 + DTU协议
4. **Air780e ↔ CH32V307**: 串口通信 + JSON协议

### API版本信息
- **API版本**: v2.0
- **协议版本**: WebSocket 13
- **支持格式**: JSON
- **字符编码**: UTF-8

## 🌐 WebSocket基础API

### 连接信息
- **服务器地址**: `ws://localhost:8081/voice-ws`
- **协议版本**: WebSocket 13
- **心跳间隔**: 30秒
- **连接超时**: 60秒
- **最大连接数**: 100

### 1. 连接建立与认证

#### 客户端连接
```javascript
const ws = new WebSocket('ws://localhost:8081/voice-ws');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
    // 发送认证信息
    ws.send(JSON.stringify({
        type: 'auth',
        token: 'client_token',
        client_id: 'web_client_001',
        version: '2.0'
    }));
};
```

#### 服务器认证响应
```json
{
    "type": "auth_response",
    "status": "success",
    "client_id": "web_client_001",
    "session_id": "sess_1699123456789",
    "server_time": 1699123456789,
    "capabilities": [
        "voice_recognition",
        "print_control",
        "error_handling",
        "performance_monitoring"
    ]
}
```

### 2. 语音识别API

#### 音频数据上传
```javascript
const audioMessage = {
    type: "audio_data",
    data: arrayBuffer,      // 音频数据 (ArrayBuffer)
    format: "pcm",          // 音频格式: pcm, wav, mp3
    sampleRate: 16000,      // 采样率: 8000, 16000, 44100
    channels: 1,            // 声道数: 1, 2
    bitDepth: 16,           // 位深度: 8, 16, 24, 32
    timestamp: Date.now(),  // 时间戳
    session_id: "sess_1699123456789"
};

ws.send(JSON.stringify(audioMessage));
```

#### 语音识别结果
```json
{
    "type": "voice_result",
    "recognized_text": "你好世界",
    "confidence": 0.95,
    "language": "zh-cn",
    "duration": 2.5,
    "dtu_status": "sent_to_device",
    "target_device": "braille_printer_001",
    "command_id": "cmd_1699123456789",
    "timestamp": 1699123456789,
    "processing_time": 1.2,
    "word_count": 4
}
```

### 3. 基础设备控制API

#### 发送打印指令
```javascript
const printCommand = {
    type: "print_command",
    text: "你好世界",
    device_id: "braille_printer_001",
    print_options: {
        font_size: "normal",        // normal, large, small
        line_spacing: 2.5,          // 行间距 (mm)
        char_spacing: 6.0,          // 字符间距 (mm)
        print_speed: "medium",      // slow, medium, fast
        quality: "high"             // draft, normal, high
    },
    priority: "normal",             // low, normal, high, urgent
    command_id: "cmd_" + Date.now()
};

ws.send(JSON.stringify(printCommand));
```

#### 设备状态更新
```json
{
    "type": "device_status",
    "device_id": "braille_printer_001",
    "status": {
        "connection": "online",
        "printer_status": "printing",
        "audio_status": "ready",
        "position": {
            "x": 120.5,
            "y": 85.0
        },
        "progress": 65,
        "queue_size": 3,
        "error_count": 0,
        "last_activity": 1699123456789
    },
    "timestamp": 1699123456789
}
```

## 🛡️ 错误处理API

### 1. 错误监控与查询

#### 获取错误统计
```javascript
const errorStatsRequest = {
    type: "error_stats",
    time_range: "1h",           // 1h, 6h, 1d, 1w
    include_resolved: true,     // 是否包含已解决的错误
    error_levels: ["error", "warning"]  // 错误级别过滤
};

ws.send(JSON.stringify(errorStatsRequest));
```

#### 错误统计响应
```json
{
    "type": "error_stats_response",
    "stats": {
        "total_errors": 15,
        "resolved_errors": 12,
        "pending_errors": 3,
        "error_rate": 2.3,
        "recovery_success_rate": 80.0,
        "categories": {
            "hardware": 5,
            "communication": 7,
            "application": 2,
            "system": 1
        },
        "recent_errors": [
            {
                "id": "err_1699123456789",
                "code": 2001,
                "message": "DTU连接丢失",
                "level": "warning",
                "timestamp": 1699123456789,
                "status": "resolved",
                "recovery_strategy": "retry"
            }
        ]
    },
    "timestamp": 1699123456789
}
```

### 2. 错误恢复控制

#### 手动错误恢复
```javascript
const recoveryRequest = {
    type: "error_recovery",
    error_id: "err_1699123456789",
    recovery_action: "retry",   // retry, reset, ignore, manual
    parameters: {
        max_attempts: 3,
        retry_interval: 5000
    }
};

ws.send(JSON.stringify(recoveryRequest));
```

#### 错误恢复响应
```json
{
    "type": "error_recovery_response",
    "error_id": "err_1699123456789",
    "status": "success",
    "recovery_action": "retry",
    "attempts_used": 2,
    "recovery_time": 10.5,
    "result": {
        "success": true,
        "message": "DTU连接已恢复",
        "new_status": "connected"
    },
    "timestamp": 1699123456789
}
```

### 3. 错误配置管理

#### 更新错误处理配置
```javascript
const configUpdate = {
    type: "error_config_update",
    config: {
        auto_recovery: true,
        max_retry_attempts: 3,
        retry_interval: 5000,
        alert_thresholds: {
            error_rate: 5.0,
            response_time: 5000
        },
        recovery_strategies: {
            "2001": { strategy: "retry", max_attempts: 5 },
            "1001": { strategy: "reset", max_attempts: 2 }
        }
    }
};

ws.send(JSON.stringify(configUpdate));
```

## 📊 性能监控API

### 1. 实时性能数据

#### 获取性能摘要
```javascript
const performanceRequest = {
    type: "performance_query",
    request: "summary",         // summary, historical, alerts, statistics
    include_details: true
};

ws.send(JSON.stringify(performanceRequest));
```

#### 性能摘要响应
```json
{
    "type": "performance_response",
    "data": {
        "realtime": {
            "print_speed": 12.5,        // 字符/秒
            "queue_size": 3,            // 当前队列大小
            "active_task_count": 1,     // 活跃任务数
            "error_rate": 1.2,          // 错误率 (%)
            "response_time": 850,       // 平均响应时间 (ms)
            "cpu_usage": 25.6,          // CPU使用率 (%)
            "memory_usage": 45.2        // 内存使用率 (%)
        },
        "statistics": {
            "total_tasks": 156,
            "completed_tasks": 148,
            "failed_tasks": 8,
            "average_speed": 11.8,
            "peak_speed": 18.2,
            "uptime": 86400000
        },
        "recent_alerts": [
            {
                "id": "alert_1699123456789",
                "type": "speed_drop",
                "message": "打印速度下降30%",
                "severity": "warning",
                "timestamp": 1699123456789
            }
        ]
    },
    "timestamp": 1699123456789
}
```

### 2. 历史性能数据

#### 获取历史数据
```javascript
const historicalRequest = {
    type: "performance_query",
    request: "historical",
    time_range: "1d",           // 1h, 6h, 1d, 1w, 1m
    sample_count: 100,          // 采样点数量
    metrics: ["speed", "error_rate", "response_time"]
};

ws.send(JSON.stringify(historicalRequest));
```

#### 历史数据响应
```json
{
    "type": "performance_historical_response",
    "data": {
        "time_range": "1d",
        "sample_interval": 864000,  // 采样间隔 (ms)
        "metrics": {
            "speed": [
                { "timestamp": 1699037056789, "value": 11.2 },
                { "timestamp": 1699123456789, "value": 12.5 }
            ],
            "error_rate": [
                { "timestamp": 1699037056789, "value": 0.8 },
                { "timestamp": 1699123456789, "value": 1.2 }
            ],
            "response_time": [
                { "timestamp": 1699037056789, "value": 920 },
                { "timestamp": 1699123456789, "value": 850 }
            ]
        },
        "summary": {
            "average_speed": 11.85,
            "peak_speed": 15.2,
            "min_speed": 8.1,
            "average_error_rate": 1.0,
            "peak_error_rate": 3.2
        }
    },
    "timestamp": 1699123456789
}
```

### 3. 性能报告生成

#### 生成性能报告
```javascript
const reportRequest = {
    type: "performance_report",
    time_range: "1w",           // 报告时间范围
    format: "json",             // json, pdf, excel
    include_charts: true,       // 是否包含图表
    sections: [
        "summary",
        "trends",
        "alerts",
        "recommendations"
    ]
};

ws.send(JSON.stringify(reportRequest));
```

#### 性能报告响应
```json
{
    "type": "performance_report_response",
    "report": {
        "id": "report_1699123456789",
        "time_range": "1w",
        "generated_at": 1699123456789,
        "summary": {
            "total_tasks": 1250,
            "success_rate": 94.4,
            "average_speed": 12.1,
            "total_errors": 70,
            "uptime_percentage": 99.2
        },
        "trends": {
            "speed_trend": "stable",
            "error_trend": "decreasing",
            "usage_trend": "increasing"
        },
        "alerts": [
            {
                "type": "performance_degradation",
                "description": "周三下午打印速度下降15%",
                "impact": "medium",
                "recommendation": "检查硬件连接"
            }
        ],
        "recommendations": [
            "建议在低峰期进行系统维护",
            "考虑升级硬件以提高处理能力",
            "优化打印队列管理策略"
        ]
    },
    "download_url": "/api/reports/report_1699123456789.pdf",
    "timestamp": 1699123456789
}
```

## 🎮 打印控制API

### 1. 高级打印任务管理

#### 打印任务控制
```javascript
const printControlCommand = {
    type: "print_control",
    action: "pause",            // pause, resume, cancel, emergency_stop
    task_id: "task_1699123456789",
    reason: "用户请求暂停",
    parameters: {
        safe_stop: true,        // 安全停止
        save_progress: true     // 保存进度
    }
};

ws.send(JSON.stringify(printControlCommand));
```

#### 打印控制响应
```json
{
    "type": "print_control_response",
    "action": "pause",
    "task_id": "task_1699123456789",
    "status": "success",
    "previous_state": "printing",
    "current_state": "paused",
    "progress_saved": true,
    "resume_position": {
        "character_index": 45,
        "line_number": 3,
        "x_position": 120.5,
        "y_position": 85.0
    },
    "timestamp": 1699123456789
}
```

### 2. 打印队列管理

#### 获取打印队列
```javascript
const queueRequest = {
    type: "print_queue",
    action: "get",
    include_completed: false,   // 是否包含已完成任务
    limit: 20                   // 返回任务数量限制
};

ws.send(JSON.stringify(queueRequest));
```

#### 打印队列响应
```json
{
    "type": "print_queue_response",
    "queue": [
        {
            "task_id": "task_1699123456789",
            "text": "你好世界",
            "status": "printing",
            "priority": "normal",
            "progress": 65,
            "estimated_time": 120,
            "created_at": 1699123456789,
            "started_at": 1699123456800,
            "character_count": 4,
            "current_position": 2
        },
        {
            "task_id": "task_1699123456790",
            "text": "测试打印内容",
            "status": "pending",
            "priority": "high",
            "progress": 0,
            "estimated_time": 180,
            "created_at": 1699123456790,
            "character_count": 6,
            "queue_position": 1
        }
    ],
    "queue_stats": {
        "total_tasks": 2,
        "active_tasks": 1,
        "pending_tasks": 1,
        "estimated_total_time": 300
    },
    "timestamp": 1699123456789
}
```

### 3. 批量任务操作

#### 批量任务控制
```javascript
const batchControlCommand = {
    type: "batch_print_control",
    action: "cancel_all",       // pause_all, resume_all, cancel_all, clear_queue
    filter: {
        status: ["pending", "paused"],
        priority: ["low", "normal"]
    },
    reason: "系统维护"
};

ws.send(JSON.stringify(batchControlCommand));
```

#### 批量控制响应
```json
{
    "type": "batch_control_response",
    "action": "cancel_all",
    "affected_tasks": [
        {
            "task_id": "task_1699123456790",
            "previous_status": "pending",
            "new_status": "cancelled",
            "result": "success"
        },
        {
            "task_id": "task_1699123456791",
            "previous_status": "paused",
            "new_status": "cancelled",
            "result": "success"
        }
    ],
    "summary": {
        "total_affected": 2,
        "successful": 2,
        "failed": 0
    },
    "timestamp": 1699123456789
}
```

## 📡 DTU平台API

### 平台信息
- **平台地址**: `dtu.yinled.com:8888`
- **协议**: HTTP/HTTPS
- **认证方式**: IMEI + ICCID
- **数据格式**: JSON
- **API版本**: v2.0

### 1. 设备认证

#### 认证请求
```http
POST /api/v2/device/auth
Host: dtu.yinled.com:8888
Content-Type: application/json

{
    "imei": "869080075169294",
    "iccid": "898604021024C0050919",
    "device_type": "M100P",
    "firmware_version": "2.0.0",
    "capabilities": [
        "voice_recognition",
        "print_control",
        "error_handling",
        "performance_monitoring"
    ]
}
```

#### 认证响应
```json
{
    "status": "success",
    "device_id": "braille_printer_001",
    "access_token": "dtu_token_1699123456789",
    "refresh_token": "refresh_token_1699123456789",
    "expires_in": 3600,
    "server_time": 1699123456789,
    "assigned_server": "dtu-server-01.yinled.com"
}
```

### 2. 🆕 增强数据上行

#### 设备状态上报 (增强版)
```http
POST /api/v2/device/status
Host: dtu.yinled.com:8888
Authorization: Bearer dtu_token_1699123456789
Content-Type: application/json

{
    "device_id": "braille_printer_001",
    "status": {
        "printer_status": "ready",
        "audio_status": "recording",
        "position": {"x": 0, "y": 0},
        "temperature": 25.6,
        "humidity": 45.2,
        "error_count": 0,
        "performance_metrics": {
            "print_speed": 12.5,
            "queue_size": 3,
            "uptime": 86400
        }
    },
    "timestamp": 1699123456789
}
```

### 3. 🆕 增强数据下行

#### 接收控制指令 (增强版)
```http
GET /api/v2/device/commands?device_id=braille_printer_001&include_control=true
Host: dtu.yinled.com:8888
Authorization: Bearer dtu_token_1699123456789
```

#### 指令响应 (增强版)
```json
{
    "commands": [
        {
            "command_id": "cmd_1699123456789",
            "type": "print_braille",
            "data": {
                "text": "你好世界",
                "confidence": 0.95,
                "options": {
                    "font_size": "normal",
                    "line_spacing": 2.5,
                    "print_speed": "medium"
                }
            },
            "priority": "normal",
            "timestamp": 1699123456789
        },
        {
            "command_id": "cmd_1699123456790",
            "type": "system_control",
            "data": {
                "action": "performance_report",
                "time_range": "1h"
            },
            "timestamp": 1699123456790
        }
    ],
    "control_commands": [
        {
            "command_id": "ctrl_1699123456789",
            "type": "error_recovery",
            "data": {
                "error_id": "err_1699123456789",
                "recovery_action": "retry"
            },
            "timestamp": 1699123456789
        }
    ]
}
```

## 🔌 串口通信协议

### 通信参数
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无
- **协议版本**: v2.0

### 1. 🆕 增强数据格式

#### 基本消息结构 (v2.0)
```json
{
    "version": "2.0",
    "type": "message_type",
    "data": {},
    "timestamp": 1699123456789,
    "sequence": 12345,
    "checksum": "crc32_value",
    "priority": "normal"
}
```

### 2. 🆕 增强控制指令

#### 打印指令 (增强版)
```json
{
    "version": "2.0",
    "type": "VOICE_PRINT",
    "data": {
        "text": "你好世界",
        "confidence": 0.95,
        "command_id": "cmd_1699123456789",
        "task_id": "task_1699123456789",
        "options": {
            "font_size": "normal",
            "line_spacing": 2.5,
            "char_spacing": 6.0,
            "print_speed": "medium",
            "quality": "high"
        },
        "control_options": {
            "pauseable": true,
            "resumeable": true,
            "cancellable": true
        }
    },
    "priority": "normal",
    "timestamp": 1699123456789,
    "sequence": 12345
}
```

#### 🆕 错误处理指令
```json
{
    "version": "2.0",
    "type": "ERROR_RECOVERY",
    "data": {
        "error_id": "err_1699123456789",
        "error_code": 1001,
        "recovery_action": "reset",
        "parameters": {
            "component": "stepper_motor_x",
            "reset_type": "soft"
        }
    },
    "timestamp": 1699123456789,
    "sequence": 12346
}
```

#### 🆕 性能监控指令
```json
{
    "version": "2.0",
    "type": "PERFORMANCE_MONITOR",
    "data": {
        "action": "start_monitoring",
        "metrics": ["speed", "position", "temperature"],
        "sample_interval": 1000,
        "duration": 3600000
    },
    "timestamp": 1699123456789,
    "sequence": 12347
}
```

### 3. 🆕 增强状态上报

#### 设备状态 (增强版)
```json
{
    "version": "2.0",
    "type": "STATUS_UPDATE",
    "data": {
        "printer_status": "printing",
        "audio_status": "ready",
        "position": {
            "x": 120.5,
            "y": 85.0
        },
        "progress": 65,
        "error_code": 0,
        "performance_metrics": {
            "current_speed": 12.5,
            "temperature": 25.6,
            "vibration_level": 0.2
        },
        "task_info": {
            "current_task_id": "task_1699123456789",
            "characters_printed": 45,
            "estimated_remaining_time": 120
        }
    },
    "timestamp": 1699123456789,
    "sequence": 12348
}
```

#### 🆕 性能数据上报
```json
{
    "version": "2.0",
    "type": "PERFORMANCE_DATA",
    "data": {
        "metrics": {
            "print_speed": 12.5,
            "motor_temperature": 28.3,
            "vibration_x": 0.1,
            "vibration_y": 0.15,
            "power_consumption": 2.8
        },
        "task_performance": {
            "task_id": "task_1699123456789",
            "characters_per_second": 12.5,
            "accuracy_rate": 99.8,
            "error_count": 0
        }
    },
    "timestamp": 1699123456789,
    "sequence": 12349
}
```

## ❌ 错误代码定义

### 🆕 扩展错误代码体系

#### 硬件错误 (1000-1999)
- **1001**: 步进电机X轴故障
- **1002**: 步进电机Y轴故障
- **1003**: 传感器故障
- **1004**: 电源供应异常
- **1005**: 机械卡纸
- **🆕 1006**: 温度传感器异常
- **🆕 1007**: 振动传感器异常
- **🆕 1008**: 电机驱动器故障

#### 通信错误 (2000-2999)
- **2001**: DTU连接丢失
- **2002**: WebSocket连接断开
- **2003**: 串口通信失败
- **2004**: 网络超时
- **2005**: 数据传输错误
- **🆕 2006**: 认证令牌过期
- **🆕 2007**: API调用频率超限
- **🆕 2008**: 数据格式验证失败

#### 应用错误 (3000-3999)
- **3001**: 无效打印数据
- **3002**: 队列溢出
- **3003**: 任务超时
- **3004**: 配置错误
- **3005**: 权限不足
- **🆕 3006**: 任务冲突
- **🆕 3007**: 资源不足
- **🆕 3008**: 版本不兼容

#### 系统错误 (4000-4999)
- **4001**: 内存不足
- **4002**: 文件系统错误
- **4003**: 服务不可用
- **4004**: 数据库连接失败
- **🆕 4005**: 性能监控异常
- **🆕 4006**: 错误处理系统故障
- **🆕 4007**: 配置文件损坏

### 🆕 错误响应格式 (v2.0)

```json
{
    "type": "error",
    "version": "2.0",
    "error": {
        "id": "err_1699123456789",
        "code": 1003,
        "category": "hardware",
        "level": "error",
        "message": "传感器故障",
        "details": "位置传感器无响应",
        "timestamp": 1699123456789,
        "request_id": "req_1699123456789",
        "context": {
            "component": "position_sensor",
            "operation": "calibration",
            "task_id": "task_1699123456789"
        },
        "recovery_suggestion": {
            "strategy": "reset",
            "auto_recoverable": true,
            "estimated_time": 30
        }
    }
}
```

## 💻 API使用示例

### 1. 🆕 完整WebSocket客户端示例 (v2.0)

```javascript
class WebSocketDTUClientV2 {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.connected = false;
        this.version = '2.0';
        this.sessionId = null;
        this.sequenceNumber = 0;
    }

    connect() {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = (event) => {
            this.connected = true;
            console.log('WebSocket连接已建立');
            this.authenticate();
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };

        this.ws.onclose = (event) => {
            this.connected = false;
            console.log('WebSocket连接已关闭');
            // 自动重连
            setTimeout(() => this.connect(), 5000);
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    authenticate() {
        const authMessage = {
            type: 'auth',
            version: this.version,
            token: 'client_token',
            client_id: 'web_client_001',
            capabilities: [
                'voice_recognition',
                'print_control',
                'error_handling',
                'performance_monitoring'
            ]
        };
        this.send(authMessage);
    }

    send(message) {
        if (this.connected && this.ws.readyState === WebSocket.OPEN) {
            message.sequence = ++this.sequenceNumber;
            message.timestamp = Date.now();
            if (this.sessionId) {
                message.session_id = this.sessionId;
            }
            this.ws.send(JSON.stringify(message));
        }
    }

    // 🆕 错误处理API调用
    getErrorStats(timeRange = '1h') {
        const message = {
            type: 'error_stats',
            time_range: timeRange,
            include_resolved: true
        };
        this.send(message);
    }

    // 🆕 性能监控API调用
    getPerformanceData(request = 'summary') {
        const message = {
            type: 'performance_query',
            request: request,
            include_details: true
        };
        this.send(message);
    }

    // 🆕 打印控制API调用
    controlPrintTask(taskId, action, parameters = {}) {
        const message = {
            type: 'print_control',
            action: action,
            task_id: taskId,
            parameters: parameters
        };
        this.send(message);
    }

    handleMessage(message) {
        switch (message.type) {
            case 'auth_response':
                this.sessionId = message.session_id;
                console.log('认证成功:', message);
                break;
            case 'voice_result':
                console.log('语音识别结果:', message.recognized_text);
                break;
            case 'device_status':
                console.log('设备状态更新:', message.status);
                break;
            case 'error_stats_response':
                console.log('错误统计:', message.stats);
                break;
            case 'performance_response':
                console.log('性能数据:', message.data);
                break;
            case 'print_control_response':
                console.log('打印控制结果:', message);
                break;
            case 'error':
                console.error('错误:', message.error);
                // 🆕 自动错误处理
                this.handleError(message.error);
                break;
        }
    }

    // 🆕 错误处理方法
    handleError(error) {
        if (error.recovery_suggestion && error.recovery_suggestion.auto_recoverable) {
            console.log(`尝试自动恢复错误 ${error.id}...`);
            this.send({
                type: 'error_recovery',
                error_id: error.id,
                recovery_action: error.recovery_suggestion.strategy
            });
        }
    }
}

// 使用示例
const client = new WebSocketDTUClientV2('ws://localhost:8081/voice-ws');
client.connect();

// 🆕 使用新功能
setTimeout(() => {
    client.getErrorStats('1h');
    client.getPerformanceData('summary');
}, 2000);
```

### 2. 🆕 错误处理集成示例

```javascript
class ErrorHandlingManager {
    constructor(wsClient) {
        this.wsClient = wsClient;
        this.errorHistory = [];
        this.recoveryAttempts = new Map();
    }

    async handleSystemError(error) {
        // 记录错误
        this.errorHistory.push({
            ...error,
            handled_at: Date.now()
        });

        // 检查是否需要自动恢复
        if (this.shouldAttemptRecovery(error)) {
            return await this.attemptRecovery(error);
        }

        // 通知用户需要手动干预
        this.notifyManualIntervention(error);
        return false;
    }

    shouldAttemptRecovery(error) {
        const attempts = this.recoveryAttempts.get(error.code) || 0;
        const maxAttempts = this.getMaxAttempts(error.code);

        return attempts < maxAttempts && error.recovery_suggestion?.auto_recoverable;
    }

    async attemptRecovery(error) {
        const attempts = this.recoveryAttempts.get(error.code) || 0;
        this.recoveryAttempts.set(error.code, attempts + 1);

        const recoveryMessage = {
            type: 'error_recovery',
            error_id: error.id,
            recovery_action: error.recovery_suggestion.strategy,
            parameters: {
                attempt_number: attempts + 1,
                max_attempts: this.getMaxAttempts(error.code)
            }
        };

        this.wsClient.send(recoveryMessage);

        // 等待恢复结果
        return new Promise((resolve) => {
            const timeout = setTimeout(() => resolve(false), 30000);

            const originalHandler = this.wsClient.handleMessage;
            this.wsClient.handleMessage = (message) => {
                if (message.type === 'error_recovery_response' &&
                    message.error_id === error.id) {
                    clearTimeout(timeout);
                    this.wsClient.handleMessage = originalHandler;
                    resolve(message.status === 'success');
                }
                originalHandler.call(this.wsClient, message);
            };
        });
    }

    getMaxAttempts(errorCode) {
        const maxAttemptsMap = {
            1001: 2,  // 步进电机故障
            2001: 5,  // DTU连接丢失
            3001: 1,  // 无效打印数据
            4001: 1   // 内存不足
        };
        return maxAttemptsMap[errorCode] || 3;
    }

    notifyManualIntervention(error) {
        console.warn(`错误 ${error.id} 需要手动干预:`, error.message);
        // 发送通知给用户界面
        this.wsClient.send({
            type: 'user_notification',
            notification: {
                type: 'error_manual_intervention',
                error: error,
                message: `系统检测到错误: ${error.message}，需要手动处理`
            }
        });
    }
}
```

### 3. 🆕 性能监控集成示例

```javascript
class PerformanceMonitoringManager {
    constructor(wsClient) {
        this.wsClient = wsClient;
        this.monitoringInterval = null;
        this.performanceData = {
            current: {},
            history: [],
            alerts: []
        };
    }

    startMonitoring(interval = 5000) {
        this.monitoringInterval = setInterval(() => {
            this.requestPerformanceData();
        }, interval);

        console.log('性能监控已启动');
    }

    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        console.log('性能监控已停止');
    }

    requestPerformanceData() {
        this.wsClient.send({
            type: 'performance_query',
            request: 'summary',
            include_details: true
        });
    }

    updatePerformanceData(data) {
        this.performanceData.current = data.realtime;
        this.performanceData.history.push({
            timestamp: Date.now(),
            ...data.realtime
        });

        // 保持历史数据在合理范围内
        if (this.performanceData.history.length > 1000) {
            this.performanceData.history = this.performanceData.history.slice(-1000);
        }

        // 检查性能告警
        this.checkPerformanceAlerts(data.realtime);
    }

    checkPerformanceAlerts(realtimeData) {
        const alerts = [];

        // 检查打印速度
        if (realtimeData.print_speed < 8.0) {
            alerts.push({
                type: 'speed_low',
                message: `打印速度过低: ${realtimeData.print_speed} 字符/秒`,
                severity: 'warning'
            });
        }

        // 检查错误率
        if (realtimeData.error_rate > 5.0) {
            alerts.push({
                type: 'error_rate_high',
                message: `错误率过高: ${realtimeData.error_rate}%`,
                severity: 'error'
            });
        }

        // 检查队列大小
        if (realtimeData.queue_size > 20) {
            alerts.push({
                type: 'queue_size_large',
                message: `打印队列过大: ${realtimeData.queue_size} 个任务`,
                severity: 'warning'
            });
        }

        // 处理新告警
        alerts.forEach(alert => {
            this.performanceData.alerts.push({
                ...alert,
                timestamp: Date.now(),
                id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            });

            console.warn(`性能告警: ${alert.message}`);
        });
    }

    generatePerformanceReport(timeRange = '1h') {
        this.wsClient.send({
            type: 'performance_report',
            time_range: timeRange,
            format: 'json',
            include_charts: true,
            sections: ['summary', 'trends', 'alerts', 'recommendations']
        });
    }

    getPerformanceSummary() {
        return {
            current: this.performanceData.current,
            recent_alerts: this.performanceData.alerts.slice(-5),
            history_points: this.performanceData.history.length,
            monitoring_active: this.monitoringInterval !== null
        };
    }
}
```

---

## 📝 API使用注意事项

### 1. 🆕 版本兼容性
- API版本: v2.0 (向后兼容v1.0)
- 建议使用最新版本以获得完整功能支持
- 旧版本客户端可以正常连接，但无法使用新功能

### 2. 🆕 错误处理最佳实践
- 始终检查API响应中的错误字段
- 实现自动重试机制，但要设置合理的重试次数
- 对于需要手动干预的错误，及时通知用户
- 记录错误日志以便后续分析

### 3. 🆕 性能监控建议
- 定期查询性能数据，但避免过于频繁的请求
- 设置合理的性能告警阈值
- 定期生成性能报告进行趋势分析
- 在系统负载较高时适当降低监控频率

### 4. 🆕 打印控制注意事项
- 使用安全停止选项避免损坏正在进行的打印任务
- 批量操作前确认影响范围
- 紧急停止功能仅在真正紧急情况下使用
- 保存任务进度以支持恢复操作

---

**📝 注意**: 本API文档涵盖了WebSocket+DTU系统v2.0的完整通信协议，包括新增的错误处理、性能监控和打印控制功能。开发时请严格按照协议格式进行数据交换，并充分利用新功能提升系统稳定性和用户体验。
```
```