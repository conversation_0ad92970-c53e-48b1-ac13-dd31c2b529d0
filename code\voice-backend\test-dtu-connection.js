/**
 * 银尔达DTU连接测试脚本
 * 用于验证DTU平台连接和设备认证
 */

const https = require('https');
const config = require('./config');

class DTUConnectionTester {
    constructor() {
        this.config = config.dtu;
        this.baseURL = this.config.api.apiBase;
        this.deviceInfo = this.config.device;
    }

    /**
     * 测试DTU平台连接
     */
    async testPlatformConnection() {
        console.log('🔍 测试DTU平台连接...');
        console.log(`📡 平台地址: ${this.baseURL}`);
        
        try {
            const response = await this.makeRequest('/');
            console.log('✅ DTU平台连接成功');
            return true;
        } catch (error) {
            console.log(`❌ DTU平台连接失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试设备认证
     */
    async testDeviceAuth() {
        console.log('🔐 测试设备认证...');
        console.log(`📱 设备IMEI: ${this.deviceInfo.imei}`);
        console.log(`📋 设备ICCID: ${this.deviceInfo.iccid}`);
        console.log(`👤 客户标识: ${this.deviceInfo.customer}`);
        
        try {
            // 尝试获取设备信息
            const deviceData = {
                imei: this.deviceInfo.imei,
                iccid: this.deviceInfo.iccid,
                type: this.deviceInfo.type
            };
            
            console.log('📤 发送设备认证请求...');
            const response = await this.makeRequest('/api/device/status', 'POST', deviceData);
            console.log('✅ 设备认证成功');
            console.log('📊 设备状态:', response);
            return true;
        } catch (error) {
            console.log(`❌ 设备认证失败: ${error.message}`);
            console.log('💡 可能的原因:');
            console.log('   1. 设备未在银尔达平台注册');
            console.log('   2. IMEI或ICCID不正确');
            console.log('   3. 设备未分配到正确的分组');
            console.log('   4. 账号权限不足');
            return false;
        }
    }

    /**
     * 测试设备参数获取
     */
    async testParameterRetrieval() {
        console.log('⚙️ 测试设备参数获取...');
        
        try {
            const params = {
                imei: this.deviceInfo.imei,
                request_type: 'config'
            };
            
            const response = await this.makeRequest('/api/device/config', 'GET', params);
            console.log('✅ 设备参数获取成功');
            console.log('📋 设备配置:', response);
            return true;
        } catch (error) {
            console.log(`❌ 设备参数获取失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 发送HTTP请求
     */
    makeRequest(path, method = 'GET', data = null) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, this.baseURL);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname + url.search,
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'CH32V307-DTU-Client/1.0'
                }
            };

            if (data && method !== 'GET') {
                const postData = JSON.stringify(data);
                options.headers['Content-Length'] = Buffer.byteLength(postData);
            }

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const parsed = responseData ? JSON.parse(responseData) : {};
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(parsed);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                        }
                    } catch (error) {
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(responseData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                        }
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            if (data && method !== 'GET') {
                req.write(JSON.stringify(data));
            }
            
            req.end();
        });
    }

    /**
     * 运行完整测试
     */
    async runFullTest() {
        console.log('🚀 开始银尔达DTU连接测试');
        console.log('=' * 50);
        
        const results = {
            platform: false,
            auth: false,
            parameters: false
        };

        // 测试平台连接
        results.platform = await this.testPlatformConnection();
        console.log('');

        // 测试设备认证
        if (results.platform) {
            results.auth = await this.testDeviceAuth();
            console.log('');
        }

        // 测试参数获取
        if (results.auth) {
            results.parameters = await this.testParameterRetrieval();
            console.log('');
        }

        // 输出测试结果
        console.log('📊 测试结果汇总:');
        console.log('=' * 50);
        console.log(`🌐 平台连接: ${results.platform ? '✅ 成功' : '❌ 失败'}`);
        console.log(`🔐 设备认证: ${results.auth ? '✅ 成功' : '❌ 失败'}`);
        console.log(`⚙️ 参数获取: ${results.parameters ? '✅ 成功' : '❌ 失败'}`);
        
        const allSuccess = results.platform && results.auth && results.parameters;
        console.log('');
        console.log(`🎯 总体状态: ${allSuccess ? '✅ 全部成功' : '⚠️ 需要处理'}`);
        
        if (!allSuccess) {
            console.log('');
            console.log('🔧 下一步操作建议:');
            if (!results.platform) {
                console.log('1. 检查网络连接和防火墙设置');
                console.log('2. 确认DTU平台地址正确');
            }
            if (!results.auth) {
                console.log('1. 在银尔达DTU平台注册账号');
                console.log('2. 添加设备到平台 (IMEI: 869080075169294)');
                console.log('3. 创建设备分组并分配设备');
                console.log('4. 确认设备型号选择正确 (M100P)');
            }
            if (!results.parameters) {
                console.log('1. 检查设备分组配置');
                console.log('2. 确认设备参数已正确设置');
            }
        }
        
        return allSuccess;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const tester = new DTUConnectionTester();
    tester.runFullTest().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ 测试过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = DTUConnectionTester;
