#ifndef __PLOTTER_H
#define __PLOTTER_H

#include "ch32v30x.h"

/* --- ���Ŷ��� (����Keyes CNC Shield) --- */
#define X_STEP_PORT GPIOA
#define X_STEP_PIN GPIO_Pin_0
#define X_DIR_PORT GPIOA
#define X_DIR_PIN GPIO_Pin_1

#define Y_STEP_PORT GPIOA
#define Y_STEP_PIN GPIO_Pin_2
#define Y_DIR_PORT GPIOA
#define Y_DIR_PIN GPIO_Pin_3

#define Z_STEP_PORT GPIOA
#define Z_STEP_PIN GPIO_Pin_4
#define Z_DIR_PORT GPIOA
#define Z_DIR_PIN GPIO_Pin_5

#define ENA_PORT GPIOC
#define ENA_PIN GPIO_Pin_3 // �����Ṳ��ʹ��

/* --- ״̬ö�� --- */
typedef enum
{
    IDLE,
    PRINTING_BRAILLE,
    CALIBRATING,
    ERROR_STATE
} PlotterState;

/* --- ��ӡģʽö�� --- */
typedef enum
{
    PRINT_MODE_FAST,     // ���ٴ�ӡ - ���ڲݸ���Ԥ��
    PRINT_MODE_STANDARD, // ��׼ģʽ - ƽ���ٶȺ�����
    PRINT_MODE_PRECISE   // ��ȷģʽ - ��߸�������
} PrintMode;

/* --- ��ӡ������� --- */
typedef enum
{
    QUALITY_DRAFT,  // �ݸ����� - ���ٴ�ӡ
    QUALITY_NORMAL, // ��׼���� - ��ʹ��
    QUALITY_HIGH    // �߸����� - ��ʽ�ĵ�
} PrintQuality;

/* --- ���ó��� --- */
// --- �������� (��Ҫ��������ʵ�ʻ�е�ṹ����У׼) ---
#define STEPS_PER_REVOLUTION 400.0f // 1.8�ȵ��, 1/2΢�� = 400��/Ȧ

// ����2024-05-24�û�У׼�������:
// X��: 2000�� = 50.0mm => 40��/mm => 400/40 = 10.0 mm/Ȧ
#define MM_PER_REVOLUTION_X 10.0f
// Y��: 2000�� = 83.0mm => 24.096��/mm => 400/24.096 = 16.60 mm/Ȧ
#define MM_PER_REVOLUTION_Y 16.60f
// Z��: ����Ĭ��ֵ�������Ҫ�뵥��У׼
#define MM_PER_REVOLUTION_Z 8.0f // T8˿��, ����8mm

// --- ä�ĳߴ� (��λ: mm) ---
#define BRAILLE_DOT_PITCH_Y 2.5f   // ��Ĵ�ֱ��� (��1 -> ��2)
#define BRAILLE_DOT_PITCH_X 2.5f   // ���ˮƽ��� (��1 -> ��4)
#define BRAILLE_CHAR_SPACING 6.0f  // �ַ���� (��һ���ַ��ĵ�1�е���һ���ĵ�1��)
#define BRAILLE_LINE_SPACING 10.0f // �м��
#define Z_RETRACT_HEIGHT 2.0f      // Z���ƶ�ʱ̧��ĸ߶�
#define Z_PUNCH_DEPTH 1.0f         // Z����ѹ��� (�����̧��߶�)

/* --- ��ӡ������ṹ�� --- */
typedef struct
{
    uint16_t step_delay_us;      // ���������ʱ(΢��)
    float quality_factor;        // ������(0.0-1.0)
    uint8_t enable_compensation; // �Ƿ�����ȷ����
    uint8_t max_complexity;      // ��������Ӷ�
} PrintConfig;

/* --- ��ӡ״̬�ṹ�� --- */
typedef struct
{
    uint32_t current_line;    // ��ǰ����
    uint32_t current_char;    // ��ǰ�ַ�λ��
    uint32_t total_chars;     // �ܷ�����
    uint32_t dots_printed;    // �Ѵ�ӡ����
    uint32_t errors_count;    // ������
    uint8_t progress_percent; // ��ӡ����(%)
} PrintStatus;

/* --- ��ȷ����ṹ�� --- */
typedef struct
{
    float backlash_x;            // X���������
    float backlash_y;            // Y���������
    uint8_t enable_thermal_comp; // �Ƿ�����ȹ���
    float thermal_coefficient;   // �ȹ���ϵ��
} CompensationConfig;

/* --- �ṹ�嶨�� --- */
typedef struct
{
    PlotterState state;
    PrintMode current_mode;
    PrintQuality current_quality;
    PrintConfig config;
    PrintStatus status;
    CompensationConfig compensation;
} Plotter;

extern Plotter plotter;

/* --- �������� --- */
void Plotter_Init(void);
void Plotter_PrintBrailleString(const char *text);

/* --- У׼ר�ú��� --- */
void Plotter_Calibrate_Move(char axis, uint32_t steps);

/* --- ��ӡģʽ������ --- */
void Plotter_SetPrintMode(PrintMode mode);
void Plotter_SetPrintQuality(PrintQuality quality);
void Plotter_ApplyPrintConfig(const PrintConfig *config);
PrintConfig *Plotter_GetCurrentConfig(void);

/* --- ��ӡ״̬������ --- */
PrintStatus *Plotter_GetPrintStatus(void);
void Plotter_ResetPrintStatus(void);
void Plotter_UpdateProgress(uint32_t current_char, uint32_t total_chars);

/* --- ��ȷ���������� --- */
void Plotter_SetCompensation(const CompensationConfig *comp);
void Plotter_EnableBacklashCompensation(uint8_t enable);
void Plotter_EnableThermalCompensation(uint8_t enable);

/* --- �Զ����ٶȿ��� --- */
void Plotter_SetAdaptiveSpeed(uint8_t enable);
uint16_t Plotter_CalculateOptimalSpeed(float complexity);

/* --- ������⺺�ָ� --- */
uint8_t Plotter_CheckErrors(void);
void Plotter_RecoverFromError(void);
void Plotter_ReportStatus(void);

#endif /* __PLOTTER_H */