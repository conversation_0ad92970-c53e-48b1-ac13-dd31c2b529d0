/**
 * 打印性能监控和统计系统
 * 基于现有状态监控框架，实现打印性能指标的收集、分析和展示
 * 
 * 功能特性:
 * - 实时性能数据收集
 * - 性能趋势分析
 * - 异常检测和告警
 * - 性能报告生成
 * - 与现有监控体系集成
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class PrintPerformanceMonitor extends EventEmitter {
    constructor(options = {}) {
        super();

        // 配置参数
        this.config = {
            dataRetentionDays: options.dataRetentionDays || 30,
            sampleInterval: options.sampleInterval || 1000, // 1秒采样间隔
            alertThresholds: {
                speedDropPercent: 30,        // 速度下降30%告警
                errorRatePercent: 5,         // 错误率超过5%告警
                queueSizeLimit: 20,          // 队列超过20个任务告警
                responseTimeMs: 5000         // 响应时间超过5秒告警
            },
            reportInterval: options.reportInterval || 300000, // 5分钟报告间隔
            dataPath: options.dataPath || './data/performance'
        };

        // 性能数据存储
        this.performanceData = {
            realtime: {
                printSpeed: 0,              // 当前打印速度 (字符/秒)
                queueSize: 0,               // 当前队列大小
                activeTaskCount: 0,         // 活跃任务数
                errorRate: 0,               // 当前错误率
                responseTime: 0,            // 平均响应时间
                compressionRatio: 0,        // 压缩比
                throughput: 0,              // 吞吐量
                lastUpdate: Date.now()
            },
            historical: [],                 // 历史数据点
            statistics: {
                totalTasks: 0,              // 总任务数
                completedTasks: 0,          // 完成任务数
                failedTasks: 0,             // 失败任务数
                totalCharacters: 0,         // 总字符数
                totalPrintTime: 0,          // 总打印时间
                averageSpeed: 0,            // 平均速度
                peakSpeed: 0,               // 峰值速度
                successRate: 100,           // 成功率
                uptime: 0,                  // 运行时间
                startTime: Date.now()
            },
            alerts: [],                     // 告警记录
            reports: []                     // 性能报告
        };

        // 任务跟踪
        this.activeTasks = new Map();       // 活跃任务跟踪
        this.taskHistory = [];              // 任务历史记录

        // 监控状态
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.reportInterval = null;

        // 初始化
        this.init();
    }

    async init() {
        try {
            // 确保数据目录存在
            await this.ensureDataDirectory();

            // 加载历史数据
            await this.loadHistoricalData();

            console.log('📊 打印性能监控系统初始化完成');
        } catch (error) {
            console.error('❌ 性能监控初始化失败:', error);
        }
    }

    async ensureDataDirectory() {
        try {
            await fs.mkdir(this.config.dataPath, { recursive: true });
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }

    // 启动性能监控
    startMonitoring() {
        if (this.isMonitoring) {
            console.log('⚠️ 性能监控已在运行中');
            return;
        }

        this.isMonitoring = true;
        this.performanceData.statistics.startTime = Date.now();

        // 启动实时数据采集
        this.monitoringInterval = setInterval(() => {
            this.collectPerformanceData();
        }, this.config.sampleInterval);

        // 启动定期报告生成
        this.reportInterval = setInterval(() => {
            this.generatePerformanceReport();
        }, this.config.reportInterval);

        console.log('🚀 打印性能监控已启动');
        this.emit('monitoring_started');
    }

    // 停止性能监控
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;

        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        if (this.reportInterval) {
            clearInterval(this.reportInterval);
            this.reportInterval = null;
        }

        console.log('⏹️ 打印性能监控已停止');
        this.emit('monitoring_stopped');
    }

    // 收集性能数据
    collectPerformanceData() {
        const now = Date.now();

        // 计算实时指标
        this.calculateRealtimeMetrics();

        // 更新统计数据
        this.updateStatistics();

        // 检查告警条件
        this.checkAlerts();

        // 保存历史数据点
        this.saveDataPoint();

        // 更新时间戳
        this.performanceData.realtime.lastUpdate = now;

        // 发送实时数据更新事件
        this.emit('data_updated', this.performanceData.realtime);
    }

    // 计算实时指标
    calculateRealtimeMetrics() {
        const stats = this.performanceData.statistics;
        const realtime = this.performanceData.realtime;

        // 计算当前队列大小
        realtime.queueSize = this.activeTasks.size;

        // 计算活跃任务数
        realtime.activeTaskCount = Array.from(this.activeTasks.values())
            .filter(task => task.status === 'printing').length;

        // 计算错误率
        if (stats.totalTasks > 0) {
            realtime.errorRate = (stats.failedTasks / stats.totalTasks) * 100;
        }

        // 计算平均响应时间
        const recentTasks = this.taskHistory.slice(-10); // 最近10个任务
        if (recentTasks.length > 0) {
            const totalResponseTime = recentTasks.reduce((sum, task) => {
                return sum + (task.responseTime || 0);
            }, 0);
            realtime.responseTime = totalResponseTime / recentTasks.length;
        }

        // 计算吞吐量 (任务/分钟)
        const oneMinuteAgo = Date.now() - 60000;
        const recentCompletedTasks = this.taskHistory.filter(task =>
            task.completedTime && task.completedTime > oneMinuteAgo
        );
        realtime.throughput = recentCompletedTasks.length;
    }

    // 更新统计数据
    updateStatistics() {
        const stats = this.performanceData.statistics;
        const realtime = this.performanceData.realtime;

        // 更新运行时间
        stats.uptime = Date.now() - stats.startTime;

        // 更新成功率
        if (stats.totalTasks > 0) {
            stats.successRate = ((stats.totalTasks - stats.failedTasks) / stats.totalTasks) * 100;
        }

        // 更新平均速度
        if (stats.totalPrintTime > 0) {
            stats.averageSpeed = stats.totalCharacters / (stats.totalPrintTime / 1000);
        }

        // 更新峰值速度
        if (realtime.printSpeed > stats.peakSpeed) {
            stats.peakSpeed = realtime.printSpeed;
        }
    }

    // 检查告警条件
    checkAlerts() {
        const realtime = this.performanceData.realtime;
        const thresholds = this.config.alertThresholds;
        const now = Date.now();

        // 检查速度下降告警
        if (this.performanceData.statistics.averageSpeed > 0) {
            const speedDropPercent = ((this.performanceData.statistics.averageSpeed - realtime.printSpeed) /
                this.performanceData.statistics.averageSpeed) * 100;

            if (speedDropPercent > thresholds.speedDropPercent) {
                this.createAlert('speed_drop', `打印速度下降${speedDropPercent.toFixed(1)}%`, 'warning');
            }
        }

        // 检查错误率告警
        if (realtime.errorRate > thresholds.errorRatePercent) {
            this.createAlert('high_error_rate', `错误率过高: ${realtime.errorRate.toFixed(1)}%`, 'error');
        }

        // 检查队列大小告警
        if (realtime.queueSize > thresholds.queueSizeLimit) {
            this.createAlert('queue_overflow', `队列任务过多: ${realtime.queueSize}个`, 'warning');
        }

        // 检查响应时间告警
        if (realtime.responseTime > thresholds.responseTimeMs) {
            this.createAlert('slow_response', `响应时间过长: ${realtime.responseTime}ms`, 'warning');
        }
    }

    // 创建告警
    createAlert(type, message, level = 'info') {
        const alert = {
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type,
            message,
            level,
            timestamp: Date.now(),
            acknowledged: false
        };

        this.performanceData.alerts.unshift(alert);

        // 保持告警数量限制
        if (this.performanceData.alerts.length > 100) {
            this.performanceData.alerts = this.performanceData.alerts.slice(0, 100);
        }

        console.log(`🚨 性能告警 [${level.toUpperCase()}]: ${message}`);
        this.emit('alert_created', alert);

        return alert;
    }

    // 保存数据点
    saveDataPoint() {
        const dataPoint = {
            timestamp: Date.now(),
            ...this.performanceData.realtime
        };

        this.performanceData.historical.push(dataPoint);

        // 保持历史数据量限制 (最近24小时)
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
        this.performanceData.historical = this.performanceData.historical.filter(
            point => point.timestamp > oneDayAgo
        );
    }

    // 任务开始跟踪
    startTask(taskId, taskInfo = {}) {
        const task = {
            id: taskId,
            startTime: Date.now(),
            status: 'started',
            characterCount: taskInfo.characterCount || 0,
            mode: taskInfo.mode || 'standard',
            priority: taskInfo.priority || 'normal',
            ...taskInfo
        };

        this.activeTasks.set(taskId, task);
        this.performanceData.statistics.totalTasks++;

        console.log(`📝 开始跟踪任务: ${taskId}`);
        this.emit('task_started', task);

        return task;
    }

    // 任务进度更新
    updateTaskProgress(taskId, progress, speed = 0) {
        const task = this.activeTasks.get(taskId);
        if (!task) {
            console.warn(`⚠️ 任务不存在: ${taskId}`);
            return;
        }

        task.progress = progress;
        task.currentSpeed = speed;
        task.status = 'printing';
        task.lastUpdate = Date.now();

        // 更新实时打印速度
        this.performanceData.realtime.printSpeed = speed;

        this.emit('task_progress', task);
    }

    // 任务完成
    completeTask(taskId, result = {}) {
        const task = this.activeTasks.get(taskId);
        if (!task) {
            console.warn(`⚠️ 任务不存在: ${taskId}`);
            return;
        }

        const now = Date.now();
        task.completedTime = now;
        task.duration = now - task.startTime;
        task.status = result.success ? 'completed' : 'failed';
        task.responseTime = task.duration;

        // 更新统计数据
        if (result.success) {
            this.performanceData.statistics.completedTasks++;
            this.performanceData.statistics.totalCharacters += task.characterCount;
            this.performanceData.statistics.totalPrintTime += task.duration;
        } else {
            this.performanceData.statistics.failedTasks++;
        }

        // 移动到历史记录
        this.taskHistory.push({ ...task });
        this.activeTasks.delete(taskId);

        // 保持历史记录限制
        if (this.taskHistory.length > 1000) {
            this.taskHistory = this.taskHistory.slice(-1000);
        }

        console.log(`✅ 任务完成: ${taskId} (${task.status})`);
        this.emit('task_completed', task);

        return task;
    }

    // 生成性能报告
    async generatePerformanceReport(timeRange = '1h') {
        try {
            const now = Date.now();
            let startTime;

            // 确定时间范围
            switch (timeRange) {
                case '1h':
                    startTime = now - (60 * 60 * 1000);
                    break;
                case '24h':
                    startTime = now - (24 * 60 * 60 * 1000);
                    break;
                case '7d':
                    startTime = now - (7 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startTime = now - (60 * 60 * 1000);
            }

            // 筛选时间范围内的数据
            const rangeData = this.performanceData.historical.filter(
                point => point.timestamp >= startTime
            );

            const rangeTasks = this.taskHistory.filter(
                task => task.startTime >= startTime
            );

            // 计算报告指标
            const report = {
                id: `report_${now}_${Math.random().toString(36).substr(2, 9)}`,
                timeRange,
                startTime,
                endTime: now,
                duration: now - startTime,

                // 任务统计
                taskStats: {
                    total: rangeTasks.length,
                    completed: rangeTasks.filter(t => t.status === 'completed').length,
                    failed: rangeTasks.filter(t => t.status === 'failed').length,
                    successRate: 0,
                    averageDuration: 0,
                    totalCharacters: 0
                },

                // 性能指标
                performance: {
                    averageSpeed: 0,
                    peakSpeed: 0,
                    minSpeed: Infinity,
                    speedVariance: 0,
                    averageQueueSize: 0,
                    maxQueueSize: 0,
                    averageResponseTime: 0,
                    throughput: 0
                },

                // 趋势分析
                trends: {
                    speedTrend: 'stable',
                    errorTrend: 'stable',
                    queueTrend: 'stable',
                    performanceTrend: 'stable'
                },

                // 告警统计
                alertStats: {
                    total: 0,
                    byLevel: { info: 0, warning: 0, error: 0 },
                    byType: {}
                },

                // 建议
                recommendations: [],

                generatedAt: now
            };

            // 计算任务统计
            if (rangeTasks.length > 0) {
                const completedTasks = rangeTasks.filter(t => t.status === 'completed');
                report.taskStats.successRate = (completedTasks.length / rangeTasks.length) * 100;

                const totalDuration = rangeTasks.reduce((sum, task) => sum + (task.duration || 0), 0);
                report.taskStats.averageDuration = totalDuration / rangeTasks.length;

                report.taskStats.totalCharacters = rangeTasks.reduce((sum, task) =>
                    sum + (task.characterCount || 0), 0);
            }

            // 计算性能指标
            if (rangeData.length > 0) {
                const speeds = rangeData.map(d => d.printSpeed).filter(s => s > 0);
                const queueSizes = rangeData.map(d => d.queueSize);
                const responseTimes = rangeData.map(d => d.responseTime).filter(r => r > 0);

                if (speeds.length > 0) {
                    report.performance.averageSpeed = speeds.reduce((a, b) => a + b) / speeds.length;
                    report.performance.peakSpeed = Math.max(...speeds);
                    report.performance.minSpeed = Math.min(...speeds);

                    // 计算速度方差
                    const avgSpeed = report.performance.averageSpeed;
                    const variance = speeds.reduce((sum, speed) =>
                        sum + Math.pow(speed - avgSpeed, 2), 0) / speeds.length;
                    report.performance.speedVariance = Math.sqrt(variance);
                }

                if (queueSizes.length > 0) {
                    report.performance.averageQueueSize = queueSizes.reduce((a, b) => a + b) / queueSizes.length;
                    report.performance.maxQueueSize = Math.max(...queueSizes);
                }

                if (responseTimes.length > 0) {
                    report.performance.averageResponseTime = responseTimes.reduce((a, b) => a + b) / responseTimes.length;
                }

                // 计算吞吐量
                report.performance.throughput = (rangeTasks.filter(t => t.status === 'completed').length /
                    (report.duration / (60 * 1000))); // 任务/分钟
            }

            // 分析趋势
            this.analyzeTrends(report, rangeData);

            // 统计告警
            this.analyzeAlerts(report, startTime);

            // 生成建议
            this.generateRecommendations(report);

            // 保存报告
            this.performanceData.reports.unshift(report);

            // 保持报告数量限制
            if (this.performanceData.reports.length > 50) {
                this.performanceData.reports = this.performanceData.reports.slice(0, 50);
            }

            // 保存到文件
            await this.saveReportToFile(report);

            console.log(`📊 性能报告生成完成: ${report.id} (${timeRange})`);
            this.emit('report_generated', report);

            return report;

        } catch (error) {
            console.error('❌ 生成性能报告失败:', error);
            throw error;
        }
    }

    // 分析趋势
    analyzeTrends(report, rangeData) {
        if (rangeData.length < 10) {
            return; // 数据不足，无法分析趋势
        }

        const midPoint = Math.floor(rangeData.length / 2);
        const firstHalf = rangeData.slice(0, midPoint);
        const secondHalf = rangeData.slice(midPoint);

        // 速度趋势
        const firstHalfAvgSpeed = firstHalf.reduce((sum, d) => sum + d.printSpeed, 0) / firstHalf.length;
        const secondHalfAvgSpeed = secondHalf.reduce((sum, d) => sum + d.printSpeed, 0) / secondHalf.length;

        if (secondHalfAvgSpeed > firstHalfAvgSpeed * 1.1) {
            report.trends.speedTrend = 'improving';
        } else if (secondHalfAvgSpeed < firstHalfAvgSpeed * 0.9) {
            report.trends.speedTrend = 'declining';
        }

        // 错误趋势
        const firstHalfAvgError = firstHalf.reduce((sum, d) => sum + d.errorRate, 0) / firstHalf.length;
        const secondHalfAvgError = secondHalf.reduce((sum, d) => sum + d.errorRate, 0) / secondHalf.length;

        if (secondHalfAvgError > firstHalfAvgError * 1.2) {
            report.trends.errorTrend = 'worsening';
        } else if (secondHalfAvgError < firstHalfAvgError * 0.8) {
            report.trends.errorTrend = 'improving';
        }

        // 队列趋势
        const firstHalfAvgQueue = firstHalf.reduce((sum, d) => sum + d.queueSize, 0) / firstHalf.length;
        const secondHalfAvgQueue = secondHalf.reduce((sum, d) => sum + d.queueSize, 0) / secondHalf.length;

        if (secondHalfAvgQueue > firstHalfAvgQueue * 1.5) {
            report.trends.queueTrend = 'increasing';
        } else if (secondHalfAvgQueue < firstHalfAvgQueue * 0.5) {
            report.trends.queueTrend = 'decreasing';
        }
    }

    // 分析告警
    analyzeAlerts(report, startTime) {
        const rangeAlerts = this.performanceData.alerts.filter(
            alert => alert.timestamp >= startTime
        );

        report.alertStats.total = rangeAlerts.length;

        // 按级别统计
        rangeAlerts.forEach(alert => {
            report.alertStats.byLevel[alert.level] = (report.alertStats.byLevel[alert.level] || 0) + 1;
            report.alertStats.byType[alert.type] = (report.alertStats.byType[alert.type] || 0) + 1;
        });
    }

    // 生成建议
    generateRecommendations(report) {
        const recommendations = [];

        // 基于成功率的建议
        if (report.taskStats.successRate < 95) {
            recommendations.push({
                type: 'reliability',
                priority: 'high',
                message: `任务成功率较低(${report.taskStats.successRate.toFixed(1)}%)，建议检查硬件连接和错误日志`
            });
        }

        // 基于速度的建议
        if (report.performance.speedVariance > report.performance.averageSpeed * 0.3) {
            recommendations.push({
                type: 'performance',
                priority: 'medium',
                message: '打印速度波动较大，建议检查系统负载和网络稳定性'
            });
        }

        // 基于队列的建议
        if (report.performance.averageQueueSize > 10) {
            recommendations.push({
                type: 'capacity',
                priority: 'medium',
                message: '队列长度较长，建议优化打印参数或增加处理能力'
            });
        }

        // 基于趋势的建议
        if (report.trends.speedTrend === 'declining') {
            recommendations.push({
                type: 'maintenance',
                priority: 'high',
                message: '打印速度呈下降趋势，建议进行系统维护检查'
            });
        }

        if (report.trends.errorTrend === 'worsening') {
            recommendations.push({
                type: 'reliability',
                priority: 'high',
                message: '错误率呈上升趋势，建议立即检查系统状态'
            });
        }

        report.recommendations = recommendations;
    }

    // 保存报告到文件
    async saveReportToFile(report) {
        try {
            const fileName = `performance_report_${report.timeRange}_${new Date(report.generatedAt).toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            const filePath = path.join(this.config.dataPath, fileName);

            await fs.writeFile(filePath, JSON.stringify(report, null, 2), 'utf8');
            console.log(`💾 性能报告已保存: ${fileName}`);
        } catch (error) {
            console.error('❌ 保存性能报告失败:', error);
        }
    }

    // 加载历史数据
    async loadHistoricalData() {
        try {
            const dataFile = path.join(this.config.dataPath, 'performance_data.json');

            try {
                const data = await fs.readFile(dataFile, 'utf8');
                const savedData = JSON.parse(data);

                // 恢复统计数据
                if (savedData.statistics) {
                    Object.assign(this.performanceData.statistics, savedData.statistics);
                }

                // 恢复历史数据（最近24小时）
                if (savedData.historical) {
                    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
                    this.performanceData.historical = savedData.historical.filter(
                        point => point.timestamp > oneDayAgo
                    );
                }

                console.log('📂 历史性能数据加载完成');
            } catch (error) {
                if (error.code !== 'ENOENT') {
                    console.warn('⚠️ 加载历史数据失败:', error.message);
                }
            }
        } catch (error) {
            console.error('❌ 加载历史数据异常:', error);
        }
    }

    // 保存数据到文件
    async saveDataToFile() {
        try {
            const dataFile = path.join(this.config.dataPath, 'performance_data.json');
            const dataToSave = {
                statistics: this.performanceData.statistics,
                historical: this.performanceData.historical.slice(-1000), // 保存最近1000个数据点
                lastSaved: Date.now()
            };

            await fs.writeFile(dataFile, JSON.stringify(dataToSave, null, 2), 'utf8');
            console.log('💾 性能数据已保存');
        } catch (error) {
            console.error('❌ 保存性能数据失败:', error);
        }
    }

    // 获取性能摘要
    getPerformanceSummary() {
        return {
            realtime: { ...this.performanceData.realtime },
            statistics: { ...this.performanceData.statistics },
            recentAlerts: this.performanceData.alerts.slice(0, 5),
            activeTasks: this.activeTasks.size,
            isMonitoring: this.isMonitoring
        };
    }

    // 获取历史数据
    getHistoricalData(timeRange = '1h', sampleCount = 100) {
        const now = Date.now();
        let startTime;

        switch (timeRange) {
            case '1h':
                startTime = now - (60 * 60 * 1000);
                break;
            case '24h':
                startTime = now - (24 * 60 * 60 * 1000);
                break;
            default:
                startTime = now - (60 * 60 * 1000);
        }

        const filteredData = this.performanceData.historical.filter(
            point => point.timestamp >= startTime
        );

        // 如果数据点太多，进行采样
        if (filteredData.length > sampleCount) {
            const step = Math.floor(filteredData.length / sampleCount);
            return filteredData.filter((_, index) => index % step === 0);
        }

        return filteredData;
    }

    // 清理过期数据
    async cleanupExpiredData() {
        try {
            const retentionTime = Date.now() - (this.config.dataRetentionDays * 24 * 60 * 60 * 1000);

            // 清理历史数据
            this.performanceData.historical = this.performanceData.historical.filter(
                point => point.timestamp > retentionTime
            );

            // 清理告警记录
            this.performanceData.alerts = this.performanceData.alerts.filter(
                alert => alert.timestamp > retentionTime
            );

            // 清理任务历史
            this.taskHistory = this.taskHistory.filter(
                task => task.startTime > retentionTime
            );

            console.log('🧹 过期数据清理完成');
        } catch (error) {
            console.error('❌ 清理过期数据失败:', error);
        }
    }

    // 销毁监控器
    async destroy() {
        this.stopMonitoring();

        // 保存最终数据
        await this.saveDataToFile();

        // 清理资源
        this.activeTasks.clear();
        this.taskHistory = [];
        this.performanceData.historical = [];
        this.performanceData.alerts = [];

        console.log('🗑️ 性能监控器已销毁');
    }
}

module.exports = PrintPerformanceMonitor;
