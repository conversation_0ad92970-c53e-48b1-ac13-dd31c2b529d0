<!DOCTYPE html><html><head><meta charset="UTF-8"><title>串口测试</title></head><body><h1>串口Web转发测试</h1><textarea id="msg" placeholder="输入消息"></textarea><button onclick="send()">发送</button><div id="log"></div><script>async function send(){const text=document.getElementById("msg").value;if(!text)return;try{const resp=await fetch("/api/send/text",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text,prefix:"[d-j-q.xyz]"})});const data=await resp.json();document.getElementById("log").innerHTML+=`<p>${data.success?"✅":"❌"} ${text}</p>`;}catch(e){document.getElementById("log").innerHTML+=`<p>❌ 错误: ${e.message}</p>`;}}</script></body></html>
