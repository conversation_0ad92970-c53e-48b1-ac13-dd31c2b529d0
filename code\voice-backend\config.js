// CH32V307智能语音识别盲文打印系统 - 统一配置管理
// WebSocket+DTU混合架构配置文件
// 版本: 2.0.0

// 环境检测
const NODE_ENV = process.env.NODE_ENV || 'development';

console.log(`[配置管理] 当前环境: ${NODE_ENV}`);

// 基础配置结构
const BASE_CONFIG = {
    // 系统信息
    system: {
        name: 'CH32V307_WebSocket_DTU_System',
        version: '2.0.0',
        description: 'WebSocket+DTU混合架构智能语音识别盲文打印系统',
        environment: NODE_ENV,
        startTime: new Date().toISOString()
    },

    // 服务器配置
    server: {
        // WebSocket服务器
        websocket: {
            host: process.env.WS_HOST || '0.0.0.0',
            port: parseInt(process.env.WS_PORT) || 8081,
            path: process.env.WS_PATH || '/voice-ws',
            maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS) || 100,
            heartbeatInterval: parseInt(process.env.WS_HEARTBEAT) || 30000,
            connectionTimeout: parseInt(process.env.WS_TIMEOUT) || 60000
        },

        // HTTP服务器（如果需要）
        http: {
            enabled: process.env.HTTP_ENABLED === 'true' || false,
            port: parseInt(process.env.HTTP_PORT) || 8080,
            staticPath: process.env.HTTP_STATIC || '../voice-frontend'
        }
    },

    // 讯飞语音识别配置
    xunfei: {
        enabled: process.env.XUNFEI_ENABLED !== 'false', // 默认启用
        appId: process.env.XUNFEI_APP_ID || 'ebde7329',
        apiKey: process.env.XUNFEI_API_KEY || '0e141d1d1e1a2a73e1691f9330e71211',
        apiSecret: process.env.XUNFEI_API_SECRET || 'NzViOTRlNWNkNTlmYzQxMTYwMTFiYzUy',

        // 识别参数
        recognition: {
            language: 'zh_cn',
            domain: 'iat',
            accent: 'mandarin',
            timeout: parseInt(process.env.XUNFEI_TIMEOUT) || 20000,
            confidenceThreshold: parseFloat(process.env.XUNFEI_CONFIDENCE) || 0.7
        }
    },

    // 银尔达DTU平台配置
    dtu: {
        enabled: false, // 临时禁用DTU功能，避免连接错误

        // DTU平台API配置
        api: {
            apiBase: process.env.DTU_API_BASE || 'http://api.yinled.com',
            serverHost: process.env.DTU_SERVER_HOST || 'dtu.yinled.com',
            serverPort: parseInt(process.env.DTU_SERVER_PORT) || 8888,
            timeout: parseInt(process.env.DTU_TIMEOUT) || 10000,
            retryAttempts: parseInt(process.env.DTU_RETRY_ATTEMPTS) || 3,
            retryDelay: parseInt(process.env.DTU_RETRY_DELAY) || 1000
        },

        // 设备认证信息
        device: {
            deviceId: process.env.DTU_DEVICE_ID || '869080075169294',  // IMEI
            deviceKey: process.env.DTU_DEVICE_KEY || '898604021024C0050919', // ICCID
            type: 'ch32v307_braille_printer',
            features: ['voice_recognition', 'braille_print', 'status_report'],
            communication: 'dtu_transparent'
        },

        // 设备管理配置
        management: {
            heartbeatInterval: parseInt(process.env.DTU_HEARTBEAT) || 30000,
            offlineTimeout: parseInt(process.env.DTU_OFFLINE_TIMEOUT) || 300000, // 5分钟
            supportedTypes: ['ch32v307', 'air780e']
        },

        // 数据转发配置
        forwarding: {
            autoForwardVoice: process.env.DTU_AUTO_FORWARD !== 'false',
            dataFormat: 'json',
            encoding: 'utf-8',
            confidenceThreshold: parseFloat(process.env.DTU_CONFIDENCE_THRESHOLD) || 0.7
        }
    },

    // Air780e 4G模块配置
    air780e: {
        // 通信配置
        communication: {
            uart: {
                baudRate: parseInt(process.env.AIR780E_BAUD_RATE) || 115200,
                dataBits: 8,
                parity: 'none',
                stopBits: 1
            }
        },

        // 网络配置
        network: {
            apn: process.env.AIR780E_APN || 'cmnet',
            protocol: 'tcp',
            keepAlive: parseInt(process.env.AIR780E_KEEP_ALIVE) || 60
        },

        // 设备配置
        device: {
            deviceId: process.env.AIR780E_DEVICE_ID || 'ch32v307_dtu_01',
            reconnectInterval: parseInt(process.env.AIR780E_RECONNECT) || 5000,
            heartbeatInterval: parseInt(process.env.AIR780E_HEARTBEAT) || 30000
        }
    },

    // CH32V307主控芯片配置
    ch32v307: {
        // 通信协议
        protocol: {
            format: 'json',
            encoding: 'utf-8',
            frameStart: 0x7E,
            frameEnd: 0x7F
        },

        // 支持的指令类型
        commands: {
            voice: 'VOICE_CMD',
            control: 'CTRL_CMD',
            query: 'QUERY_CMD',
            config: 'CONFIG_CMD'
        },

        // 设备功能
        features: {
            gpio: ['led', 'relay', 'sensor'],
            peripherals: ['uart', 'i2c', 'spi', 'adc'],
            sensors: ['temperature', 'humidity', 'light']
        }
    },

    // 盲文打印控制配置
    printing: {
        // 打印速度等级配置
        speed_levels: {
            fast: {
                step_delay_us: parseInt(process.env.PRINT_SPEED_FAST) || 200,
                description: '快速打印模式，适用于草稿和预览',
                max_complexity: 0.3
            },
            standard: {
                step_delay_us: parseInt(process.env.PRINT_SPEED_STANDARD) || 500,
                description: '标准打印模式，平衡速度和质量',
                max_complexity: 0.7
            },
            precise: {
                step_delay_us: parseInt(process.env.PRINT_SPEED_PRECISE) || 1000,
                description: '精确打印模式，最高质量输出',
                max_complexity: 1.0
            }
        },

        // 打印精度参数配置
        precision_settings: {
            // 步进电机校准参数
            calibration: {
                steps_per_revolution: parseFloat(process.env.PRINT_STEPS_PER_REV) || 400.0,
                mm_per_revolution_x: parseFloat(process.env.PRINT_MM_PER_REV_X) || 10.0,
                mm_per_revolution_y: parseFloat(process.env.PRINT_MM_PER_REV_Y) || 16.60,
                mm_per_revolution_z: parseFloat(process.env.PRINT_MM_PER_REV_Z) || 8.0
            },

            // 盲文尺寸参数
            braille_dimensions: {
                dot_pitch_x: parseFloat(process.env.PRINT_DOT_PITCH_X) || 2.5,
                dot_pitch_y: parseFloat(process.env.PRINT_DOT_PITCH_Y) || 2.5,
                char_spacing: parseFloat(process.env.PRINT_CHAR_SPACING) || 6.0,
                line_spacing: parseFloat(process.env.PRINT_LINE_SPACING) || 10.0,
                z_retract_height: parseFloat(process.env.PRINT_Z_RETRACT) || 2.0,
                z_punch_depth: parseFloat(process.env.PRINT_Z_PUNCH) || 1.0
            },

            // 精度补偿参数
            compensation: {
                enable_backlash_compensation: process.env.PRINT_BACKLASH_COMP !== 'false',
                backlash_x: parseFloat(process.env.PRINT_BACKLASH_X) || 0.1,
                backlash_y: parseFloat(process.env.PRINT_BACKLASH_Y) || 0.1,
                enable_thermal_compensation: process.env.PRINT_THERMAL_COMP !== 'false',
                thermal_coefficient: parseFloat(process.env.PRINT_THERMAL_COEFF) || 0.00001
            }
        },

        // 打印模式预设
        print_modes: {
            draft: {
                name: '草稿模式',
                speed_level: 'fast',
                quality_factor: 0.6,
                enable_compensation: false,
                description: '快速草稿打印，用于内容预览'
            },
            normal: {
                name: '标准模式',
                speed_level: 'standard',
                quality_factor: 0.8,
                enable_compensation: true,
                description: '日常使用的标准打印质量'
            },
            high_quality: {
                name: '高质量模式',
                speed_level: 'precise',
                quality_factor: 1.0,
                enable_compensation: true,
                description: '最高质量打印，用于正式文档'
            }
        },

        // 打印任务管理配置
        task_management: {
            max_queue_size: parseInt(process.env.PRINT_MAX_QUEUE) || 10,
            default_priority: parseInt(process.env.PRINT_DEFAULT_PRIORITY) || 5,
            timeout_ms: parseInt(process.env.PRINT_TIMEOUT) || 300000, // 5分钟
            enable_batch_processing: process.env.PRINT_BATCH_ENABLED !== 'false',
            batch_size: parseInt(process.env.PRINT_BATCH_SIZE) || 3
        },

        // 状态监控配置
        monitoring: {
            enable_realtime_status: process.env.PRINT_REALTIME_STATUS !== 'false',
            status_update_interval: parseInt(process.env.PRINT_STATUS_INTERVAL) || 1000,
            enable_performance_tracking: process.env.PRINT_PERF_TRACKING !== 'false',
            performance_sample_rate: parseFloat(process.env.PRINT_PERF_SAMPLE_RATE) || 0.1
        },

        // 错误处理配置
        error_handling: {
            enable_auto_recovery: process.env.PRINT_AUTO_RECOVERY !== 'false',
            max_retry_attempts: parseInt(process.env.PRINT_MAX_RETRIES) || 3,
            retry_delay_ms: parseInt(process.env.PRINT_RETRY_DELAY) || 2000,
            enable_error_logging: process.env.PRINT_ERROR_LOGGING !== 'false',
            error_notification_threshold: parseInt(process.env.PRINT_ERROR_THRESHOLD) || 5
        }
    }


};

// 环境特定配置覆盖
const ENVIRONMENT_OVERRIDES = {
    development: {
        server: {
            websocket: {
                host: '127.0.0.1',
                port: 8081
            }
        },
        xunfei: {
            enabled: true
        },
        dtu: {
            enabled: false  // 开发环境禁用DTU，避免连接错误
        }
    },

    production: {
        server: {
            websocket: {
                host: '0.0.0.0',
                port: 8081
            }
        },
        xunfei: {
            enabled: true
        },
        dtu: {
            enabled: true
        }
    },

    test: {
        server: {
            websocket: {
                port: 8082
            }
        },
        xunfei: {
            enabled: false // 测试环境禁用语音识别
        },
        dtu: {
            enabled: false // 测试环境禁用DTU
        }
    }
};

// 配置合并函数
function mergeConfig(baseConfig, overrides) {
    const result = JSON.parse(JSON.stringify(baseConfig));

    function deepMerge(target, source) {
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                if (!target[key]) target[key] = {};
                deepMerge(target[key], source[key]);
            } else {
                target[key] = source[key];
            }
        }
    }

    if (overrides) {
        deepMerge(result, overrides);
    }

    return result;
}

// 配置验证函数
function validateConfig(config) {
    const errors = [];

    // 验证必需的配置项
    if (!config.system?.name) {
        errors.push('系统名称未配置');
    }

    if (config.xunfei?.enabled && !config.xunfei?.appId) {
        errors.push('讯飞语音识别已启用但缺少appId');
    }

    if (config.dtu?.enabled && !config.dtu?.device?.deviceId) {
        errors.push('DTU平台已启用但缺少设备ID');
    }

    if (!config.server?.websocket?.port) {
        errors.push('WebSocket服务器端口未配置');
    }

    // 验证打印配置
    if (config.printing) {
        // 验证速度等级配置
        const speedLevels = config.printing.speed_levels;
        if (speedLevels) {
            ['fast', 'standard', 'precise'].forEach(level => {
                if (speedLevels[level] && (!speedLevels[level].step_delay_us || speedLevels[level].step_delay_us <= 0)) {
                    errors.push(`打印速度等级 ${level} 的 step_delay_us 配置无效`);
                }
            });
        }

        // 验证精度参数
        const precision = config.printing.precision_settings;
        if (precision?.calibration) {
            const cal = precision.calibration;
            if (!cal.steps_per_revolution || cal.steps_per_revolution <= 0) {
                errors.push('打印校准参数 steps_per_revolution 配置无效');
            }
            if (!cal.mm_per_revolution_x || cal.mm_per_revolution_x <= 0) {
                errors.push('打印校准参数 mm_per_revolution_x 配置无效');
            }
            if (!cal.mm_per_revolution_y || cal.mm_per_revolution_y <= 0) {
                errors.push('打印校准参数 mm_per_revolution_y 配置无效');
            }
        }

        // 验证任务管理配置
        const taskMgmt = config.printing.task_management;
        if (taskMgmt) {
            if (taskMgmt.max_queue_size && taskMgmt.max_queue_size <= 0) {
                errors.push('打印队列最大大小配置无效');
            }
            if (taskMgmt.timeout_ms && taskMgmt.timeout_ms <= 0) {
                errors.push('打印超时时间配置无效');
            }
        }
    }

    if (errors.length > 0) {
        console.error('[配置验证] 配置错误:', errors);
        throw new Error(`配置验证失败: ${errors.join(', ')}`);
    }

    console.log('[配置验证] 配置验证通过');
    return true;
}

// 生成最终配置
const environmentOverride = ENVIRONMENT_OVERRIDES[NODE_ENV] || {};
const finalConfig = mergeConfig(BASE_CONFIG, environmentOverride);

// 验证配置
validateConfig(finalConfig);

// 配置信息输出
console.log(`[配置管理] 系统: ${finalConfig.system.name} v${finalConfig.system.version}`);
console.log(`[配置管理] WebSocket服务器: ${finalConfig.server.websocket.host}:${finalConfig.server.websocket.port}`);
console.log(`[配置管理] 讯飞语音识别: ${finalConfig.xunfei.enabled ? '启用' : '禁用'}`);
console.log(`[配置管理] DTU平台: ${finalConfig.dtu.enabled ? '启用' : '禁用'}`);

// 导出配置
module.exports = finalConfig;

// 兼容性导出（保持向后兼容）
module.exports.xunfei = finalConfig.xunfei;
module.exports.air780e = finalConfig.air780e;
module.exports.ch32v307 = finalConfig.ch32v307;
module.exports.dtu = finalConfig.dtu;
module.exports.printing = finalConfig.printing;

// 配置管理器支持
module.exports.getConfig = () => finalConfig;
module.exports.validateConfig = () => validateConfig(finalConfig);
module.exports.mergeConfig = mergeConfig;

// 配置元信息
module.exports.meta = {
    version: '2.0.0',
    loadTime: new Date().toISOString(),
    environment: NODE_ENV,
    configFiles: [
        'config.js',
        `config.${NODE_ENV}.js`
    ]
};