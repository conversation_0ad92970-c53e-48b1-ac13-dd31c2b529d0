// Ѷ������ʶ������
// ʹ��ǰ�뵽 https://console.xfyun.cn/ ע�Ტ��ȡ������Ϣ

const XUNFEI_CONFIG = {
    // ?? ��Ҫ���뽫���������滻Ϊ������ʵѶ���˺���Ϣ
    appId: 'ebde7329',          // ����Ӧ��ID
    apiKey: '0e141d1d1e1a2a73e1691f9330e71211',        // ����API Key  
    apiSecret: 'NzViOTRlNWNkNTlmYzQxMTYwMTFiYzUy',  // ����API Secret

    // �Ƿ�����Ѷ��API����Ϊfalse��ʹ����ʾģʽ��
    enabled: true  // ��������ʵ��Ѷ������ʶ��
};



// Air780e 4Gģ������˵��
const AIR780E_CONFIG = {
    // ͨ������
    communication: {
        uart: {
            baudRate: 115200,
            dataBits: 8,
            parity: 'none',
            stopBits: 1
        }
    },

    // ��������
    network: {
        apn: 'cmnet',  // ������Ӫ�̵���: cmnet(�ƶ�), 3gnet(��ͨ), ctnet(����)
        protocol: 'tcp',
        keepAlive: 60
    },

    // MQTT�ͻ������� (�������mqtt���ñ���һ��)
    mqtt: {
        server: 'broker.emqx.io',
        port: 1883,
        clientId: 'air780e_${device_id}',  // �豸ID��̬�滻
        username: '',
        password: '',
        qos: 1
    }
};

// CH32V307��Ƭ������˵��  
const CH32V307_CONFIG = {
    // ͨ��Э��
    protocol: {
        format: 'json',  // ���ݸ�ʽ
        encoding: 'utf-8',
        frameStart: 0x7E,  // ֡��ʼ��ʶ
        frameEnd: 0x7F     // ֡������ʶ
    },

    // ֧�ֵ�ָ������
    commands: {
        voice: 'VOICE_CMD',      // ����ָ��
        control: 'CTRL_CMD',     // ����ָ��  
        query: 'QUERY_CMD',      // ��ѯָ��
        config: 'CONFIG_CMD'     // ����ָ��
    },

    // �豸����
    features: {
        gpio: ['led', 'relay', 'sensor'],
        peripherals: ['uart', 'i2c', 'spi', 'adc'],
        sensors: ['temperature', 'humidity', 'light']
    }
};

// 银尔达DTU平台配置
const DTU_CONFIG = {
    // 是否启用DTU平台
    enabled: true,

    // DTU平台API配置
    api: {
        // DTU平台API基础地址
        apiBase: 'http://api.yinled.com',

        // DTU服务器地址和端口
        serverHost: 'dtu.yinled.com',
        serverPort: 8888,

        // 设备认证信息
        deviceId: 'CH32V307_001',
        deviceKey: '', // 需要从银尔达平台获取

        // 请求超时设置
        timeout: 10000,
        retryAttempts: 3,
        retryDelay: 1000
    },

    // 设备管理配置
    device: {
        // 心跳检测间隔（毫秒）
        heartbeatInterval: 30000,

        // 设备离线超时（毫秒）
        offlineTimeout: 5 * 60 * 1000,

        // 支持的设备类型
        supportedTypes: ['ch32v307', 'air780e'],

        // 默认设备配置
        defaultDevice: {
            type: 'ch32v307_braille_printer',
            features: ['voice_recognition', 'braille_print', 'status_report'],
            communication: 'dtu_transparent'
        }
    },

    // 数据转发配置
    forwarding: {
        // 自动转发语音识别结果到DTU设备
        autoForwardVoice: true,

        // 数据格式转换
        dataFormat: 'json',
        encoding: 'utf-8',

        // 语音识别置信度阈值
        confidenceThreshold: 0.7
    }
};

// �������û��Ѷ���˺ţ�����ʹ����ʾģʽ
// ������� enabled ��Ϊ true ��������ȷ����Կ�󣬼���ʹ����ʵ������ʶ��

module.exports = {
    xunfei: XUNFEI_CONFIG,
    air780e: AIR780E_CONFIG,
    ch32v307: CH32V307_CONFIG,
    dtu: DTU_CONFIG
};