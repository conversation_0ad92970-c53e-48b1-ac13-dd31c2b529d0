#include "plotter.h"
#include "debug.h"  // For printf
#include <string.h> // For strlen
#include <ctype.h>  // For tolower
#include <stdlib.h> // For labs

/* --- ģ�鼶(��̬)���� --- */
Plotter plotter;

// ��ǰ����λ�� (��λ: ��)
static long current_steps_x = 0;
static long current_steps_y = 0;
static long current_steps_z = 0;

// �ٶ��趨 (��������֮�����ʱ, ��λ: ΢��)
static uint16_t step_delay_us = 500; // ��Ӧ 2000 ��/��

// �Զ����ٶȿ���
static uint8_t adaptive_speed_enabled = 1;

// ��ӡģʽ����
static const PrintConfig print_mode_configs[3] = {
    // PRINT_MODE_FAST
    {200, 0.6f, 0, 30}, // step_delay_us, quality_factor, enable_compensation, max_complexity
    // PRINT_MODE_STANDARD
    {500, 0.8f, 1, 70}, // ��׼ģʽ
    // PRINT_MODE_PRECISE
    {1000, 1.0f, 1, 100} // ��ȷģʽ
};

// ���׵�������ת���� (����)
#define MM_TO_STEPS_X(mm) ((long)((mm) * (STEPS_PER_REVOLUTION / MM_PER_REVOLUTION_X)))
#define MM_TO_STEPS_Y(mm) ((long)((mm) * (STEPS_PER_REVOLUTION / MM_PER_REVOLUTION_Y)))
#define MM_TO_STEPS_Z(mm) ((long)((mm) * (STEPS_PER_REVOLUTION / MM_PER_REVOLUTION_Z)))

/*
 * ä�� (6��) ���, ����ASCII 32 (' ') �� 122 ('z')
 */
static const uint8_t braille_font_map[91] = {
    // ASCII 32-122
    0b000000, // 32: ' '
    0b011010, // 33: !
    0b010110, // 34: "
    0b001100, // 35: # (Number sign)
    0b101101, // 36: $
    0b000000, // 37: % (not defined)
    0b111100, // 38: &
    0b000100, // 39: '
    0b010111, // 40: (
    0b001111, // 41: )
    0b010010, // 42: *
    0b101110, // 43: +
    0b000010, // 44: ,
    0b100100, // 45: -
    0b010010, // 46: .
    0b001010, // 47: /
    0b011010, // 48: 0 (as j)
    0b000001, // 49: 1 (as a)
    0b000011, // 50: 2 (as b)
    0b001001, // 51: 3 (as c)
    0b011001, // 52: 4 (as d)
    0b010001, // 53: 5 (as e)
    0b001011, // 54: 6 (as f)
    0b011011, // 55: 7 (as g)
    0b010011, // 56: 8 (as h)
    0b001010, // 57: 9 (as i)
    0b000110, // 58: :
    0b001110, // 59: ;
    0b000101, // 60: < (k)
    0b101100, // 61: =
    0b010101, // 62: > (o)
    0b100110, // 63: ?
    0b110101, // 64: @
    0b000001, 0b000011, 0b001001, 0b011001, 0b010001, 0b001011, 0b011011, 0b010011,
    0b001010, 0b011010, 0b000101, 0b000111, 0b001101, 0b011101, 0b010101, 0b001111,
    0b011111, 0b010111, 0b001110, 0b011110, 0b100101, 0b100111, 0b011010, 0b101101,
    0b111101, 0b110101,
    0, 0, 0, 0, 0, 0,
    0b000001, 0b000011, 0b001001, 0b011001, 0b010001, 0b001011, 0b011011, 0b010011,
    0b001010, 0b011010, 0b000101, 0b000111, 0b001101, 0b011101, 0b010101, 0b001111,
    0b011111, 0b010111, 0b001110, 0b011110, 0b100101, 0b100111, 0b011010, 0b101101,
    0b111101, 0b110101};

/* --- ˽�к������� --- */
static void move_axis_to_steps(GPIO_TypeDef *GPIO_STEP, uint16_t PIN_STEP, GPIO_TypeDef *GPIO_DIR, uint16_t PIN_DIR, long *current_pos_steps, long target_pos_steps);
static void punch_dot(void);
static void print_braille_char(char c);

/* --- ���к������� --- */

void Plotter_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOC, ENABLE);

    GPIO_InitStructure.GPIO_Pin = X_STEP_PIN | X_DIR_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(X_STEP_PORT, &GPIO_InitStructure);

    GPIO_InitStructure.GPIO_Pin = Y_STEP_PIN | Y_DIR_PIN;
    GPIO_Init(Y_STEP_PORT, &GPIO_InitStructure);

    GPIO_InitStructure.GPIO_Pin = Z_STEP_PIN | Z_DIR_PIN;
    GPIO_Init(Z_STEP_PORT, &GPIO_InitStructure);

    GPIO_InitStructure.GPIO_Pin = ENA_PIN;
    GPIO_Init(ENA_PORT, &GPIO_InitStructure);

    GPIO_SetBits(ENA_PORT, ENA_PIN);
    printf("Braille Plotter Initialized.\r\n");

    // ��ʼ����ӡ��״̬
    plotter.state = IDLE;
    plotter.current_mode = PRINT_MODE_STANDARD;
    plotter.current_quality = QUALITY_NORMAL;

    // ��ʼ����ӡ����
    plotter.config = print_mode_configs[PRINT_MODE_STANDARD];

    // ��ʼ����ӡ״̬
    plotter.status.current_line = 0;
    plotter.status.current_char = 0;
    plotter.status.total_chars = 0;
    plotter.status.dots_printed = 0;
    plotter.status.errors_count = 0;
    plotter.status.progress_percent = 0;

    // ��ʼ������ȹ���
    plotter.compensation.backlash_x = 0.1f;
    plotter.compensation.backlash_y = 0.1f;
    plotter.compensation.enable_thermal_comp = 0;
    plotter.compensation.thermal_coefficient = 0.00001f;

    // ��ʼ��λ��
    current_steps_x = 0;
    current_steps_y = 0;
    current_steps_z = 0;

    long z_retract_steps = MM_TO_STEPS_Z(Z_RETRACT_HEIGHT);
    move_axis_to_steps(Z_STEP_PORT, Z_STEP_PIN, Z_DIR_PORT, Z_DIR_PIN, &current_steps_z, z_retract_steps);

    printf("��ӡ��ģʽ: %s, ����: %s\r\n",
           plotter.current_mode == PRINT_MODE_FAST ? "����" : plotter.current_mode == PRINT_MODE_STANDARD ? "��׼"
                                                                                                          : "��ȷ",
           plotter.current_quality == QUALITY_DRAFT ? "�ݸ�" : plotter.current_quality == QUALITY_NORMAL ? "��׼"
                                                                                                        : "�߸���");
}

void Plotter_PrintBrailleString(const char *text)
{
    if (plotter.state != IDLE)
    {
        printf("ERROR: Plotter is busy.\r\n");
        return;
    }

    printf("Starting Braille Print: \"%s\"\r\n", text);
    plotter.state = PRINTING_BRAILLE;

    // ��ʼ����ӡ״̬
    Plotter_ResetPrintStatus();
    size_t text_len = strlen(text);
    plotter.status.total_chars = text_len;

    GPIO_ResetBits(ENA_PORT, ENA_PIN);
    Delay_Ms(100);

    for (size_t i = 0; i < text_len; i++)
    {
        // ���½���״̬
        Plotter_UpdateProgress(i, text_len);

        // ��������
        if (Plotter_CheckErrors())
        {
            printf("����: ��ӡ���̷������󣬽���ֹͣ\r\n");
            Plotter_RecoverFromError();
            return;
        }

        char c = text[i];
        if (c == '\n')
        {
            plotter.status.current_line++;
            current_steps_y += MM_TO_STEPS_Y(BRAILLE_LINE_SPACING);
            move_axis_to_steps(X_STEP_PORT, X_STEP_PIN, X_DIR_PORT, X_DIR_PIN, &current_steps_x, 0);
            printf("Action: Newline (��%d��)\r\n", plotter.status.current_line);
        }
        else if (c == '\r')
        {
            // Ignore
        }
        else
        {
            print_braille_char(c);
        }

        // ÿ10���ַ����״̬
        if (i % 10 == 0 && i > 0)
        {
            printf("��ӡ����: %d/%d (%.1f%%)\r\n",
                   plotter.status.current_char, plotter.status.total_chars,
                   plotter.status.progress_percent);
        }
    }

    // ��ӡ���
    Plotter_UpdateProgress(text_len, text_len);
    printf("Print job finished. Returning to origin.\r\n");
    printf("��ӡͳ��: ������%d, ������%ld, ������%ld\r\n",
           plotter.status.total_chars, plotter.status.dots_printed, plotter.status.errors_count);

    move_axis_to_steps(X_STEP_PORT, X_STEP_PIN, X_DIR_PORT, X_DIR_PIN, &current_steps_x, 0);
    move_axis_to_steps(Y_STEP_PORT, Y_STEP_PIN, Y_DIR_PORT, Y_DIR_PIN, &current_steps_y, 0);
    long z_retract_steps = MM_TO_STEPS_Z(Z_RETRACT_HEIGHT);
    move_axis_to_steps(Z_STEP_PORT, Z_STEP_PIN, Z_DIR_PORT, Z_DIR_PIN, &current_steps_z, z_retract_steps);

    GPIO_SetBits(ENA_PORT, ENA_PIN);
    plotter.state = IDLE;
}

/* --- ˽�к������� --- */

static void move_axis_to_steps(GPIO_TypeDef *GPIO_STEP, uint16_t PIN_STEP, GPIO_TypeDef *GPIO_DIR, uint16_t PIN_DIR, long *current_pos_steps, long target_pos_steps)
{
    long delta = target_pos_steps - *current_pos_steps;
    if (delta == 0)
        return;

    // ��������ȹ���
    if (plotter.config.enable_compensation)
    {
        float compensation = 0;

        // ����X��Y��ȷ��������
        if (GPIO_STEP == X_STEP_PORT)
        {
            compensation = plotter.compensation.backlash_x;
        }
        else if (GPIO_STEP == Y_STEP_PORT)
        {
            compensation = plotter.compensation.backlash_y;
        }

        // ���ݷ������������
        if (delta < 0 && compensation > 0)
        {
            long comp_steps = (long)(compensation * STEPS_PER_REVOLUTION / MM_PER_REVOLUTION_X);
            target_pos_steps -= comp_steps;
            delta = target_pos_steps - *current_pos_steps;
        }
    }

    GPIO_WriteBit(GPIO_DIR, PIN_DIR, (delta > 0) ? Bit_SET : Bit_RESET);
    Delay_Us(2);

    // ʹ�ö�̬�ٶȿ���
    uint16_t current_delay = step_delay_us;
    if (adaptive_speed_enabled)
    {
        // ���ݵ�ǰ��ӡģʽ�����ٶ�
        float complexity = (float)plotter.status.current_char / (plotter.status.total_chars + 1);
        current_delay = Plotter_CalculateOptimalSpeed(complexity);
    }

    for (long i = 0; i < labs(delta); i++)
    {
        GPIO_SetBits(GPIO_STEP, PIN_STEP);
        Delay_Us(current_delay);
        GPIO_ResetBits(GPIO_STEP, PIN_STEP);
        Delay_Us(current_delay);

        // ��������
        if (Plotter_CheckErrors())
        {
            plotter.state = ERROR_STATE;
            break;
        }
    }
    *current_pos_steps = target_pos_steps;
}

static void punch_dot(void)
{
    long punch_target_steps = 0;
    long retract_target_steps = MM_TO_STEPS_Z(Z_RETRACT_HEIGHT);

    move_axis_to_steps(Z_STEP_PORT, Z_STEP_PIN, Z_DIR_PORT, Z_DIR_PIN, &current_steps_z, punch_target_steps);
    Delay_Ms(50);
    move_axis_to_steps(Z_STEP_PORT, Z_STEP_PIN, Z_DIR_PORT, Z_DIR_PIN, &current_steps_z, retract_target_steps);
}

static void print_braille_char(char c)
{
    char lookup_char = tolower(c);
    if (lookup_char < 32 || lookup_char > 122)
    {
        printf("Warn: Char '%c' out of range, skipping.\r\n", c);
        return;
    }

    uint8_t pattern = braille_font_map[lookup_char - 32];

    if (pattern == 0 && lookup_char != ' ')
    {
        printf("Warn: Char '%c' not in font map, skipping.\r\n", c);
    }

    printf("Printing char '%c', pattern: 0x%02X\r\n", c, pattern);

    long char_base_x_steps = current_steps_x;
    long char_base_y_steps = current_steps_y;

    move_axis_to_steps(X_STEP_PORT, X_STEP_PIN, X_DIR_PORT, X_DIR_PIN, &current_steps_x, char_base_x_steps);
    move_axis_to_steps(Y_STEP_PORT, Y_STEP_PIN, Y_DIR_PORT, Y_DIR_PIN, &current_steps_y, char_base_y_steps);

    // ͳ�Ƶ���
    uint8_t dot_count = 0;
    for (int i = 0; i < 6; i++)
    {
        if ((pattern >> i) & 1)
        {
            dot_count++;
        }
    }

    for (int i = 0; i < 6; i++)
    {
        if ((pattern >> i) & 1)
        {
            long dot_offset_x_steps = (i >= 3) ? MM_TO_STEPS_X(BRAILLE_DOT_PITCH_X) : 0;
            long dot_offset_y_steps = MM_TO_STEPS_Y((i % 3) * BRAILLE_DOT_PITCH_Y);

            long target_x = char_base_x_steps + dot_offset_x_steps;
            long target_y = char_base_y_steps + dot_offset_y_steps;

            move_axis_to_steps(X_STEP_PORT, X_STEP_PIN, X_DIR_PORT, X_DIR_PIN, &current_steps_x, target_x);
            move_axis_to_steps(Y_STEP_PORT, Y_STEP_PIN, Y_DIR_PORT, Y_DIR_PIN, &current_steps_y, target_y);

            punch_dot();
            plotter.status.dots_printed++; // ���µ���
        }
    }

    // ���ݸ��Ӷȵ��ٶ�
    if (adaptive_speed_enabled && dot_count > 4)
    {
        // ���Ӷȸߵ��ַ���ʹ�ø��ٶ�
        Delay_Ms(5);
    }

    current_steps_x = char_base_x_steps + MM_TO_STEPS_X(BRAILLE_CHAR_SPACING);
    move_axis_to_steps(X_STEP_PORT, X_STEP_PIN, X_DIR_PORT, X_DIR_PIN, &current_steps_x, current_steps_x);
    move_axis_to_steps(Y_STEP_PORT, Y_STEP_PIN, Y_DIR_PORT, Y_DIR_PIN, &current_steps_y, char_base_y_steps);
}

/* --- У׼ר�ú������� --- */
void Plotter_Calibrate_Move(char axis, uint32_t steps)
{
    if (plotter.state != IDLE)
    {
        printf("����: ��ӡ����æ, ��ȴ������.\r\n");
        return;
    }
    printf("У׼�ƶ�: %c ��, %lu ��\r\n", axis, (unsigned long)steps);

    // �ƶ�ǰ��ʹ�ܵ��
    GPIO_ResetBits(ENA_PORT, ENA_PIN);
    Delay_Ms(100);

    long start_pos = 0;
    long end_pos = steps;

    if (axis == 'X' || axis == 'x')
    {
        start_pos = current_steps_x;
        move_axis_to_steps(X_STEP_PORT, X_STEP_PIN, X_DIR_PORT, X_DIR_PIN, &current_steps_x, current_steps_x + end_pos);
    }
    else if (axis == 'Y' || axis == 'y')
    {
        start_pos = current_steps_y;
        move_axis_to_steps(Y_STEP_PORT, Y_STEP_PIN, Y_DIR_PORT, Y_DIR_PIN, &current_steps_y, current_steps_y + end_pos);
    }
    else
    {
        printf("����: ��Ч���� '%c'.\r\n", axis);
    }

    // �ƶ���������Խ��õ����Ҳ���Ա���ʹ��
    // GPIO_SetBits(ENA_PORT, ENA_PIN);
    printf("У׼�ƶ����.\r\n");
}

/* --- ��ӡģʽ������ --- */
void Plotter_SetPrintMode(PrintMode mode)
{
    if (mode >= 0 && mode < 3)
    {
        plotter.current_mode = mode;
        plotter.config = print_mode_configs[mode];
        step_delay_us = plotter.config.step_delay_us;
        printf("��ӡģʽ�ѻ���Ϊ: %s\r\n",
               mode == PRINT_MODE_FAST ? "����" : mode == PRINT_MODE_STANDARD ? "��׼"
                                                                              : "��ȷ");
    }
}

void Plotter_SetPrintQuality(PrintQuality quality)
{
    if (quality >= 0 && quality < 3)
    {
        plotter.current_quality = quality;
        printf("��ӡ�����ѻ���Ϊ: %s\r\n",
               quality == QUALITY_DRAFT ? "�ݸ�" : quality == QUALITY_NORMAL ? "��׼"
                                                                            : "�߸���");
    }
}

void Plotter_ApplyPrintConfig(const PrintConfig *config)
{
    if (config)
    {
        plotter.config = *config;
        step_delay_us = config->step_delay_us;
        printf("��ӡ�����ѻ���: ��ʱ=%dμs, ����=%.2f\r\n",
               config->step_delay_us, config->quality_factor);
    }
}

PrintConfig *Plotter_GetCurrentConfig(void)
{
    return &plotter.config;
}

/* --- ��ӡ״̬������ --- */
PrintStatus *Plotter_GetPrintStatus(void)
{
    return &plotter.status;
}

void Plotter_ResetPrintStatus(void)
{
    plotter.status.current_line = 0;
    plotter.status.current_char = 0;
    plotter.status.total_chars = 0;
    plotter.status.dots_printed = 0;
    plotter.status.errors_count = 0;
    plotter.status.progress_percent = 0;
}

void Plotter_UpdateProgress(uint32_t current_char, uint32_t total_chars)
{
    plotter.status.current_char = current_char;
    plotter.status.total_chars = total_chars;
    if (total_chars > 0)
    {
        plotter.status.progress_percent = (current_char * 100) / total_chars;
    }
}

/* --- ��ȷ���������� --- */
void Plotter_SetCompensation(const CompensationConfig *comp)
{
    if (comp)
    {
        plotter.compensation = *comp;
        printf("��ȹ����ѻ���: X=%.3f, Y=%.3f, �ȹ���=%s\r\n",
               comp->backlash_x, comp->backlash_y,
               comp->enable_thermal_comp ? "����" : "�ر�");
    }
}

void Plotter_EnableBacklashCompensation(uint8_t enable)
{
    plotter.config.enable_compensation = enable;
    printf("��������ȹ���: %s\r\n", enable ? "����" : "�ر�");
}

void Plotter_EnableThermalCompensation(uint8_t enable)
{
    plotter.compensation.enable_thermal_comp = enable;
    printf("�ȹ���: %s\r\n", enable ? "����" : "�ر�");
}

/* --- �Զ����ٶȿ��� --- */
void Plotter_SetAdaptiveSpeed(uint8_t enable)
{
    adaptive_speed_enabled = enable;
    printf("�Զ����ٶȿ���: %s\r\n", enable ? "����" : "�ر�");
}

uint16_t Plotter_CalculateOptimalSpeed(float complexity)
{
    if (!adaptive_speed_enabled)
    {
        return plotter.config.step_delay_us;
    }

    // ���ݸ��Ӷȼ����ٶ�
    uint16_t base_delay = plotter.config.step_delay_us;

    if (complexity < 0.3f)
    {
        // ���ӶȺܵͣ�ʹ�ÿ��ٶ�
        return (uint16_t)(base_delay * 0.7f);
    }
    else if (complexity > 0.7f)
    {
        // ���Ӷȸߣ�ʹ�ø��ٶ�
        return (uint16_t)(base_delay * 1.5f);
    }
    else
    {
        // �еȸ��Ӷȣ�ʹ�û����ٶ�
        return base_delay;
    }
}

/* --- ������⺺�ָ� --- */
uint8_t Plotter_CheckErrors(void)
{
    uint8_t error_detected = 0;

    // ���λ�ô���
    if (current_steps_x < -10000 || current_steps_x > 50000)
    {
        printf("����: X��λ�ó���Χ: %ld\r\n", current_steps_x);
        plotter.status.errors_count++;
        error_detected = 1;
    }

    if (current_steps_y < -10000 || current_steps_y > 50000)
    {
        printf("����: Y��λ�ó���Χ: %ld\r\n", current_steps_y);
        plotter.status.errors_count++;
        error_detected = 1;
    }

    // ���״̬����
    if (plotter.state == ERROR_STATE)
    {
        error_detected = 1;
    }

    return error_detected;
}

void Plotter_RecoverFromError(void)
{
    printf("��ʼ�����ָ�...\r\n");

    // ֹͣ���е��
    GPIO_SetBits(ENA_PORT, ENA_PIN);
    Delay_Ms(100);

    // ����״̬
    plotter.state = IDLE;

    // ����λ��
    current_steps_x = 0;
    current_steps_y = 0;
    current_steps_z = MM_TO_STEPS_Z(Z_RETRACT_HEIGHT);

    // ���ô�ӡ״̬
    Plotter_ResetPrintStatus();

    printf("�����ָ����\r\n");
}

void Plotter_ReportStatus(void)
{
    printf("=== ��ӡ��״̬���� ===\r\n");
    printf("״̬: %s\r\n",
           plotter.state == IDLE ? "����" : plotter.state == PRINTING_BRAILLE ? "��ӡ��"
                                        : plotter.state == CALIBRATING        ? "У׼��"
                                                                              : "����");

    printf("ģʽ: %s\r\n",
           plotter.current_mode == PRINT_MODE_FAST ? "����" : plotter.current_mode == PRINT_MODE_STANDARD ? "��׼"
                                                                                                          : "��ȷ");

    printf("����: %s\r\n",
           plotter.current_quality == QUALITY_DRAFT ? "�ݸ�" : plotter.current_quality == QUALITY_NORMAL ? "��׼"
                                                                                                        : "�߸���");

    printf("λ��: X=%ld, Y=%ld, Z=%ld\r\n",
           current_steps_x, current_steps_y, current_steps_z);

    printf("����: %d/%d (%.1f%%)\r\n",
           plotter.status.current_char, plotter.status.total_chars,
           plotter.status.progress_percent);

    printf("������: %ld, ������: %ld\r\n",
           plotter.status.dots_printed, plotter.status.errors_count);

    printf("�����趨: ��ʱ=%dμs, ����=%.2f\r\n",
           plotter.config.step_delay_us, plotter.config.quality_factor);

    printf("�Զ����ٶ�: %s\r\n", adaptive_speed_enabled ? "����" : "�ر�");
    printf("========================\r\n");
}