# DTU设备连接故障排除指南

## 🔍 当前系统状态检查

### ✅ 已确认正常的项目：
- [x] WebSocket服务器运行正常 (端口8081)
- [x] DTU TCP服务器运行正常 (端口8082)
- [x] 服务器地址配置正确 (***********)
- [x] 设备认证信息正确 (IMEI: 869080075169294)
- [x] 银尔达DTU平台配置完成

## 🔧 故障排除步骤

### 1. 检查DTU设备硬件状态

#### 1.1 设备指示灯检查
- **电源灯**：应为常亮（绿色或蓝色）
- **网络灯**：应为常亮或慢闪（表示4G网络连接正常）
- **数据灯**：连接时会闪烁

#### 1.2 SIM卡检查
- 确认SIM卡正确插入
- 检查SIM卡是否有足够流量（您的卡有30M/月）
- 确认SIM卡未被运营商停用

### 2. 检查银尔达平台配置

#### 2.1 设备状态检查
登录 https://dtu.yinerda.com/ 检查：
- 设备是否显示"在线"状态
- 设备参数是否正确保存
- 分组配置是否正确

#### 2.2 参数配置验证
确认以下参数设置：
```
服务器地址: ***********
服务器端口: 8081
协议类型: TCP
连接模式: 客户端
心跳间隔: 30
心跳数据: PING
数据格式: JSON
```

### 3. 网络连接检查

#### 3.1 防火墙设置
确保Windows防火墙允许端口8081和8082的入站连接：
```cmd
# 管理员权限运行
netsh advfirewall firewall add rule name="DTU WebSocket" dir=in action=allow protocol=TCP localport=8081
netsh advfirewall firewall add rule name="DTU TCP" dir=in action=allow protocol=TCP localport=8082
```

#### 3.2 网络可达性测试
从其他设备测试服务器可达性：
```cmd
telnet *********** 8081
telnet *********** 8082
```

### 4. DTU设备重启

#### 4.1 软重启
在银尔达平台上发送重启命令（如果支持）

#### 4.2 硬重启
- 断电30秒后重新上电
- 等待设备完全启动（约2-3分钟）

### 5. 替代连接方案

#### 5.1 使用备用端口
如果8081端口有问题，尝试配置8082端口：
```
服务器地址: ***********
服务器端口: 8082
协议类型: TCP
```

#### 5.2 使用公网IP（如果有）
如果您有公网IP，可以尝试：
1. 获取公网IP地址
2. 配置路由器端口转发
3. 在DTU平台配置公网IP

### 6. 日志分析

#### 6.1 查看连接尝试
监控以下日志输出：
- DTU连接监听器日志
- WebSocket服务器日志
- 系统网络连接日志

#### 6.2 常见错误信息
- **连接超时**：网络不通或防火墙阻止
- **连接拒绝**：端口未开放或服务未运行
- **认证失败**：设备未在平台正确配置

## 🆘 联系技术支持

### 银尔达技术支持
- **电话**：0755-23732189
- **说明问题**：设备无法连接到自定义服务器
- **提供信息**：
  - 设备IMEI: 869080075169294
  - 服务器地址: ***********:8081
  - 错误现象描述

### 常见支持请求
1. **设备授权问题**：设备可能需要厂家授权才能连接自定义服务器
2. **固件版本问题**：可能需要更新DTU固件
3. **参数模板问题**：可能需要特定的参数配置模板

## 📋 测试检查清单

### 基础检查
- [ ] DTU设备通电正常
- [ ] SIM卡插入正确
- [ ] 设备指示灯状态正常
- [ ] 银尔达平台设备显示在线

### 网络检查
- [ ] 服务器IP地址正确 (***********)
- [ ] 端口8081和8082开放
- [ ] 防火墙规则配置正确
- [ ] 网络连通性正常

### 配置检查
- [ ] DTU平台参数配置正确
- [ ] 设备分组配置正确
- [ ] 协议类型设置为TCP
- [ ] 连接模式设置为客户端

### 服务检查
- [ ] WebSocket服务器运行正常
- [ ] DTU连接监听器运行正常
- [ ] 日志输出正常
- [ ] 端口监听状态正常

## 🎯 预期结果

配置成功后，您应该看到：

```
🔗 DTU设备连接: xxx.xxx.xxx.xxx:xxxx
📨 收到DTU数据: {"type":"dtu_register","imei":"869080075169294"}
🔐 DTU设备注册: IMEI=869080075169294
💓 DTU心跳: DTU_xxxxxxxxx
```

## 📞 紧急联系方式

如果所有步骤都无法解决问题：

1. **保存所有日志信息**
2. **记录详细的错误现象**
3. **联系银尔达技术支持**：0755-23732189
4. **说明您正在进行自定义服务器集成**
