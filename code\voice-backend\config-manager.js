// CH32V307智能语音识别盲文打印系统 - 配置管理工具
// 提供配置加载、验证、热重载等功能
// 版本: 2.0.0

const fs = require('fs');
const path = require('path');

class ConfigManager {
    constructor() {
        this.config = null;
        this.watchers = [];
        this.listeners = [];
        this.lastLoadTime = null;
    }

    /**
     * 加载配置文件
     * @param {string} environment - 环境名称 (development, production, test)
     * @returns {Object} 配置对象
     */
    loadConfig(environment = null) {
        try {
            const env = environment || process.env.NODE_ENV || 'development';
            console.log(`[配置管理器] 加载环境配置: ${env}`);

            // 加载基础配置
            delete require.cache[require.resolve('./config.js')];
            const baseConfig = require('./config.js');

            // 环境名称映射
            const envFileMap = {
                'development': 'dev',
                'production': 'prod',
                'test': 'test'
            };

            // 尝试加载环境特定配置
            const envFileName = envFileMap[env] || env;
            const envConfigPath = path.join(__dirname, `config.${envFileName}.js`);
            let envConfig = {};

            if (fs.existsSync(envConfigPath)) {
                delete require.cache[require.resolve(envConfigPath)];
                envConfig = require(envConfigPath);
                console.log(`[配置管理器] 加载环境特定配置: ${envConfigPath}`);
            } else {
                console.log(`[配置管理器] 环境特定配置文件不存在: ${envConfigPath}`);
            }

            // 合并配置
            this.config = this.mergeConfigs(baseConfig, envConfig);
            this.lastLoadTime = new Date();

            console.log(`[配置管理器] 配置加载完成: ${this.config.system.name} v${this.config.system.version}`);

            // 触发配置更新事件
            this.notifyListeners('configLoaded', this.config);

            return this.config;
        } catch (error) {
            console.error('[配置管理器] 配置加载失败:', error.message);
            throw new Error(`配置加载失败: ${error.message}`);
        }
    }

    /**
     * 深度合并配置对象
     * @param {Object} base - 基础配置
     * @param {Object} override - 覆盖配置
     * @returns {Object} 合并后的配置
     */
    mergeConfigs(base, override) {
        const result = JSON.parse(JSON.stringify(base));

        function deepMerge(target, source) {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    if (!target[key]) target[key] = {};
                    deepMerge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        }

        if (override) {
            deepMerge(result, override);
        }

        return result;
    }

    /**
     * 获取配置值
     * @param {string} path - 配置路径 (例如: 'server.websocket.port')
     * @param {*} defaultValue - 默认值
     * @returns {*} 配置值
     */
    get(path, defaultValue = null) {
        if (!this.config) {
            throw new Error('配置未加载，请先调用 loadConfig()');
        }

        const keys = path.split('.');
        let value = this.config;

        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }

        return value;
    }

    /**
     * 设置配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     */
    set(path, value) {
        if (!this.config) {
            throw new Error('配置未加载，请先调用 loadConfig()');
        }

        const keys = path.split('.');
        let target = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }

        const lastKey = keys[keys.length - 1];
        const oldValue = target[lastKey];
        target[lastKey] = value;

        console.log(`[配置管理器] 配置更新: ${path} = ${JSON.stringify(value)}`);

        // 触发配置更新事件
        this.notifyListeners('configChanged', { path, oldValue, newValue: value });
    }

    /**
     * 验证配置完整性
     * @returns {Object} 验证结果
     */
    validateConfig() {
        if (!this.config) {
            return { valid: false, errors: ['配置未加载'] };
        }

        const errors = [];
        const warnings = [];

        // 必需配置检查
        const requiredPaths = [
            'system.name',
            'system.version',
            'server.websocket.port'
        ];

        for (const path of requiredPaths) {
            if (this.get(path) === null) {
                errors.push(`必需配置缺失: ${path}`);
            }
        }

        // 条件配置检查
        if (this.get('xunfei.enabled') && !this.get('xunfei.appId')) {
            errors.push('讯飞语音识别已启用但缺少 appId');
        }

        if (this.get('dtu.enabled') && !this.get('dtu.device.deviceId')) {
            errors.push('DTU平台已启用但缺少设备ID');
        }

        // 打印配置检查
        if (this.get('printing')) {
            this.validatePrintingConfig(errors, warnings);
        }

        // 配置合理性检查
        const wsPort = this.get('server.websocket.port');
        if (wsPort && (wsPort < 1024 || wsPort > 65535)) {
            warnings.push(`WebSocket端口可能不合理: ${wsPort}`);
        }

        const result = {
            valid: errors.length === 0,
            errors,
            warnings,
            timestamp: new Date().toISOString()
        };

        if (result.valid) {
            console.log('[配置管理器] 配置验证通过');
        } else {
            console.error('[配置管理器] 配置验证失败:', errors);
        }

        if (warnings.length > 0) {
            console.warn('[配置管理器] 配置警告:', warnings);
        }

        return result;
    }

    /**
     * 启用配置文件监控
     * @param {Array} files - 要监控的文件列表
     */
    enableFileWatching(files = ['./config.js']) {
        if (this.watchers.length > 0) {
            console.log('[配置管理器] 文件监控已启用');
            return;
        }

        for (const file of files) {
            const filePath = path.resolve(__dirname, file);

            if (fs.existsSync(filePath)) {
                const watcher = fs.watchFile(filePath, { interval: 1000 }, (curr, prev) => {
                    if (curr.mtime !== prev.mtime) {
                        console.log(`[配置管理器] 检测到配置文件变更: ${file}`);
                        this.reloadConfig();
                    }
                });

                this.watchers.push({ file: filePath, watcher });
                console.log(`[配置管理器] 开始监控配置文件: ${file}`);
            } else {
                console.warn(`[配置管理器] 配置文件不存在，跳过监控: ${file}`);
            }
        }
    }

    /**
     * 禁用配置文件监控
     */
    disableFileWatching() {
        for (const { file, watcher } of this.watchers) {
            fs.unwatchFile(file);
            console.log(`[配置管理器] 停止监控配置文件: ${file}`);
        }
        this.watchers = [];
    }

    /**
     * 重新加载配置
     */
    reloadConfig() {
        try {
            const oldConfig = this.config;
            this.loadConfig();

            console.log('[配置管理器] 配置重新加载完成');
            this.notifyListeners('configReloaded', { oldConfig, newConfig: this.config });
        } catch (error) {
            console.error('[配置管理器] 配置重新加载失败:', error.message);
            this.notifyListeners('configReloadError', error);
        }
    }

    /**
     * 添加配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    addListener(listener) {
        this.listeners.push(listener);
    }

    /**
     * 移除配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知所有监听器
     * @param {string} event - 事件类型
     * @param {*} data - 事件数据
     */
    notifyListeners(event, data) {
        for (const listener of this.listeners) {
            try {
                listener(event, data);
            } catch (error) {
                console.error('[配置管理器] 监听器执行错误:', error.message);
            }
        }
    }

    /**
     * 验证打印配置
     * @param {Array} errors - 错误数组
     * @param {Array} warnings - 警告数组
     */
    validatePrintingConfig(errors, warnings) {
        // 验证速度等级配置
        const speedLevels = this.get('printing.speed_levels');
        if (speedLevels) {
            ['fast', 'standard', 'precise'].forEach(level => {
                const stepDelay = this.get(`printing.speed_levels.${level}.step_delay_us`);
                if (stepDelay !== null && (stepDelay <= 0 || stepDelay > 10000)) {
                    warnings.push(`打印速度等级 ${level} 的 step_delay_us 值可能不合理: ${stepDelay}`);
                }
            });
        }

        // 验证校准参数
        const calibration = this.get('printing.precision_settings.calibration');
        if (calibration) {
            const stepsPerRev = this.get('printing.precision_settings.calibration.steps_per_revolution');
            if (stepsPerRev !== null && (stepsPerRev <= 0 || stepsPerRev > 10000)) {
                errors.push(`打印校准参数 steps_per_revolution 无效: ${stepsPerRev}`);
            }

            ['mm_per_revolution_x', 'mm_per_revolution_y', 'mm_per_revolution_z'].forEach(param => {
                const value = this.get(`printing.precision_settings.calibration.${param}`);
                if (value !== null && (value <= 0 || value > 100)) {
                    warnings.push(`打印校准参数 ${param} 可能不合理: ${value}`);
                }
            });
        }

        // 验证任务管理配置
        const maxQueue = this.get('printing.task_management.max_queue_size');
        if (maxQueue !== null && (maxQueue <= 0 || maxQueue > 100)) {
            warnings.push(`打印队列最大大小可能不合理: ${maxQueue}`);
        }

        const timeout = this.get('printing.task_management.timeout_ms');
        if (timeout !== null && (timeout < 1000 || timeout > 3600000)) {
            warnings.push(`打印超时时间可能不合理: ${timeout}ms`);
        }
    }

    /**
     * 获取打印模式配置
     * @param {string} mode - 打印模式名称
     * @returns {Object|null} 打印模式配置
     */
    getPrintMode(mode) {
        return this.get(`printing.print_modes.${mode}`);
    }

    /**
     * 设置打印模式配置
     * @param {string} mode - 打印模式名称
     * @param {Object} config - 打印模式配置
     */
    setPrintMode(mode, config) {
        this.set(`printing.print_modes.${mode}`, config);
        console.log(`[配置管理器] 打印模式 ${mode} 已更新`);
    }

    /**
     * 获取打印速度等级配置
     * @param {string} level - 速度等级 (fast/standard/precise)
     * @returns {Object|null} 速度等级配置
     */
    getPrintSpeedLevel(level) {
        return this.get(`printing.speed_levels.${level}`);
    }

    /**
     * 设置打印速度等级配置
     * @param {string} level - 速度等级
     * @param {Object} config - 速度等级配置
     */
    setPrintSpeedLevel(level, config) {
        // 验证配置
        if (!config.step_delay_us || config.step_delay_us <= 0) {
            throw new Error(`无效的 step_delay_us 值: ${config.step_delay_us}`);
        }

        this.set(`printing.speed_levels.${level}`, config);
        console.log(`[配置管理器] 打印速度等级 ${level} 已更新`);
    }

    /**
     * 获取打印精度参数
     * @param {string} category - 参数类别 (calibration/braille_dimensions/compensation)
     * @returns {Object|null} 精度参数配置
     */
    getPrintPrecisionSettings(category) {
        return this.get(`printing.precision_settings.${category}`);
    }

    /**
     * 设置打印精度参数
     * @param {string} category - 参数类别
     * @param {Object} settings - 精度参数设置
     */
    setPrintPrecisionSettings(category, settings) {
        this.set(`printing.precision_settings.${category}`, settings);
        console.log(`[配置管理器] 打印精度参数 ${category} 已更新`);
    }

    /**
     * 获取打印任务管理配置
     * @returns {Object|null} 任务管理配置
     */
    getPrintTaskManagement() {
        return this.get('printing.task_management');
    }

    /**
     * 设置打印任务管理配置
     * @param {Object} config - 任务管理配置
     */
    setPrintTaskManagement(config) {
        // 验证关键参数
        if (config.max_queue_size && config.max_queue_size <= 0) {
            throw new Error(`无效的 max_queue_size 值: ${config.max_queue_size}`);
        }
        if (config.timeout_ms && config.timeout_ms <= 0) {
            throw new Error(`无效的 timeout_ms 值: ${config.timeout_ms}`);
        }

        this.set('printing.task_management', config);
        console.log('[配置管理器] 打印任务管理配置已更新');
    }

    /**
     * 获取完整的打印配置
     * @returns {Object|null} 完整的打印配置
     */
    getPrintingConfig() {
        return this.get('printing');
    }

    /**
     * 应用打印模式预设
     * @param {string} presetName - 预设名称 (draft/normal/high_quality)
     * @returns {Object} 应用的配置
     */
    applyPrintModePreset(presetName) {
        const preset = this.getPrintMode(presetName);
        if (!preset) {
            throw new Error(`打印模式预设不存在: ${presetName}`);
        }

        const appliedConfig = {
            mode: presetName,
            speed_level: preset.speed_level,
            quality_factor: preset.quality_factor,
            enable_compensation: preset.enable_compensation,
            timestamp: new Date().toISOString()
        };

        // 触发配置应用事件
        this.notifyListeners('printModeApplied', appliedConfig);

        console.log(`[配置管理器] 已应用打印模式预设: ${presetName}`);
        return appliedConfig;
    }

    /**
     * 获取配置摘要信息
     * @returns {Object} 配置摘要
     */
    getConfigSummary() {
        if (!this.config) {
            return { loaded: false };
        }

        return {
            loaded: true,
            lastLoadTime: this.lastLoadTime,
            environment: this.config.system.environment,
            version: this.config.system.version,
            features: {
                websocket: !!this.config.server?.websocket?.port,
                xunfei: this.config.xunfei?.enabled || false,
                dtu: this.config.dtu?.enabled || false,
                http: this.config.server?.http?.enabled || false,
                printing: !!this.config.printing
            },
            printing: this.config.printing ? {
                speed_levels: Object.keys(this.config.printing.speed_levels || {}),
                print_modes: Object.keys(this.config.printing.print_modes || {}),
                monitoring_enabled: this.config.printing.monitoring?.enable_realtime_status || false,
                error_handling_enabled: this.config.printing.error_handling?.enable_auto_recovery || false
            } : null,
            monitoring: this.watchers.length > 0
        };
    }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

module.exports = configManager;
