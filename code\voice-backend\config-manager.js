// CH32V307智能语音识别盲文打印系统 - 配置管理工具
// 提供配置加载、验证、热重载等功能
// 版本: 2.0.0

const fs = require('fs');
const path = require('path');

class ConfigManager {
    constructor() {
        this.config = null;
        this.watchers = [];
        this.listeners = [];
        this.lastLoadTime = null;
    }

    /**
     * 加载配置文件
     * @param {string} environment - 环境名称 (development, production, test)
     * @returns {Object} 配置对象
     */
    loadConfig(environment = null) {
        try {
            const env = environment || process.env.NODE_ENV || 'development';
            console.log(`[配置管理器] 加载环境配置: ${env}`);

            // 加载基础配置
            delete require.cache[require.resolve('./config.js')];
            const baseConfig = require('./config.js');

            // 环境名称映射
            const envFileMap = {
                'development': 'dev',
                'production': 'prod',
                'test': 'test'
            };

            // 尝试加载环境特定配置
            const envFileName = envFileMap[env] || env;
            const envConfigPath = path.join(__dirname, `config.${envFileName}.js`);
            let envConfig = {};

            if (fs.existsSync(envConfigPath)) {
                delete require.cache[require.resolve(envConfigPath)];
                envConfig = require(envConfigPath);
                console.log(`[配置管理器] 加载环境特定配置: ${envConfigPath}`);
            } else {
                console.log(`[配置管理器] 环境特定配置文件不存在: ${envConfigPath}`);
            }

            // 合并配置
            this.config = this.mergeConfigs(baseConfig, envConfig);
            this.lastLoadTime = new Date();

            console.log(`[配置管理器] 配置加载完成: ${this.config.system.name} v${this.config.system.version}`);

            // 触发配置更新事件
            this.notifyListeners('configLoaded', this.config);

            return this.config;
        } catch (error) {
            console.error('[配置管理器] 配置加载失败:', error.message);
            throw new Error(`配置加载失败: ${error.message}`);
        }
    }

    /**
     * 深度合并配置对象
     * @param {Object} base - 基础配置
     * @param {Object} override - 覆盖配置
     * @returns {Object} 合并后的配置
     */
    mergeConfigs(base, override) {
        const result = JSON.parse(JSON.stringify(base));

        function deepMerge(target, source) {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    if (!target[key]) target[key] = {};
                    deepMerge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        }

        if (override) {
            deepMerge(result, override);
        }

        return result;
    }

    /**
     * 获取配置值
     * @param {string} path - 配置路径 (例如: 'server.websocket.port')
     * @param {*} defaultValue - 默认值
     * @returns {*} 配置值
     */
    get(path, defaultValue = null) {
        if (!this.config) {
            throw new Error('配置未加载，请先调用 loadConfig()');
        }

        const keys = path.split('.');
        let value = this.config;

        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }

        return value;
    }

    /**
     * 设置配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     */
    set(path, value) {
        if (!this.config) {
            throw new Error('配置未加载，请先调用 loadConfig()');
        }

        const keys = path.split('.');
        let target = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }

        const lastKey = keys[keys.length - 1];
        const oldValue = target[lastKey];
        target[lastKey] = value;

        console.log(`[配置管理器] 配置更新: ${path} = ${JSON.stringify(value)}`);

        // 触发配置更新事件
        this.notifyListeners('configChanged', { path, oldValue, newValue: value });
    }

    /**
     * 验证配置完整性
     * @returns {Object} 验证结果
     */
    validateConfig() {
        if (!this.config) {
            return { valid: false, errors: ['配置未加载'] };
        }

        const errors = [];
        const warnings = [];

        // 必需配置检查
        const requiredPaths = [
            'system.name',
            'system.version',
            'server.websocket.port'
        ];

        for (const path of requiredPaths) {
            if (this.get(path) === null) {
                errors.push(`必需配置缺失: ${path}`);
            }
        }

        // 条件配置检查
        if (this.get('xunfei.enabled') && !this.get('xunfei.appId')) {
            errors.push('讯飞语音识别已启用但缺少 appId');
        }

        if (this.get('dtu.enabled') && !this.get('dtu.device.deviceId')) {
            errors.push('DTU平台已启用但缺少设备ID');
        }

        // 配置合理性检查
        const wsPort = this.get('server.websocket.port');
        if (wsPort && (wsPort < 1024 || wsPort > 65535)) {
            warnings.push(`WebSocket端口可能不合理: ${wsPort}`);
        }

        const result = {
            valid: errors.length === 0,
            errors,
            warnings,
            timestamp: new Date().toISOString()
        };

        if (result.valid) {
            console.log('[配置管理器] 配置验证通过');
        } else {
            console.error('[配置管理器] 配置验证失败:', errors);
        }

        if (warnings.length > 0) {
            console.warn('[配置管理器] 配置警告:', warnings);
        }

        return result;
    }

    /**
     * 启用配置文件监控
     * @param {Array} files - 要监控的文件列表
     */
    enableFileWatching(files = ['./config.js']) {
        if (this.watchers.length > 0) {
            console.log('[配置管理器] 文件监控已启用');
            return;
        }

        for (const file of files) {
            const filePath = path.resolve(__dirname, file);

            if (fs.existsSync(filePath)) {
                const watcher = fs.watchFile(filePath, { interval: 1000 }, (curr, prev) => {
                    if (curr.mtime !== prev.mtime) {
                        console.log(`[配置管理器] 检测到配置文件变更: ${file}`);
                        this.reloadConfig();
                    }
                });

                this.watchers.push({ file: filePath, watcher });
                console.log(`[配置管理器] 开始监控配置文件: ${file}`);
            } else {
                console.warn(`[配置管理器] 配置文件不存在，跳过监控: ${file}`);
            }
        }
    }

    /**
     * 禁用配置文件监控
     */
    disableFileWatching() {
        for (const { file, watcher } of this.watchers) {
            fs.unwatchFile(file);
            console.log(`[配置管理器] 停止监控配置文件: ${file}`);
        }
        this.watchers = [];
    }

    /**
     * 重新加载配置
     */
    reloadConfig() {
        try {
            const oldConfig = this.config;
            this.loadConfig();

            console.log('[配置管理器] 配置重新加载完成');
            this.notifyListeners('configReloaded', { oldConfig, newConfig: this.config });
        } catch (error) {
            console.error('[配置管理器] 配置重新加载失败:', error.message);
            this.notifyListeners('configReloadError', error);
        }
    }

    /**
     * 添加配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    addListener(listener) {
        this.listeners.push(listener);
    }

    /**
     * 移除配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知所有监听器
     * @param {string} event - 事件类型
     * @param {*} data - 事件数据
     */
    notifyListeners(event, data) {
        for (const listener of this.listeners) {
            try {
                listener(event, data);
            } catch (error) {
                console.error('[配置管理器] 监听器执行错误:', error.message);
            }
        }
    }

    /**
     * 获取配置摘要信息
     * @returns {Object} 配置摘要
     */
    getConfigSummary() {
        if (!this.config) {
            return { loaded: false };
        }

        return {
            loaded: true,
            lastLoadTime: this.lastLoadTime,
            environment: this.config.system.environment,
            version: this.config.system.version,
            features: {
                websocket: !!this.config.server?.websocket?.port,
                xunfei: this.config.xunfei?.enabled || false,
                dtu: this.config.dtu?.enabled || false,
                http: this.config.server?.http?.enabled || false
            },
            monitoring: this.watchers.length > 0
        };
    }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

module.exports = configManager;
