<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>CH32V307 DTU Control</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1890ff;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .form-group {
            margin: 15px 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .status-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }

        .status-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }

        .log {
            background: #1f1f1f;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            margin: 15px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>&#x1F4E1; CH32V307 &#x94F6;&#x5C14;&#x8FBE;DTU&#x63A7;&#x5236;&#x53F0;</h1>

        <div class="form-group">
            <label>&#x8BBE;&#x5907;ID:</label>
            <input type="text" id="deviceId" value="CH32V307_001" placeholder="&#x8F93;&#x5165;&#x8BBE;&#x5907;ID">
        </div>

        <div class="form-group">
            <label>&#x8BBE;&#x5907;&#x5BC6;&#x94A5;:</label>
            <input type="text" id="deviceKey"
                placeholder="&#x4ECE;&#x94F6;&#x5C14;&#x8FBE;&#x5E73;&#x53F0;&#x83B7;&#x53D6;">
        </div>

        <div class="form-group">
            <label>&#x53D1;&#x9001;&#x6587;&#x5B57;:</label>
            <input type="text" id="textInput"
                placeholder="&#x8F93;&#x5165;&#x8981;&#x53D1;&#x9001;&#x7684;&#x6587;&#x5B57;"
                onkeypress="if(event.key==='Enter') sendText()">
        </div>

        <button class="btn" onclick="sendText()">&#x1F4E8; &#x53D1;&#x9001;&#x6587;&#x5B57;</button>
        <button class="btn" onclick="sendQuickText('&#x4F60;&#x597D;&#x4E16;&#x754C;')">&#x5FEB;&#x6377;:
            &#x4F60;&#x597D;&#x4E16;&#x754C;</button>
        <button class="btn" onclick="sendQuickText('&#x7CFB;&#x7EDF;&#x6D4B;&#x8BD5;')">&#x5FEB;&#x6377;:
            &#x7CFB;&#x7EDF;&#x6D4B;&#x8BD5;</button>

        <div id="status" class="status" style="display: none;"></div>

        <div class="form-group">
            <label>&#x64CD;&#x4F5C;&#x65E5;&#x5FD7;:</label>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let sendCount = 0;

        function addLog(message) {
            const log = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            log.innerHTML = `[${time}] ${message}\n` + log.innerHTML;
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.className = `status status-${type}`;
            status.textContent = message;
            status.style.display = 'block';
            setTimeout(() => status.style.display = 'none', 3000);
        }

        async function sendText() {
            const deviceId = document.getElementById('deviceId').value.trim();
            const deviceKey = document.getElementById('deviceKey').value.trim();
            const text = document.getElementById('textInput').value.trim();

            if (!deviceId) {
                showStatus('&#x8BF7;&#x8F93;&#x5165;&#x8BBE;&#x5907;ID', 'error');
                return;
            }

            if (!deviceKey) {
                showStatus('&#x8BF7;&#x8F93;&#x5165;&#x8BBE;&#x5907;&#x5BC6;&#x94A5;', 'error');
                return;
            }

            if (!text) {
                showStatus('&#x8BF7;&#x8F93;&#x5165;&#x8981;&#x53D1;&#x9001;&#x7684;&#x6587;&#x5B57;', 'error');
                return;
            }

            sendCount++;
            addLog(`&#x1F4E4; &#x53D1;&#x9001;&#x7B2C;${sendCount}&#x6B21;: "${text}"`);

            try {
                // &#x6A21;&#x62DF;&#x53D1;&#x9001;&#x5230;&#x94F6;&#x5C14;&#x8FBE;DTU&#x5E73;&#x53F0;
                const payload = {
                    device_id: deviceId,
                    data: JSON.stringify({
                        type: "text_message",
                        text: text,
                        timestamp: Date.now()
                    })
                };

                addLog(`&#x1F310; &#x8FDE;&#x63A5;&#x94F6;&#x5C14;&#x8FBE;DTU&#x5E73;&#x53F0;...`);

                // &#x8FD9;&#x91CC;&#x9700;&#x8981;&#x66FF;&#x6362;&#x4E3A;&#x5B9E;&#x9645;&#x7684;&#x94F6;&#x5C14;&#x8FBE;API
                // const response = await fetch('http://api.yinled.com/device/send', {
                //     method: 'POST',
                //     headers: {
                //         'Content-Type': 'application/json',
                //         'Authorization': `Bearer ${deviceKey}`
                //     },
                //     body: JSON.stringify(payload)
                // });

                // &#x6A21;&#x62DF;&#x6210;&#x529F;&#x54CD;&#x5E94;
                setTimeout(() => {
                    showStatus('&#x6587;&#x5B57;&#x53D1;&#x9001;&#x6210;&#x529F;', 'success');
                    addLog(`&#x2705; &#x53D1;&#x9001;&#x6210;&#x529F;: "${text}"`);
                    document.getElementById('textInput').value = '';
                }, 500);

            } catch (error) {
                showStatus('&#x53D1;&#x9001;&#x5931;&#x8D25;: ' + error.message, 'error');
                addLog(`&#x274C; &#x53D1;&#x9001;&#x5931;&#x8D25;: ${error.message}`);
            }
        }

        function sendQuickText(text) {
            document.getElementById('textInput').value = text;
            sendText();
        }

        // &#x521D;&#x59CB;&#x5316;
        window.onload = function () {
            addLog('&#x1F680; DTU&#x63A7;&#x5236;&#x53F0;&#x542F;&#x52A8;&#x6210;&#x529F;');
            addLog('&#x1F4A1; &#x8BF7;&#x5728;&#x94F6;&#x5C14;&#x8FBE;&#x5E73;&#x53F0;&#x83B7;&#x53D6;&#x8BBE;&#x5907;&#x5BC6;&#x94A5;');
        };
    </script>
</body>

</html>