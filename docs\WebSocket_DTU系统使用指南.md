# WebSocket+DTU智能语音识别盲文打印系统使用指南

## 📋 目录

1. [系统概述](#系统概述)
2. [快速启动](#快速启动)
3. [系统配置](#系统配置)
4. [功能使用](#功能使用)
5. [故障排除](#故障排除)
6. [维护指南](#维护指南)

## 🎯 系统概述

### 系统架构
WebSocket+DTU智能语音识别盲文打印系统采用混合通信架构：
```
前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
```

### 核心组件
- **前端界面**: Web浏览器访问 http://localhost:4000
- **WebSocket后端**: Node.js服务器 (端口8081)
- **DTU平台**: 银尔达DTU平台 (dtu.yinled.com:8888)
- **4G设备**: Air780e模块 (IMEI: 869080075169294)
- **控制器**: CH32V307微控制器
- **语音识别**: 讯飞语音API

## 🚀 快速启动

### 1. 系统启动

#### 方式一：完整启动（推荐）
```bash
# 双击运行主启动脚本
start_websocket_dtu_system.bat
```

**启动流程**:
1. ✅ 检查Node.js环境
2. ✅ 验证项目文件完整性
3. ✅ 自动安装依赖包
4. ✅ 启动WebSocket后端服务器
5. ✅ 启动前端Web界面
6. ✅ 显示系统状态和访问地址

#### 方式二：快速启动（开发调试）
```bash
# 双击运行快速启动脚本
quick_start_websocket_dtu.bat
```

### 2. 系统访问

启动成功后，系统会显示以下访问信息：

```
📊 服务状态:
  • WebSocket+DTU后端: http://localhost:8081
  • 前端Web界面:      http://localhost:4000
  • 银尔达DTU平台:    dtu.yinled.com:8888

🌐 访问地址:
  • 主界面:          http://localhost:4000
  • WebSocket测试:   ws://localhost:8081/voice-ws
  • DTU控制台:       http://localhost:4000/dtu-console
```

### 3. 系统状态检查

```bash
# 运行状态检查脚本
check_websocket_dtu_status.bat
```

检查项目包括：
- Node.js环境状态
- 端口占用情况
- 进程运行状态
- 项目文件完整性
- 依赖包安装状态
- 网络连接测试
- 服务响应测试

## ⚙️ 系统配置

### 1. 后端配置文件

**位置**: `code/voice-backend/config.js`

```javascript
// WebSocket服务器配置
server: {
    websocket: {
        host: '0.0.0.0',        // 监听地址
        port: 8081,             // WebSocket端口
        path: '/voice-ws',      // WebSocket路径
        maxConnections: 100,    // 最大连接数
        heartbeatInterval: 30000, // 心跳间隔(ms)
        connectionTimeout: 60000  // 连接超时(ms)
    }
}

// 银尔达DTU配置
dtu: {
    enabled: true,
    platform: 'yinled',
    endpoint: 'dtu.yinled.com:8888',
    device: {
        imei: '869080075169294',
        iccid: '898604021024C0050919',
        model: 'M100P'
    }
}

// 讯飞语音识别配置
xunfei: {
    enabled: true,
    appId: 'ebde7329',
    apiKey: '0e141d1d1e1a2a73e1691f9330e71211',
    apiSecret: 'NzViOTRlNWNkNTlmYzQxMTYwMTFiYzUy'
}
```

### 2. 环境变量配置

可通过环境变量覆盖默认配置：

```bash
# WebSocket配置
set WS_HOST=0.0.0.0
set WS_PORT=8081
set WS_PATH=/voice-ws

# DTU配置
set DTU_ENABLED=true
set DTU_ENDPOINT=dtu.yinled.com:8888
set DTU_IMEI=869080075169294

# 讯飞配置
set XUNFEI_ENABLED=true
set XUNFEI_APP_ID=your_app_id
set XUNFEI_API_KEY=your_api_key
```

### 3. Air780e设备配置

**脚本位置**: `code/01.0/air780e_websocket_dtu.lua`

```lua
-- 设备基本信息
local config = {
    device_id = "braille_printer_001",
    device_type = "ch32v307_braille_printer",
    
    -- 银尔达DTU配置
    dtu = {
        enabled = true,
        server = "dtu.yinled.com",
        port = 8888,
        imei = "869080075169294",
        iccid = "898604021024C0050919"
    },
    
    -- WebSocket备用配置
    websocket = {
        enabled = true,
        server = "your-backup-server.com",
        port = 8081,
        path = "/voice-ws"
    }
}
```

## 🎮 功能使用

### 1. 语音识别打印

#### 步骤流程：
1. **打开Web界面**: 访问 http://localhost:4000
2. **检查连接状态**: 确认WebSocket和DTU连接正常
3. **开始语音录制**: 点击"开始录音"按钮
4. **语音输入**: 清晰说出要打印的内容
5. **停止录制**: 点击"停止录音"按钮
6. **查看识别结果**: 系统显示识别的文字内容
7. **确认打印**: 点击"发送到打印机"按钮
8. **监控进度**: 实时查看打印状态

#### 语音识别技巧：
- 🎤 **环境要求**: 安静环境，减少背景噪音
- 🗣️ **发音要求**: 普通话标准，语速适中
- ⏱️ **录音时长**: 建议单次录音不超过30秒
- 📝 **内容建议**: 简短句子，避免过长段落

### 2. 手动文本打印

#### 操作步骤：
1. **文本输入**: 在Web界面文本框输入内容
2. **格式检查**: 确认文本格式正确
3. **发送打印**: 点击"直接打印"按钮
4. **状态监控**: 查看打印进度和状态

#### 文本格式要求：
- 📝 **字符支持**: 中文、英文、数字、常用标点
- 📏 **长度限制**: 单次打印建议不超过100字符
- 🔤 **编码格式**: UTF-8编码
- 📋 **特殊字符**: 避免使用特殊符号

### 3. 设备状态监控

#### 实时状态显示：
- **WebSocket连接**: 🟢 已连接 / 🔴 断开
- **DTU平台状态**: 🟢 在线 / 🔴 离线
- **设备连接**: 🟢 正常 / 🔴 异常
- **打印状态**: 🟢 就绪 / 🟡 打印中 / 🔴 错误

#### 状态指示说明：
- **🟢 绿色**: 正常工作状态
- **🟡 黄色**: 工作中或警告状态
- **🔴 红色**: 错误或断开状态
- **⚪ 灰色**: 未连接或禁用状态

### 4. 系统控制功能

#### 控制选项：
- **R - 重启系统**: 重新启动所有服务
- **S - 查看状态**: 显示详细系统状态
- **T - 测试连接**: 测试各组件连接
- **H - 显示帮助**: 查看帮助信息
- **Q - 退出系统**: 安全关闭系统

#### 使用方法：
```bash
# 在主启动脚本窗口中输入对应字母
请选择操作 (R/S/T/H/Q): S
```

## 🔧 故障排除

### 1. 常见问题诊断

#### 问题：WebSocket连接失败
**症状**: 前端显示"WebSocket连接断开"
**解决方案**:
```bash
# 1. 检查后端服务状态
netstat -an | findstr :8081

# 2. 重启WebSocket服务
# 在主启动脚本中按 R 重启

# 3. 检查防火墙设置
# 确保8081端口未被阻止
```

#### 问题：DTU平台连接异常
**症状**: DTU状态显示离线
**解决方案**:
```bash
# 1. 测试网络连接
ping dtu.yinled.com

# 2. 检查设备信息
# 确认IMEI: 869080075169294
# 确认ICCID: 898604021024C0050919

# 3. 查看DTU平台状态
# 访问银尔达DTU控制台
```

#### 问题：语音识别失败
**症状**: 录音后无识别结果
**解决方案**:
```bash
# 1. 检查讯飞API配置
# 确认appId、apiKey、apiSecret正确

# 2. 测试麦克风
# 在浏览器中测试麦克风权限

# 3. 检查网络连接
# 确保能访问讯飞API服务器
```

#### 问题：打印机无响应
**症状**: 发送打印指令后设备无动作
**解决方案**:
```bash
# 1. 检查硬件连接
# 确认CH32V307与Air780e连接正常

# 2. 检查串口通信
# 验证UART3通信是否正常

# 3. 检查固件状态
# 确认CH32V307固件已更新到WebSocket+DTU版本
```

### 2. 日志分析

#### 查看系统日志：
- **WebSocket后端日志**: 查看"WebSocket+DTU后端服务器"窗口
- **前端日志**: 浏览器F12开发者工具Console
- **DTU平台日志**: 银尔达DTU控制台
- **系统状态日志**: 运行状态检查脚本

#### 常见日志信息：
```
✅ WebSocket服务器启动成功 - 端口: 8081
✅ DTU连接建立成功 - IMEI: 869080075169294
✅ 讯飞语音API初始化完成
⚠️ 客户端连接超时 - 自动重连中
❌ DTU平台连接失败 - 检查网络连接
```

### 3. 性能优化

#### 系统性能监控：
```bash
# 查看系统资源使用
check_websocket_dtu_status.bat

# 监控项目：
# - Node.js内存使用
# - CPU占用率
# - 网络连接状态
# - 响应时间
```

#### 优化建议：
- **内存优化**: 定期重启服务释放内存
- **网络优化**: 使用有线网络连接
- **硬件优化**: 确保设备供电稳定
- **环境优化**: 保持良好的4G信号强度

## 🛠️ 维护指南

### 1. 定期维护

#### 每日检查：
- ✅ 系统运行状态
- ✅ 网络连接稳定性
- ✅ 设备响应正常
- ✅ 日志无异常错误

#### 每周维护：
- 🔄 重启系统服务
- 🧹 清理临时文件
- 📊 检查性能指标
- 🔍 更新系统状态

#### 每月维护：
- 📦 更新依赖包
- 🔧 检查硬件连接
- 📋 备份配置文件
- 📈 分析使用统计

### 2. 备份与恢复

#### 配置备份：
```bash
# 备份重要配置文件
copy code\voice-backend\config.js config_backup.js
copy code\01.0\air780e_websocket_dtu.lua air780e_backup.lua
```

#### 系统恢复：
```bash
# 恢复配置文件
copy config_backup.js code\voice-backend\config.js

# 重新安装依赖
cd code\voice-backend && npm install
cd ..\voice-frontend && npm install

# 重启系统
start_websocket_dtu_system.bat
```

### 3. 升级指南

#### 系统升级步骤：
1. **备份当前配置**
2. **停止所有服务**
3. **更新代码文件**
4. **更新依赖包**
5. **恢复配置文件**
6. **测试系统功能**
7. **正式投入使用**

#### 升级注意事项：
- 📋 记录当前版本信息
- 💾 完整备份配置和数据
- 🧪 在测试环境先验证
- 📞 准备技术支持联系方式

## 📞 技术支持

### 联系方式
- **技术文档**: `docs/development/` 目录
- **问题反馈**: GitHub Issues
- **开发讨论**: 项目Wiki页面

### 相关资源
- [WebSocket+DTU系统启动脚本文档](development/WebSocket_DTU_System_Startup_Scripts.md)
- [CH32V307固件开发指南](../code/01.0/README.md)
- [银尔达DTU平台文档](http://dtu.yinled.com)
- [讯飞语音API文档](https://www.xfyun.cn/services/voicedictation)

---

**💡 提示**: 本指南涵盖了WebSocket+DTU系统的完整使用流程，如遇到未涵盖的问题，请参考技术文档或联系技术支持。
