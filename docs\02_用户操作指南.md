# WebSocket+DTU智能语音识别盲文打印系统 - 用户操作指南

## 📋 目录

1. [系统概述](#系统概述)
2. [快速启动](#快速启动)
3. [基础功能操作](#基础功能操作)
4. [高级功能操作](#高级功能操作)
5. [系统监控与管理](#系统监控与管理)
6. [错误处理与故障排除](#错误处理与故障排除)
7. [系统配置与维护](#系统配置与维护)

## 🎯 系统概述

### 系统架构
WebSocket+DTU智能语音识别盲文打印系统采用混合通信架构：
```
前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
```

### 核心组件
- **前端界面**: Web浏览器访问 http://localhost:4000
- **WebSocket后端**: Node.js服务器 (端口8081)
- **DTU平台**: 银尔达DTU平台 (dtu.yinled.com:8888)
- **4G设备**: Air780e模块 (IMEI: 869080075169294)
- **控制器**: CH32V307微控制器
- **语音识别**: 讯飞语音API
- **🆕 错误处理系统**: 智能错误检测和恢复
- **🆕 性能监控系统**: 实时性能指标监控

## 🚀 快速启动

### 1. 系统启动

#### 方式一：完整启动（推荐）
```bash
# 双击运行主启动脚本
start_websocket_dtu_system.bat
```

**启动流程**:
1. ✅ 检查Node.js环境
2. ✅ 验证项目文件完整性
3. ✅ 自动安装依赖包
4. ✅ 启动WebSocket后端服务器
5. ✅ 启动前端Web界面
6. ✅ 🆕 初始化错误处理系统
7. ✅ 🆕 启动性能监控服务
8. ✅ 显示系统状态和访问地址

#### 方式二：快速启动（开发调试）
```bash
# 双击运行快速启动脚本
quick_start_websocket_dtu.bat
```

### 2. 系统访问

启动成功后，系统会显示以下访问信息：

```
📊 服务状态:
  • WebSocket+DTU后端: http://localhost:8081
  • 前端Web界面:      http://localhost:4000
  • 银尔达DTU平台:    dtu.yinled.com:8888
  • 🆕 性能监控面板:   http://localhost:4000/monitor
  • 🆕 错误处理控制台: http://localhost:4000/error-console

🌐 访问地址:
  • 主界面:          http://localhost:4000
  • WebSocket测试:   ws://localhost:8081/voice-ws
  • DTU控制台:       http://localhost:4000/dtu-console
```

### 3. 系统状态检查

```bash
# 运行状态检查脚本
check_websocket_dtu_status.bat
```

检查项目包括：
- Node.js环境状态
- 端口占用情况
- 进程运行状态
- 项目文件完整性
- 依赖包安装状态
- 网络连接测试
- 服务响应测试
- 🆕 错误处理系统状态
- 🆕 性能监控系统状态

## 🎮 基础功能操作

### 1. 语音识别打印

#### 步骤流程：
1. **打开Web界面**: 访问 http://localhost:4000
2. **检查连接状态**: 确认WebSocket和DTU连接正常
3. **开始语音录制**: 点击"开始录音"按钮
4. **语音输入**: 清晰说出要打印的内容
5. **停止录制**: 点击"停止录音"按钮
6. **查看识别结果**: 系统显示识别的文字内容
7. **确认打印**: 点击"发送到打印机"按钮
8. **监控进度**: 实时查看打印状态

#### 语音识别技巧：
- 🎤 **环境要求**: 安静环境，减少背景噪音
- 🗣️ **发音要求**: 普通话标准，语速适中
- ⏱️ **录音时长**: 建议单次录音不超过30秒
- 📝 **内容建议**: 简短句子，避免过长段落

### 2. 手动文本打印

#### 操作步骤：
1. **文本输入**: 在Web界面文本框输入内容
2. **格式检查**: 确认文本格式正确
3. **发送打印**: 点击"直接打印"按钮
4. **状态监控**: 查看打印进度和状态

#### 文本格式要求：
- 📝 **字符支持**: 中文、英文、数字、常用标点
- 📏 **长度限制**: 单次打印建议不超过100字符
- 🔤 **编码格式**: UTF-8编码
- 📋 **特殊字符**: 避免使用特殊符号

### 3. 设备状态监控

#### 实时状态显示：
- **WebSocket连接**: 🟢 已连接 / 🔴 断开
- **DTU平台状态**: 🟢 在线 / 🔴 离线
- **设备连接**: 🟢 正常 / 🔴 异常
- **打印状态**: 🟢 就绪 / 🟡 打印中 / 🔴 错误
- **🆕 错误处理状态**: 🟢 正常 / 🟡 处理中 / 🔴 需要干预
- **🆕 性能监控状态**: 🟢 正常 / 🟡 警告 / 🔴 异常

#### 状态指示说明：
- **🟢 绿色**: 正常工作状态
- **🟡 黄色**: 工作中或警告状态
- **🔴 红色**: 错误或断开状态
- **⚪ 灰色**: 未连接或禁用状态

## 🔧 高级功能操作

### 1. 🆕 打印控制管理

#### 打印任务控制：
1. **暂停打印**: 
   - 点击"暂停"按钮或发送暂停指令
   - 系统会安全暂停当前打印任务
   - 可以随时恢复打印

2. **恢复打印**:
   - 点击"恢复"按钮
   - 系统从暂停点继续打印
   - 保持打印质量和精度

3. **取消打印**:
   - 点击"取消"按钮
   - 系统会安全停止当前任务
   - 清理打印队列中的相关任务

4. **紧急停止**:
   - 点击"紧急停止"按钮
   - 立即停止所有打印操作
   - 用于紧急情况处理

#### 打印队列管理：
- **查看队列**: 实时查看待打印任务列表
- **任务优先级**: 设置任务优先级（高/中/低）
- **批量操作**: 支持批量暂停、恢复、取消任务
- **队列清空**: 一键清空所有待处理任务

### 2. 🆕 性能监控功能

#### 实时性能监控：
1. **访问监控面板**: http://localhost:4000/monitor
2. **查看实时指标**:
   - 打印速度 (字符/秒)
   - 队列大小
   - 活跃任务数
   - 错误率
   - 响应时间

#### 性能数据查询：
```javascript
// 通过WebSocket查询性能数据
{
    "type": "performance_query",
    "request": "summary"  // summary, historical, alerts, statistics
}
```

#### 性能报告生成：
1. **生成报告**: 点击"生成性能报告"按钮
2. **选择时间范围**: 1小时、1天、1周、1月
3. **下载报告**: 系统生成PDF或Excel格式报告
4. **定期报告**: 可设置自动生成定期报告

### 3. 🆕 错误处理与恢复

#### 错误监控界面：
1. **访问错误控制台**: http://localhost:4000/error-console
2. **查看错误分类**:
   - 硬件错误 (1000-1999)
   - 通信错误 (2000-2999)
   - 应用错误 (3000-3999)
   - 系统错误 (4000-4999)

#### 自动错误恢复：
- **重试策略**: 系统自动重试失败操作
- **重置策略**: 自动重置异常组件
- **降级策略**: 启用备用功能模式
- **手动干预**: 需要用户确认的错误处理

#### 手动错误处理：
1. **查看错误详情**: 点击错误条目查看详细信息
2. **选择恢复策略**: 重试、重置、忽略、手动处理
3. **执行恢复操作**: 点击"执行恢复"按钮
4. **验证恢复结果**: 确认系统恢复正常

## 📊 系统监控与管理

### 1. 🆕 实时监控面板

#### 监控指标：
- **系统性能**: CPU、内存、网络使用率
- **打印性能**: 速度、精度、成功率
- **通信状态**: WebSocket、DTU、串口连接
- **错误统计**: 错误类型、频率、恢复率

#### 告警设置：
1. **性能告警**: 设置性能阈值
   - 打印速度下降30%告警
   - 错误率超过5%告警
   - 队列大小超过20个任务告警

2. **通信告警**: 设置连接监控
   - WebSocket断开告警
   - DTU离线告警
   - 串口通信异常告警

### 2. 🆕 配置管理

#### 动态配置更新：
1. **访问配置界面**: http://localhost:4000/config
2. **修改配置参数**: 在线修改系统配置
3. **热更新配置**: 无需重启即可生效
4. **配置验证**: 自动验证配置参数合法性

#### 配置备份与恢复：
- **自动备份**: 系统自动备份配置变更
- **手动备份**: 用户可手动创建配置备份
- **一键恢复**: 快速恢复到历史配置
- **配置导入导出**: 支持配置文件导入导出

### 3. 系统控制功能

#### 控制选项：
- **R - 重启系统**: 重新启动所有服务
- **S - 查看状态**: 显示详细系统状态
- **T - 测试连接**: 测试各组件连接
- **🆕 M - 监控面板**: 打开性能监控面板
- **🆕 E - 错误控制台**: 打开错误处理控制台
- **H - 显示帮助**: 查看帮助信息
- **Q - 退出系统**: 安全关闭系统

#### 使用方法：
```bash
# 在主启动脚本窗口中输入对应字母
请选择操作 (R/S/T/M/E/H/Q): M
```

## 🔍 错误处理与故障排除

### 1. 🆕 智能错误诊断

#### 自动错误检测：
系统会自动检测以下错误类型：
- **硬件错误**: 步进电机故障、传感器异常、电源问题
- **通信错误**: DTU连接丢失、WebSocket断开、串口通信失败
- **应用错误**: 无效打印数据、队列溢出、任务超时
- **系统错误**: 内存不足、文件系统错误、权限问题

#### 错误恢复流程：
1. **错误检测**: 系统自动检测异常
2. **错误分类**: 根据错误代码自动分类
3. **恢复策略选择**: 智能选择最佳恢复策略
4. **自动恢复**: 执行自动恢复操作
5. **结果验证**: 验证恢复效果
6. **用户通知**: 通知用户处理结果

### 2. 常见问题诊断

#### 问题：WebSocket连接失败
**症状**: 前端显示"WebSocket连接断开"
**🆕 智能诊断**: 系统会自动检测并尝试重连
**解决方案**:
```bash
# 1. 查看错误控制台
# 访问 http://localhost:4000/error-console

# 2. 自动恢复
# 系统会自动尝试重连，无需手动干预

# 3. 手动重启（如果自动恢复失败）
# 在主启动脚本中按 R 重启
```

#### 问题：DTU平台连接异常
**症状**: DTU状态显示离线
**🆕 智能诊断**: 系统会自动检测网络状态和设备信息
**解决方案**:
```bash
# 1. 查看性能监控面板
# 访问 http://localhost:4000/monitor

# 2. 检查网络连接
ping dtu.yinled.com

# 3. 验证设备信息
# 系统会自动验证IMEI和ICCID
```

#### 问题：🆕 打印性能下降
**症状**: 打印速度明显降低
**智能诊断**: 性能监控系统会自动检测并告警
**解决方案**:
1. **查看性能报告**: 分析性能趋势
2. **检查系统资源**: 确认CPU和内存使用情况
3. **优化打印参数**: 调整打印速度和精度设置
4. **清理打印队列**: 移除不必要的待处理任务

### 3. 🆕 高级故障排除

#### 错误日志分析：
1. **访问日志界面**: http://localhost:4000/logs
2. **筛选错误类型**: 按错误级别和类型筛选
3. **导出日志**: 下载详细日志文件
4. **日志分析**: 使用内置分析工具

#### 系统诊断工具：
- **连接测试**: 测试所有组件连接状态
- **性能测试**: 运行系统性能基准测试
- **压力测试**: 模拟高负载情况
- **恢复测试**: 验证错误恢复机制

## ⚙️ 系统配置与维护

### 1. 🆕 高级配置选项

#### 错误处理配置：
```javascript
// 错误处理配置
errorHandling: {
    enabled: true,
    autoRecovery: true,
    maxRetryAttempts: 3,
    retryInterval: 5000,
    alertThresholds: {
        errorRate: 5,        // 错误率超过5%告警
        responseTime: 5000   // 响应时间超过5秒告警
    }
}
```

#### 性能监控配置：
```javascript
// 性能监控配置
performanceMonitor: {
    enabled: true,
    sampleInterval: 1000,    // 采样间隔1秒
    dataRetentionDays: 30,   // 数据保留30天
    reportInterval: 300000,  // 报告间隔5分钟
    alertThresholds: {
        speedDropPercent: 30,    // 速度下降30%告警
        queueSizeLimit: 20,      // 队列超过20个任务告警
        errorRatePercent: 5      // 错误率超过5%告警
    }
}
```

### 2. 定期维护

#### 每日检查：
- ✅ 系统运行状态
- ✅ 网络连接稳定性
- ✅ 设备响应正常
- ✅ 日志无异常错误
- ✅ 🆕 性能指标正常
- ✅ 🆕 错误恢复率正常

#### 每周维护：
- 🔄 重启系统服务
- 🧹 清理临时文件
- 📊 检查性能指标
- 🔍 更新系统状态
- 📋 🆕 生成性能报告
- 🛡️ 🆕 检查错误处理日志

#### 每月维护：
- 📦 更新依赖包
- 🔧 检查硬件连接
- 📋 备份配置文件
- 📈 分析使用统计
- 🔄 🆕 优化系统配置
- 📊 🆕 生成月度性能报告

### 3. 🆕 系统优化建议

#### 性能优化：
1. **打印参数优化**: 根据性能报告调整打印参数
2. **队列管理优化**: 合理设置队列大小和优先级
3. **网络优化**: 使用有线网络，确保网络稳定
4. **硬件优化**: 定期检查和维护硬件设备

#### 稳定性优化：
1. **错误预防**: 根据错误统计预防常见错误
2. **监控优化**: 调整监控阈值，减少误报
3. **恢复策略优化**: 根据恢复成功率优化策略
4. **备份策略**: 定期备份重要配置和数据

---

## 📞 技术支持

### 联系方式
- **技术文档**: `docs/` 目录完整文档
- **问题反馈**: GitHub Issues
- **开发讨论**: 项目Wiki页面
- **🆕 在线帮助**: http://localhost:4000/help

### 相关资源
- [系统概述文档](01_系统概述.md)
- [API接口文档](03_API接口文档.md)
- [错误处理指南](04_错误处理指南.md)
- [部署维护文档](05_部署维护文档.md)
- [开发者文档](06_开发者文档.md)

---

**💡 提示**: 本指南涵盖了WebSocket+DTU系统的完整操作流程，包括最新的错误处理和性能监控功能。如遇到未涵盖的问题，请参考相关技术文档或联系技术支持。
