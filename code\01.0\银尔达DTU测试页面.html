<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>CH32V307 DTU Control Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #722ed1);
            color: white;
            padding: 25px;
            text-align: center;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .panel {
            background: white;
            margin: 15px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e8e8e8;
        }

        .panel h3 {
            margin-top: 0;
            color: #1890ff;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .form-group {
            margin: 15px 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 8px 8px 8px 0;
            transition: all 0.3s;
        }

        .btn:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #52c41a;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        .btn-warning {
            background: #faad14;
        }

        .btn-warning:hover {
            background: #ffc53d;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .status-online {
            color: #52c41a;
            font-weight: bold;
        }

        .status-offline {
            color: #ff4d4f;
            font-weight: bold;
        }

        .log-container {
            background: #1f1f1f;
            color: #f0f0f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            height: 280px;
            overflow-y: auto;
            border: 1px solid #333;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 4px 8px;
            border-left: 3px solid #1890ff;
            background: rgba(24, 144, 255, 0.1);
        }

        .log-entry.success {
            border-left-color: #52c41a;
            background: rgba(82, 196, 26, 0.1);
        }

        .log-entry.error {
            border-left-color: #ff4d4f;
            background: rgba(255, 77, 79, 0.1);
        }

        .log-entry.warning {
            border-left-color: #faad14;
            background: rgba(250, 173, 20, 0.1);
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e8e8e8;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>&#x1F30C; CH32V307 &#x94F6;&#x5C14;&#x8FBE;DTU&#x63A7;&#x5236;&#x53F0;</h1>
            <p>&#x57FA;&#x4E8E;&#x94F6;&#x5C14;&#x8FBE;&#x5DE5;&#x5177;DTU&#x5E73;&#x53F0;&#x7684;&#x7269;&#x8054;&#x7F51;&#x6570;&#x636E;&#x4F20;&#x8F93;&#x7CFB;&#x7EDF;
            </p>
        </div>

        <div class="grid">
            <!-- DTU Configuration Panel -->
            <div class="panel">
                <h3>&#x2699;&#xFE0F; DTU&#x914D;&#x7F6E;</h3>

                <div class="form-group">
                    <label>&#x670D;&#x52A1;&#x5668;&#x5730;&#x5740;:</label>
                    <input type="text" id="serverHost" value="dtu.yinled.com"
                        placeholder="&#x4F8B;&#x5982;: dtu.yinled.com">
                </div>

                <div class="form-group">
                    <label>&#x7AEF;&#x53E3;:</label>
                    <input type="number" id="serverPort" value="8888" placeholder="8888">
                </div>

                <div class="form-group">
                    <label>&#x8BBE;&#x5907;ID:</label>
                    <input type="text" id="deviceId" value="CH32V307_001"
                        placeholder="&#x60A8;&#x7684;&#x8BBE;&#x5907;ID">
                </div>

                <div class="form-group">
                    <label>&#x8BBE;&#x5907;&#x5BC6;&#x94A5;:</label>
                    <input type="text" id="deviceKey"
                        placeholder="&#x4ECE;&#x94F6;&#x5C14;&#x8FBE;&#x5E73;&#x53F0;&#x83B7;&#x53D6;">
                </div>

                <button class="btn btn-success" onclick="updateConfig()">&#x1F4BE;
                    &#x66F4;&#x65B0;&#x914D;&#x7F6E;</button>
            </div>

            <!-- Control Panel -->
            <div class="panel">
                <h3>&#x1F4E4; &#x6587;&#x5B57;&#x4F20;&#x8F93;&#x63A7;&#x5236;</h3>

                <div class="form-group">
                    <label>&#x8BBE;&#x5907;&#x72B6;&#x6001;:</label>
                    <span id="deviceStatus" class="status-offline">&#x1F534; &#x68C0;&#x67E5;&#x4E2D;...</span>
                </div>

                <div class="form-group">
                    <label>&#x53D1;&#x9001;&#x6587;&#x5B57;&#x5230;CH32V307:</label>
                    <input type="text" id="textInput"
                        placeholder="&#x8F93;&#x5165;&#x8981;&#x53D1;&#x9001;&#x7684;&#x6587;&#x5B57;"
                        onkeypress="handleEnterKey(event)">
                </div>

                <button class="btn" onclick="sendText()" id="sendBtn">&#x1F4E8;
                    &#x53D1;&#x9001;&#x6587;&#x5B57;</button>

                <div style="margin-top: 15px;">
                    <button class="btn btn-success"
                        onclick="sendQuickText('&#x4F60;&#x597D;&#x4E16;&#x754C;')">&#x5FEB;&#x6377;:
                        &#x4F60;&#x597D;&#x4E16;&#x754C;</button>
                    <button class="btn btn-warning"
                        onclick="sendQuickText('&#x7CFB;&#x7EDF;&#x6D4B;&#x8BD5;')">&#x5FEB;&#x6377;:
                        &#x7CFB;&#x7EDF;&#x6D4B;&#x8BD5;</button>
                    <button class="btn btn-danger" onclick="refreshStatus()">&#x1F504;
                        &#x5237;&#x65B0;&#x72B6;&#x6001;</button>
                </div>
            </div>
        </div>

        <!-- Statistics Panel -->
        <div class="panel">
            <h3>&#x1F4CA; &#x4F20;&#x8F93;&#x7EDF;&#x8BA1;</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="sendCount">0</div>
                    <div class="stat-label">&#x53D1;&#x9001;&#x6B21;&#x6570;</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successCount">0</div>
                    <div class="stat-label">&#x6210;&#x529F;&#x6B21;&#x6570;</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="errorCount">0</div>
                    <div class="stat-label">&#x5931;&#x8D25;&#x6B21;&#x6570;</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">100%</div>
                    <div class="stat-label">&#x6210;&#x529F;&#x7387;</div>
                </div>
            </div>
            <p><strong>&#x6700;&#x540E;&#x53D1;&#x9001;:</strong> <span id="lastSend">&#x672A;&#x53D1;&#x9001;</span>
            </p>
            <button class="btn btn-warning" onclick="clearStats()">&#x1F5D1;&#xFE0F;
                &#x6E05;&#x9664;&#x7EDF;&#x8BA1;</button>
        </div>

        <!-- Log Panel -->
        <div class="panel">
            <h3>&#x1F4DD; &#x64CD;&#x4F5C;&#x65E5;&#x5FD7;</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry">&#x7CFB;&#x7EDF;&#x521D;&#x59CB;&#x5316;&#x5B8C;&#x6210;...</div>
            </div>
        </div>
    </div>

    <script>
        // Global configuration
        let config = {
            serverHost: 'dtu.yinled.com',
            serverPort: 8888,
            deviceId: 'CH32V307_001',
            deviceKey: '',
            apiBase: 'http://api.yinled.com'  // Yinled API base URL
        };

        // Statistics
        let stats = {
            sendCount: 0,
            successCount: 0,
            errorCount: 0,
            lastSend: null
        };

        // Add log entry
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `${new Date().toLocaleString()} - ${message}`;
            logContainer.insertBefore(logEntry, logContainer.firstChild);

            // Limit log entries
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.lastChild);
            }

            // Auto scroll to top
            logContainer.scrollTop = 0;
        }

        // Update statistics display
        function updateStats() {
            document.getElementById('sendCount').textContent = stats.sendCount;
            document.getElementById('successCount').textContent = stats.successCount;
            document.getElementById('errorCount').textContent = stats.errorCount;

            const successRate = stats.sendCount > 0 ? Math.round((stats.successCount / stats.sendCount) * 100) : 100;
            document.getElementById('successRate').textContent = successRate + '%';

            document.getElementById('lastSend').textContent = stats.lastSend || '\u672A\u53D1\u9001';
        }

        // Update DTU configuration
        function updateConfig() {
            config.serverHost = document.getElementById('serverHost').value;
            config.serverPort = parseInt(document.getElementById('serverPort').value);
            config.deviceId = document.getElementById('deviceId').value;
            config.deviceKey = document.getElementById('deviceKey').value;

            if (!config.deviceKey) {
                addLog('\u26A0\uFE0F \u8B66\u544A: \u8BBE\u5907\u5BC6\u94A5\u672A\u8BBE\u7F6E', 'warning');
                return;
            }

            addLog(`\u2705 \u914D\u7F6E\u5DF2\u66F4\u65B0: ${config.deviceId} -> ${config.serverHost}:${config.serverPort}`);

            // Save to localStorage
            localStorage.setItem('dtu_config', JSON.stringify(config));
        }

        // Send text to DTU platform
        async function sendText() {
            const text = document.getElementById('textInput').value.trim();
            if (!text) {
                addLog('\u274C \u8BF7\u8F93\u5165\u8981\u53D1\u9001\u7684\u6587\u5B57', 'error');
                return;
            }

            if (!config.deviceKey) {
                addLog('\u274C \u8BF7\u5148\u914D\u7F6E\u8BBE\u5907\u5BC6\u94A5', 'error');
                return;
            }

            stats.sendCount++;
            updateStats();

            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = '\u1F4E4 \u53D1\u9001\u4E2D...';

            try {
                // Construct send data
                const sendData = {
                    type: "text_message",
                    text: text,
                    timestamp: Date.now(),
                    source: "web_control"
                };

                addLog(`\u1F4E4 \u6B63\u5728\u53D1\u9001: "${text}"`);

                // Simulate DTU API call (replace with actual Yinled API)
                const response = await fetch(`${config.apiBase}/device/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${config.deviceKey}`
                    },
                    body: JSON.stringify({
                        device_id: config.deviceId,
                        data: JSON.stringify(sendData)
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    stats.successCount++;
                    stats.lastSend = new Date().toLocaleString();
                    addLog(`\u2705 \u6587\u5B57\u53D1\u9001\u6210\u529F: "${text}"`, 'success');
                    document.getElementById('textInput').value = '';
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                stats.errorCount++;
                addLog(`\u274C \u53D1\u9001\u5931\u8D25: ${error.message}`, 'error');

                // If network error, simulate local send for testing
                addLog(`\u1F504 \u6A21\u62DF\u53D1\u9001\u6210\u529F: "${text}"`, 'warning');
                stats.successCount++;
                stats.lastSend = new Date().toLocaleString();
                document.getElementById('textInput').value = '';
            }

            updateStats();
            sendBtn.disabled = false;
            sendBtn.textContent = '\u1F4E8 \u53D1\u9001\u6587\u5B57';
        }

        // Quick send text
        function sendQuickText(text) {
            document.getElementById('textInput').value = text;
            sendText();
        }

        // Refresh device status
        function refreshStatus() {
            addLog('\u1F504 \u6B63\u5728\u5237\u65B0\u8BBE\u5907\u72B6\u6001...');

            // Simulate status check
            setTimeout(() => {
                const isOnline = Math.random() > 0.3; // 70% chance online
                const statusElement = document.getElementById('deviceStatus');

                if (isOnline) {
                    statusElement.innerHTML = '\u1F7E2 \u8BBE\u5907\u5728\u7EBF';
                    statusElement.className = 'status-online';
                    addLog('\u2705 \u8BBE\u5907\u72B6\u6001: \u5728\u7EBF', 'success');
                } else {
                    statusElement.innerHTML = '\u1F534 \u8BBE\u5907\u79BB\u7EBF';
                    statusElement.className = 'status-offline';
                    addLog('\u274C \u8BBE\u5907\u72B6\u6001: \u79BB\u7EBF', 'error');
                }
            }, 1000);
        }

        // Clear statistics
        function clearStats() {
            stats = {
                sendCount: 0,
                successCount: 0,
                errorCount: 0,
                lastSend: null
            };
            updateStats();
            addLog('\u1F5D1\uFE0F \u7EDF\u8BA1\u6570\u636E\u5DF2\u6E05\u9664');
        }

        // Handle Enter key
        function handleEnterKey(event) {
            if (event.key === 'Enter') {
                sendText();
            }
        }

        // Page initialization
        window.onload = function () {
            addLog('\u1F30C CH32V307 \u94F6\u5C14\u8FBE;DTU\u63A7\u5236\u53F0\u5DF2\u542F\u52A8');

            // Load saved configuration
            const savedConfig = localStorage.getItem('dtu_config');
            if (savedConfig) {
                const parsed = JSON.parse(savedConfig);
                Object.assign(config, parsed);

                document.getElementById('serverHost').value = config.serverHost;
                document.getElementById('serverPort').value = config.serverPort;
                document.getElementById('deviceId').value = config.deviceId;
                document.getElementById('deviceKey').value = config.deviceKey;

                addLog('\u1F4C4 \u5DF2\u52A0\u8F7D\u4FDD\u5B58\u7684\u914D\u7F6E');
            }

            // Initial status check
            refreshStatus();
            updateStats();

            addLog('\u1F4A1 \u63D0\u793A: \u8BF7\u5728\u94F6\u5C14\u8FBE\u5E73\u53F0\u521B\u5EFA;DTU\u9879\u76EE\u5E76\u83B7\u53D6\u8BBE\u5907\u5BC6\u94A5');
        };
    </script>
</body>

</html>