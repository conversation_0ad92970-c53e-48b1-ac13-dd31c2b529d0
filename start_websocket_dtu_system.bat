@echo off
chcp 65001 >nul
title WebSocket+DTU智能语音识别盲文打印系统启动器

echo.
echo ========================================
echo   WebSocket+DTU智能语音识别盲文打印系统
echo   版本: 2.0.0 WebSocket+DTU混合架构
echo   启动时间: %date% %time%
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查Node.js环境
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到Node.js环境
    echo    请先安装Node.js: https://nodejs.org/
    echo    建议版本: Node.js 16.x 或更高版本
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js环境检查通过: %NODE_VERSION%

:: 检查项目目录结构
echo.
echo [2/6] 检查项目目录结构...
if not exist "code\voice-backend\server.js" (
    echo ❌ 错误: 找不到WebSocket后端服务器文件
    echo    请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "code\voice-frontend\server.js" (
    echo ❌ 错误: 找不到前端服务器文件
    echo    请检查voice-frontend目录是否完整
    pause
    exit /b 1
)

echo ✅ 项目目录结构检查通过

:: 检查依赖包
echo.
echo [3/6] 检查依赖包安装状态...
cd code\voice-backend
if not exist "node_modules" (
    echo 📦 正在安装WebSocket后端依赖包...
    call npm install
    if errorlevel 1 (
        echo ❌ 后端依赖包安装失败
        pause
        exit /b 1
    )
)
echo ✅ WebSocket后端依赖包检查完成

cd ..\voice-frontend
if not exist "node_modules" (
    echo 📦 正在安装前端依赖包...
    call npm install
    if errorlevel 1 (
        echo ❌ 前端依赖包安装失败
        pause
        exit /b 1
    )
)
echo ✅ 前端依赖包检查完成

cd ..\..

:: 启动WebSocket后端服务器
echo.
echo [4/6] 启动WebSocket+DTU后端服务器...
echo 🚀 正在启动WebSocket服务器 (端口: 8081)
echo 🔗 正在初始化银尔达DTU连接...
start "WebSocket+DTU后端服务器" cmd /k "cd code\voice-backend && echo WebSocket+DTU后端服务器启动中... && node server.js"

:: 等待后端服务器启动
echo ⏳ 等待后端服务器启动 (5秒)...
timeout /t 5 /nobreak >nul

:: 测试WebSocket连接
echo 🔍 测试WebSocket连接...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ WebSocket服务器响应正常' } catch { Write-Host '⚠️  WebSocket服务器可能仍在启动中' }"

:: 启动前端Web界面
echo.
echo [5/6] 启动前端Web界面...
echo 🌐 正在启动前端服务器 (端口: 4000)
start "前端Web界面" cmd /k "cd code\voice-frontend && echo 前端Web界面启动中... && node server.js"

:: 等待前端服务器启动
echo ⏳ 等待前端服务器启动 (3秒)...
timeout /t 3 /nobreak >nul

:: 显示系统状态和访问信息
echo.
echo [6/6] 系统启动完成！
echo.
echo ========================================
echo           🎉 系统启动成功！
echo ========================================
echo.
echo 📊 服务状态:
echo   • WebSocket+DTU后端: http://localhost:8081
echo   • 前端Web界面:      http://localhost:4000
echo   • 银尔达DTU平台:    dtu.yinled.com:8888
echo.
echo 🔧 设备信息:
echo   • DTU设备型号:      银尔达 M100P
echo   • IMEI:            869080075169294
echo   • ICCID:           898604021024C0050919
echo   • 数据套餐:         4G 30M/月
echo.
echo 🌐 访问地址:
echo   • 主界面:          http://localhost:4000
echo   • WebSocket测试:   ws://localhost:8081/voice-ws
echo   • DTU控制台:       http://localhost:4000/dtu-console
echo.
echo 📱 使用说明:
echo   1. 打开浏览器访问: http://localhost:4000
echo   2. 系统会自动连接WebSocket和DTU服务
echo   3. 可以进行语音识别和盲文打印操作
echo   4. 支持WebSocket实时通信和DTU数据传输
echo.
echo ⚠️  注意事项:
echo   • 确保CH32V307设备已连接并烧录最新固件
echo   • 确保Air780e模块已上传WebSocket+DTU脚本
echo   • 如遇问题请检查防火墙和网络连接
echo.

:: 询问是否自动打开浏览器
set /p OPEN_BROWSER="是否自动打开浏览器? (Y/N): "
if /i "%OPEN_BROWSER%"=="Y" (
    echo 🌐 正在打开浏览器...
    start http://localhost:4000
)

echo.
echo ========================================
echo 系统控制选项:
echo   R - 重启系统
echo   S - 查看系统状态  
echo   T - 测试连接
echo   H - 显示帮助
echo   Q - 退出系统
echo ========================================

:MENU
set /p CHOICE="请选择操作 (R/S/T/H/Q): "

if /i "%CHOICE%"=="R" goto RESTART
if /i "%CHOICE%"=="S" goto STATUS
if /i "%CHOICE%"=="T" goto TEST
if /i "%CHOICE%"=="H" goto HELP
if /i "%CHOICE%"=="Q" goto QUIT

echo 无效选择，请重新输入
goto MENU

:RESTART
echo.
echo 🔄 正在重启系统...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul
echo 系统已停止，正在重新启动...
goto START

:STATUS
echo.
echo 📊 系统状态检查:
echo.
netstat -an | findstr ":8081" >nul && echo ✅ WebSocket后端服务器 (8081) - 运行中 || echo ❌ WebSocket后端服务器 (8081) - 未运行
netstat -an | findstr ":4000" >nul && echo ✅ 前端Web服务器 (4000) - 运行中 || echo ❌ 前端Web服务器 (4000) - 未运行
echo.
echo 🔗 网络连接测试:
ping -n 1 dtu.yinled.com >nul && echo ✅ 银尔达DTU平台连接 - 正常 || echo ❌ 银尔达DTU平台连接 - 异常
echo.
goto MENU

:TEST
echo.
echo 🧪 连接测试:
echo.
echo 测试WebSocket连接...
powershell -Command "try { $ws = New-Object System.Net.WebSockets.ClientWebSocket; Write-Host '✅ WebSocket连接测试通过' } catch { Write-Host '❌ WebSocket连接测试失败' }"
echo.
echo 测试HTTP服务...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 5 | Out-Null; Write-Host '✅ 前端HTTP服务测试通过' } catch { Write-Host '❌ 前端HTTP服务测试失败' }"
echo.
goto MENU

:HELP
echo.
echo 📖 WebSocket+DTU系统帮助:
echo.
echo 🏗️  系统架构:
echo   前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
echo.
echo 🔧 故障排除:
echo   • 端口被占用: 检查8081和4000端口是否被其他程序占用
echo   • WebSocket连接失败: 检查防火墙设置和网络连接
echo   • DTU连接异常: 检查银尔达DTU平台状态和设备在线情况
echo   • 设备无响应: 检查CH32V307和Air780e硬件连接
echo.
echo 📞 技术支持:
echo   • 项目文档: docs/development/
echo   • 配置文件: code/voice-backend/config.js
echo   • 日志文件: 查看各服务器窗口输出
echo.
goto MENU

:QUIT
echo.
echo 🛑 正在停止系统...
taskkill /f /im node.exe >nul 2>&1
echo ✅ 系统已停止
echo 感谢使用WebSocket+DTU智能语音识别盲文打印系统！
pause
exit /b 0
