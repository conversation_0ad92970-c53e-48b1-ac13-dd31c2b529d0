@echo off
chcp 65001 >nul
title WebSocket+DTU系统停止器

echo.
echo ========================================
echo   WebSocket+DTU系统停止器
echo   版本: 2.0.0
echo ========================================
echo.

:: 检查是否有Node.js进程运行
tasklist | findstr "node.exe" >nul
if errorlevel 1 (
    echo ℹ️  未发现运行中的Node.js进程
    echo ✅ 系统已经停止
    goto END
)

echo 🔍 发现运行中的Node.js进程:
tasklist | findstr "node.exe"
echo.

:: 询问确认
set /p CONFIRM="确定要停止所有WebSocket+DTU系统进程吗? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo ❌ 操作已取消
    goto END
)

echo.
echo 🛑 正在停止WebSocket+DTU系统...

:: 优雅停止 - 先尝试正常关闭
echo [1/3] 尝试优雅停止Node.js进程...
taskkill /im node.exe >nul 2>&1

:: 等待2秒
timeout /t 2 /nobreak >nul

:: 检查是否还有进程
tasklist | findstr "node.exe" >nul
if not errorlevel 1 (
    echo [2/3] 强制停止剩余进程...
    taskkill /f /im node.exe >nul 2>&1
    timeout /t 1 /nobreak >nul
)

:: 最终检查
tasklist | findstr "node.exe" >nul
if errorlevel 1 (
    echo ✅ 所有Node.js进程已停止
) else (
    echo ⚠️  部分进程可能仍在运行，请手动检查
)

echo.
echo [3/3] 清理临时文件...

:: 清理可能的临时文件
if exist "*.tmp" del /q "*.tmp" >nul 2>&1
if exist "code\voice-backend\*.log" del /q "code\voice-backend\*.log" >nul 2>&1
if exist "code\voice-frontend\*.log" del /q "code\voice-frontend\*.log" >nul 2>&1

echo ✅ 临时文件清理完成

echo.
echo ========================================
echo 🎉 WebSocket+DTU系统已完全停止！
echo.
echo 📊 系统状态:
echo   • WebSocket后端服务器: 已停止
echo   • 前端Web服务器: 已停止
echo   • 所有Node.js进程: 已终止
echo.
echo 💡 重新启动系统请运行:
echo   • 完整启动: start_websocket_dtu_system.bat
echo   • 快速启动: quick_start_websocket_dtu.bat
echo ========================================

:END
echo.
pause
