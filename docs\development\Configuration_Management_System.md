# CH32V307智能语音识别盲文打印系统 - 配置管理系统文档

## 概述

配置管理系统为CH32V307智能语音识别盲文打印系统提供统一的配置管理功能，支持多环境配置、环境变量覆盖、配置验证和热重载等特性。

## 系统架构

### 配置文件结构
```
code/voice-backend/
├── config.js              # 主配置文件
├── config.dev.js          # 开发环境配置
├── config.prod.js         # 生产环境配置
├── config-manager.js      # 配置管理器
├── .env.example           # 环境变量模板
└── .env                   # 环境变量文件（需要创建）
```

### 配置层次结构
1. **基础配置** (`config.js`) - 包含所有默认配置
2. **环境特定配置** (`config.{env}.js`) - 覆盖特定环境的配置
3. **环境变量** (`.env`) - 运行时配置覆盖

## 配置文件详解

### 主配置文件 (config.js)

包含系统的完整配置结构：

```javascript
const config = {
    system: {
        name: 'CH32V307_WebSocket_DTU_System',
        version: '2.0.0',
        environment: NODE_ENV
    },
    server: {
        websocket: { /* WebSocket服务器配置 */ },
        http: { /* HTTP服务器配置 */ }
    },
    xunfei: { /* 讯飞语音识别配置 */ },
    dtu: { /* DTU平台配置 */ },
    air780e: { /* Air780e模块配置 */ },
    ch32v307: { /* CH32V307芯片配置 */ }
};
```

### 环境特定配置

#### 开发环境 (config.dev.js)
- 本地服务器配置 (127.0.0.1)
- 调试功能启用
- 较短的超时时间
- 模拟数据支持
- 热重载功能

#### 生产环境 (config.prod.js)
- 生产服务器配置 (0.0.0.0)
- 性能优化设置
- 安全配置
- 监控和日志配置
- 错误处理和自动恢复

## 配置管理器 (ConfigManager)

### 主要功能

1. **配置加载和合并**
   ```javascript
   const configManager = require('./config-manager');
   const config = configManager.loadConfig('development');
   ```

2. **配置值获取和设置**
   ```javascript
   const port = configManager.get('server.websocket.port', 8081);
   configManager.set('server.websocket.port', 8082);
   ```

3. **配置验证**
   ```javascript
   const validation = configManager.validateConfig();
   if (!validation.valid) {
       console.error('配置错误:', validation.errors);
   }
   ```

4. **配置文件监控**
   ```javascript
   configManager.enableFileWatching(['./config.js', './config.dev.js']);
   ```

5. **事件监听**
   ```javascript
   configManager.addListener((event, data) => {
       console.log('配置事件:', event, data);
   });
   ```

## 环境变量支持

### 环境变量命名规范

- WebSocket配置: `WS_*`
- 讯飞配置: `XUNFEI_*`
- DTU配置: `DTU_*`
- Air780e配置: `AIR780E_*`

### 常用环境变量

```bash
# 系统环境
NODE_ENV=development

# WebSocket服务器
WS_HOST=127.0.0.1
WS_PORT=8081

# 讯飞语音识别
XUNFEI_ENABLED=true
XUNFEI_APP_ID=your_app_id
XUNFEI_API_KEY=your_api_key
XUNFEI_API_SECRET=your_api_secret

# DTU平台
DTU_ENABLED=true
DTU_DEVICE_ID=869080075169294
DTU_DEVICE_KEY=898604021024C0050919
```

## 使用指南

### 1. 基本使用

```javascript
// 直接使用配置
const config = require('./config');
console.log(config.server.websocket.port);

// 使用配置管理器
const configManager = require('./config-manager');
configManager.loadConfig();
const port = configManager.get('server.websocket.port');
```

### 2. 环境切换

```bash
# 开发环境
NODE_ENV=development node server.js

# 生产环境
NODE_ENV=production node server.js

# 测试环境
NODE_ENV=test node server.js
```

### 3. 环境变量覆盖

创建 `.env` 文件：
```bash
cp .env.example .env
# 编辑 .env 文件设置具体值
```

### 4. 配置验证

```javascript
const config = require('./config');
const validation = config.validateConfig();

if (!validation.valid) {
    console.error('配置验证失败:', validation.errors);
    process.exit(1);
}
```

## 配置项说明

### 系统配置 (system)
- `name`: 系统名称
- `version`: 系统版本
- `environment`: 运行环境
- `startTime`: 启动时间

### 服务器配置 (server)
- `websocket`: WebSocket服务器配置
  - `host`: 监听地址
  - `port`: 监听端口
  - `maxConnections`: 最大连接数
  - `heartbeatInterval`: 心跳间隔
- `http`: HTTP服务器配置（可选）

### 讯飞语音识别 (xunfei)
- `enabled`: 是否启用
- `appId`: 应用ID
- `apiKey`: API密钥
- `apiSecret`: API密钥
- `recognition`: 识别参数配置

### DTU平台配置 (dtu)
- `enabled`: 是否启用
- `api`: API配置
- `device`: 设备配置
- `management`: 设备管理配置
- `forwarding`: 数据转发配置

### Air780e配置 (air780e)
- `communication`: 通信配置
- `network`: 网络配置
- `device`: 设备配置

### CH32V307配置 (ch32v307)
- `protocol`: 通信协议配置
- `commands`: 支持的指令类型
- `features`: 设备功能配置

## 最佳实践

### 1. 配置安全
- 敏感信息使用环境变量
- 不要将 `.env` 文件提交到版本控制
- 生产环境使用专门的配置管理服务

### 2. 配置验证
- 启动时进行配置验证
- 使用类型检查和范围验证
- 提供清晰的错误信息

### 3. 环境管理
- 为每个环境创建专门的配置文件
- 使用环境变量进行部署时配置
- 保持配置文件的向后兼容性

### 4. 监控和调试
- 启用配置文件监控（开发环境）
- 记录配置加载和变更日志
- 提供配置摘要和健康检查接口

## 故障排除

### 常见问题

1. **配置加载失败**
   - 检查配置文件语法
   - 验证文件路径
   - 查看错误日志

2. **环境变量不生效**
   - 确认 `.env` 文件位置
   - 检查变量名称拼写
   - 验证环境变量类型转换

3. **配置验证失败**
   - 检查必需配置项
   - 验证配置值类型和范围
   - 查看验证错误详情

### 调试技巧

```javascript
// 查看当前配置
console.log(JSON.stringify(config, null, 2));

// 查看配置摘要
const summary = configManager.getConfigSummary();
console.log('配置摘要:', summary);

// 启用详细日志
process.env.DEBUG = 'config:*';
```

## 版本历史

- **v2.0.0**: 重构配置管理系统，支持多环境和环境变量
- **v1.0.0**: 初始版本，基础配置文件
