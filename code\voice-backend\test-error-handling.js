/**
 * 错误处理系统测试脚本
 * 测试各种错误场景和恢复机制
 */

const WebSocket = require('ws');

class ErrorHandlingTester {
    constructor() {
        this.ws = null;
        this.testResults = [];
        this.serverUrl = 'ws://localhost:8081/voice-ws';
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(this.serverUrl);

            this.ws.on('open', () => {
                console.log('🔗 已连接到WebSocket服务器');
                resolve();
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket连接错误:', error.message);
                reject(error);
            });

            this.ws.on('message', (data) => {
                try {
                    // 确保数据是字符串格式
                    const messageText = data.toString();
                    const message = JSON.parse(messageText);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('❌ 消息解析错误:', error.message);
                    console.error('原始数据:', data);
                }
            });
        });
    }

    handleMessage(message) {
        console.log('📨 收到消息:', message.type);

        switch (message.type) {
            case 'print_error_detected':
                console.log('🚨 检测到打印错误:', message.error);
                break;
            case 'error_recovery_started':
                console.log('🔄 错误恢复开始:', message.recovery);
                break;
            case 'error_recovery_completed':
                console.log('✅ 错误恢复完成:', message.recovery);
                break;
            case 'manual_intervention_required':
                console.log('👤 需要手动干预:', message.intervention);
                break;
            case 'error_stats_response':
                console.log('📊 错误统计:', message.stats);
                break;
            case 'error_history_response':
                console.log('📋 错误历史:', message.history);
                break;
            default:
                console.log('📝 其他消息:', message);
        }
    }

    // 测试1: 模拟硬件错误
    async testHardwareError() {
        console.log('\n🧪 测试1: 模拟硬件错误');

        const errorMessage = {
            type: 'simulate_error',
            error: {
                type: 'HARDWARE',
                code: 1001, // STEPPER_MOTOR_FAULT
                message: '步进电机故障 - 测试模拟',
                context: {
                    taskId: 'test_task_001',
                    component: 'stepper_motor',
                    operation: 'print_line'
                }
            }
        };

        const messageText = JSON.stringify(errorMessage);
        console.log('发送消息:', messageText);
        this.ws.send(messageText);
        await this.wait(2000);
    }

    // 测试2: 模拟通信错误
    async testCommunicationError() {
        console.log('\n🧪 测试2: 模拟通信错误');

        const errorMessage = {
            type: 'simulate_error',
            error: {
                type: 'COMMUNICATION',
                code: 2001, // DTU_CONNECTION_LOST
                message: 'DTU连接丢失 - 测试模拟',
                context: {
                    taskId: 'test_task_002',
                    component: 'dtu',
                    operation: 'send_command'
                }
            }
        };

        const messageText2 = JSON.stringify(errorMessage);
        console.log('发送消息:', messageText2);
        this.ws.send(messageText2);
        await this.wait(2000);
    }

    // 测试3: 模拟应用错误
    async testApplicationError() {
        console.log('\n🧪 测试3: 模拟应用错误');

        const errorMessage = {
            type: 'simulate_error',
            error: {
                type: 'APPLICATION',
                code: 3001, // INVALID_PRINT_DATA
                message: '无效的打印数据 - 测试模拟',
                context: {
                    taskId: 'test_task_003',
                    component: 'print_processor',
                    operation: 'validate_data'
                }
            }
        };

        const messageText3 = JSON.stringify(errorMessage);
        console.log('发送消息:', messageText3);
        this.ws.send(messageText3);
        await this.wait(2000);
    }

    // 测试4: 获取错误统计
    async testErrorStats() {
        console.log('\n🧪 测试4: 获取错误统计');

        const statsRequest = {
            type: 'error_stats_request',
            timeRange: '1h'
        };

        const messageText4 = JSON.stringify(statsRequest);
        console.log('发送消息:', messageText4);
        this.ws.send(messageText4);
        await this.wait(1000);
    }

    // 测试5: 获取错误历史
    async testErrorHistory() {
        console.log('\n🧪 测试5: 获取错误历史');

        const historyRequest = {
            type: 'error_history_request',
            limit: 10,
            level: null
        };

        const messageText5 = JSON.stringify(historyRequest);
        console.log('发送消息:', messageText5);
        this.ws.send(messageText5);
        await this.wait(1000);
    }

    // 测试6: 手动重试错误
    async testManualRetry() {
        console.log('\n🧪 测试6: 手动重试错误');

        const retryRequest = {
            type: 'error_retry_request',
            errorId: 'test_error_001'
        };

        const messageText6 = JSON.stringify(retryRequest);
        console.log('发送消息:', messageText6);
        this.ws.send(messageText6);
        await this.wait(1000);
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runAllTests() {
        try {
            console.log('🚀 开始错误处理系统测试...\n');

            await this.connect();
            await this.wait(1000);

            await this.testHardwareError();
            await this.testCommunicationError();
            await this.testApplicationError();
            await this.testErrorStats();
            await this.testErrorHistory();
            await this.testManualRetry();

            console.log('\n✅ 所有测试完成!');

        } catch (error) {
            console.error('❌ 测试失败:', error.message);
        } finally {
            if (this.ws) {
                this.ws.close();
                console.log('🔌 WebSocket连接已关闭');
            }
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new ErrorHandlingTester();
    tester.runAllTests();
}

module.exports = ErrorHandlingTester;
