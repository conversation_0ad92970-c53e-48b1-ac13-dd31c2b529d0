# WebSocket+DTU系统API文档

## 📋 目录

1. [API概述](#API概述)
2. [WebSocket API](#WebSocket-API)
3. [DTU平台API](#DTU平台API)
4. [串口通信协议](#串口通信协议)
5. [错误处理](#错误处理)
6. [示例代码](#示例代码)

## 🎯 API概述

### 系统架构
```
前端WebSocket客户端 ↔ Node.js WebSocket服务器 ↔ 银尔达DTU API ↔ Air780e DTU ↔ CH32V307
```

### 通信层级
1. **前端 ↔ WebSocket服务器**: WebSocket协议
2. **WebSocket服务器 ↔ DTU平台**: HTTP/HTTPS + DTU API
3. **DTU平台 ↔ Air780e**: 4G网络 + DTU协议
4. **Air780e ↔ CH32V307**: 串口通信 + JSON协议

## 🌐 WebSocket API

### 连接信息
- **服务器地址**: `ws://localhost:8081/voice-ws`
- **协议版本**: WebSocket 13
- **心跳间隔**: 30秒
- **连接超时**: 60秒

### 1. 连接建立

#### 客户端连接
```javascript
const ws = new WebSocket('ws://localhost:8081/voice-ws');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
    // 发送认证信息
    ws.send(JSON.stringify({
        type: 'auth',
        token: 'client_token',
        client_id: 'web_client_001'
    }));
};
```

#### 服务器响应
```json
{
    "type": "auth_response",
    "status": "success",
    "client_id": "web_client_001",
    "session_id": "sess_1699123456789",
    "server_time": 1699123456789
}
```

### 2. 语音识别API

#### 音频数据上传
```javascript
// 发送音频数据
const audioMessage = {
    type: "audio_data",
    data: arrayBuffer,      // 音频数据 (ArrayBuffer)
    format: "pcm",          // 音频格式
    sampleRate: 16000,      // 采样率
    channels: 1,            // 声道数
    bitDepth: 16,           // 位深度
    timestamp: Date.now()   // 时间戳
};

ws.send(JSON.stringify(audioMessage));
```

#### 语音识别结果
```json
{
    "type": "voice_result",
    "recognized_text": "你好世界",
    "confidence": 0.95,
    "language": "zh-cn",
    "duration": 2.5,
    "dtu_status": "sent_to_device",
    "target_device": "braille_printer_001",
    "command_id": "cmd_1699123456789",
    "timestamp": 1699123456789
}
```

### 3. 设备控制API

#### 发送打印指令
```javascript
const printCommand = {
    type: "print_command",
    text: "你好世界",
    device_id: "braille_printer_001",
    print_options: {
        font_size: "normal",
        line_spacing: 2.5,
        char_spacing: 6.0
    },
    command_id: "cmd_" + Date.now()
};

ws.send(JSON.stringify(printCommand));
```

#### 设备状态更新
```json
{
    "type": "device_status",
    "device_id": "braille_printer_001",
    "status": {
        "connection": "online",
        "printer_status": "printing",
        "audio_status": "ready",
        "position": {
            "x": 120.5,
            "y": 85.0
        },
        "progress": 65
    },
    "timestamp": 1699123456789
}
```

### 4. 系统状态API

#### 获取系统状态
```javascript
const statusRequest = {
    type: "get_status",
    components: ["websocket", "dtu", "xunfei", "device"]
};

ws.send(JSON.stringify(statusRequest));
```

#### 系统状态响应
```json
{
    "type": "system_status",
    "status": {
        "websocket": {
            "status": "online",
            "connections": 3,
            "uptime": 3600
        },
        "dtu": {
            "status": "connected",
            "platform": "yinled",
            "device_count": 1,
            "last_heartbeat": 1699123456789
        },
        "xunfei": {
            "status": "active",
            "api_calls": 156,
            "success_rate": 0.98
        },
        "device": {
            "status": "online",
            "imei": "869080075169294",
            "signal_strength": -65,
            "battery_level": 85
        }
    },
    "timestamp": 1699123456789
}
```

## 📡 DTU平台API

### 平台信息
- **平台地址**: `dtu.yinled.com:8888`
- **协议**: HTTP/HTTPS
- **认证方式**: IMEI + ICCID
- **数据格式**: JSON

### 1. 设备认证

#### 认证请求
```http
POST /api/device/auth
Host: dtu.yinled.com:8888
Content-Type: application/json

{
    "imei": "869080075169294",
    "iccid": "898604021024C0050919",
    "device_type": "M100P",
    "firmware_version": "2.0.0"
}
```

#### 认证响应
```json
{
    "status": "success",
    "device_id": "braille_printer_001",
    "access_token": "dtu_token_1699123456789",
    "expires_in": 3600,
    "server_time": 1699123456789
}
```

### 2. 数据上行

#### 设备状态上报
```http
POST /api/device/status
Host: dtu.yinled.com:8888
Authorization: Bearer dtu_token_1699123456789
Content-Type: application/json

{
    "device_id": "braille_printer_001",
    "status": {
        "printer_status": "ready",
        "audio_status": "recording",
        "position": {"x": 0, "y": 0},
        "temperature": 25.6,
        "humidity": 45.2
    },
    "timestamp": 1699123456789
}
```

### 3. 数据下行

#### 接收控制指令
```http
GET /api/device/commands?device_id=braille_printer_001
Host: dtu.yinled.com:8888
Authorization: Bearer dtu_token_1699123456789
```

#### 指令响应
```json
{
    "commands": [
        {
            "command_id": "cmd_1699123456789",
            "type": "print_braille",
            "data": {
                "text": "你好世界",
                "confidence": 0.95,
                "options": {
                    "font_size": "normal",
                    "line_spacing": 2.5
                }
            },
            "timestamp": 1699123456789
        }
    ]
}
```

## 🔌 串口通信协议

### 通信参数
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无

### 1. 数据格式

#### 基本消息结构
```json
{
    "type": "message_type",
    "data": {},
    "timestamp": 1699123456789,
    "checksum": "crc32_value"
}
```

### 2. 控制指令

#### 打印指令
```json
{
    "type": "VOICE_PRINT",
    "data": {
        "text": "你好世界",
        "confidence": 0.95,
        "command_id": "cmd_1699123456789",
        "options": {
            "font_size": "normal",
            "line_spacing": 2.5,
            "char_spacing": 6.0
        }
    },
    "timestamp": 1699123456789
}
```

#### 系统控制指令
```json
{
    "type": "SYSTEM_CMD",
    "data": {
        "command": "calibrate",
        "axis": "X",
        "steps": 2000,
        "speed": 1000
    },
    "timestamp": 1699123456789
}
```

### 3. 状态上报

#### 设备状态
```json
{
    "type": "STATUS_UPDATE",
    "data": {
        "printer_status": "printing",
        "audio_status": "ready",
        "position": {
            "x": 120.5,
            "y": 85.0
        },
        "progress": 65,
        "error_code": 0
    },
    "timestamp": 1699123456789
}
```

#### 错误报告
```json
{
    "type": "ERROR_REPORT",
    "data": {
        "error_code": 1001,
        "error_message": "步进电机X轴异常",
        "error_level": "warning",
        "suggested_action": "检查X轴连接"
    },
    "timestamp": 1699123456789
}
```

## ❌ 错误处理

### 错误代码定义

#### WebSocket错误 (1000-1999)
- **1001**: 连接超时
- **1002**: 认证失败
- **1003**: 消息格式错误
- **1004**: 权限不足
- **1005**: 服务器内部错误

#### DTU平台错误 (2000-2999)
- **2001**: 设备未注册
- **2002**: 认证令牌过期
- **2003**: 网络连接失败
- **2004**: 数据格式错误
- **2005**: 设备离线

#### 设备错误 (3000-3999)
- **3001**: 串口通信失败
- **3002**: 步进电机异常
- **3003**: 音频采集失败
- **3004**: 打印机故障
- **3005**: 传感器异常

#### 语音识别错误 (4000-4999)
- **4001**: API调用失败
- **4002**: 音频格式不支持
- **4003**: 识别置信度过低
- **4004**: 网络超时
- **4005**: 配额不足

### 错误响应格式

```json
{
    "type": "error",
    "error": {
        "code": 1003,
        "message": "消息格式错误",
        "details": "JSON解析失败",
        "timestamp": 1699123456789,
        "request_id": "req_1699123456789"
    }
}
```

## 💻 示例代码

### 1. WebSocket客户端示例

```javascript
class WebSocketDTUClient {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.connected = false;
    }
    
    connect() {
        this.ws = new WebSocket(this.url);
        
        this.ws.onopen = (event) => {
            this.connected = true;
            console.log('WebSocket连接已建立');
            this.authenticate();
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.ws.onclose = (event) => {
            this.connected = false;
            console.log('WebSocket连接已关闭');
            // 自动重连
            setTimeout(() => this.connect(), 5000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }
    
    authenticate() {
        const authMessage = {
            type: 'auth',
            token: 'client_token',
            client_id: 'web_client_001'
        };
        this.send(authMessage);
    }
    
    send(message) {
        if (this.connected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    sendAudioData(audioBuffer) {
        const message = {
            type: 'audio_data',
            data: Array.from(new Uint8Array(audioBuffer)),
            format: 'pcm',
            sampleRate: 16000,
            timestamp: Date.now()
        };
        this.send(message);
    }
    
    sendPrintCommand(text) {
        const message = {
            type: 'print_command',
            text: text,
            device_id: 'braille_printer_001',
            command_id: 'cmd_' + Date.now()
        };
        this.send(message);
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'auth_response':
                console.log('认证成功:', message);
                break;
            case 'voice_result':
                console.log('语音识别结果:', message.recognized_text);
                break;
            case 'device_status':
                console.log('设备状态更新:', message.status);
                break;
            case 'error':
                console.error('错误:', message.error);
                break;
        }
    }
}

// 使用示例
const client = new WebSocketDTUClient('ws://localhost:8081/voice-ws');
client.connect();
```

### 2. DTU集成示例

```javascript
class DTUIntegration {
    constructor(config) {
        this.config = config;
        this.accessToken = null;
    }
    
    async authenticate() {
        const response = await fetch(`${this.config.endpoint}/api/device/auth`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                imei: this.config.imei,
                iccid: this.config.iccid,
                device_type: this.config.deviceType
            })
        });
        
        const result = await response.json();
        if (result.status === 'success') {
            this.accessToken = result.access_token;
            return true;
        }
        return false;
    }
    
    async sendCommand(deviceId, command) {
        const response = await fetch(`${this.config.endpoint}/api/device/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.accessToken}`
            },
            body: JSON.stringify({
                device_id: deviceId,
                command: command,
                timestamp: Date.now()
            })
        });
        
        return await response.json();
    }
    
    async getDeviceStatus(deviceId) {
        const response = await fetch(
            `${this.config.endpoint}/api/device/status?device_id=${deviceId}`,
            {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`
                }
            }
        );
        
        return await response.json();
    }
}
```

---

**📝 注意**: 本API文档涵盖了WebSocket+DTU系统的完整通信协议，开发时请严格按照协议格式进行数据交换。
