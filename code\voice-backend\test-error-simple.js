/**
 * 简化的错误处理系统测试脚本
 * 只测试错误统计和历史功能，避免触发DTU重连
 */

const WebSocket = require('ws');

class SimpleErrorTester {
    constructor() {
        this.ws = null;
        this.serverUrl = 'ws://localhost:8081/voice-ws';
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(this.serverUrl);

            this.ws.on('open', () => {
                console.log('🔗 已连接到WebSocket服务器');
                resolve();
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket连接错误:', error.message);
                reject(error);
            });

            this.ws.on('message', (data) => {
                try {
                    const messageText = data.toString();
                    const message = JSON.parse(messageText);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('❌ 消息解析错误:', error.message);
                }
            });
        });
    }

    handleMessage(message) {
        console.log('📨 收到消息:', message.type);

        switch (message.type) {
            case 'error_stats':
                console.log('📊 错误统计响应:');
                if (message.success && message.data) {
                    console.log('  - 总错误数:', message.data.totalErrors);
                    console.log('  - 错误率:', message.data.errorRate + '%');
                    console.log('  - 连续错误数:', message.data.consecutiveErrors);
                    console.log('  - 恢复成功率:', message.data.recoverySuccessRate + '%');
                } else {
                    console.log('  - 错误:', message.error || '未知错误');
                }
                break;
            case 'error_history':
                console.log('📋 错误历史响应:');
                if (message.success && message.data) {
                    console.log('  - 历史记录数:', message.data.length);
                    message.data.forEach((error, index) => {
                        console.log(`  ${index + 1}. [${error.level}] ${error.type}: ${error.message}`);
                    });
                } else {
                    console.log('  - 错误:', message.error || '未知错误');
                }
                break;
            case 'simulate_error_result':
                console.log('✅ 错误模拟结果:', message.success ? '成功' : '失败');
                if (message.success) {
                    console.log('  - 错误ID:', message.context.taskId);
                    console.log('  - 错误类型:', message.errorInfo.type);
                    console.log('  - 错误代码:', message.errorInfo.code);
                }
                break;
            default:
                // 忽略其他消息类型
                break;
        }
    }

    // 测试1: 模拟应用错误（不会触发DTU重连）
    async testApplicationError() {
        console.log('\n🧪 测试1: 模拟应用错误');

        const errorMessage = {
            type: 'simulate_error',
            error: {
                type: 'APPLICATION',
                code: 3001, // INVALID_PRINT_DATA
                message: '无效的打印数据 - 测试模拟',
                context: {
                    taskId: 'test_task_app_001',
                    component: 'print_processor',
                    operation: 'validate_data'
                }
            }
        };

        const messageText = JSON.stringify(errorMessage);
        this.ws.send(messageText);
        await this.wait(2000);
    }

    // 测试2: 获取错误统计
    async testErrorStats() {
        console.log('\n🧪 测试2: 获取错误统计');

        const statsRequest = {
            type: 'error_stats_request',
            timeRange: '1h'
        };

        const messageText = JSON.stringify(statsRequest);
        this.ws.send(messageText);
        await this.wait(1000);
    }

    // 测试3: 获取错误历史
    async testErrorHistory() {
        console.log('\n🧪 测试3: 获取错误历史');

        const historyRequest = {
            type: 'error_history_request',
            limit: 5,
            level: null
        };

        const messageText = JSON.stringify(historyRequest);
        this.ws.send(messageText);
        await this.wait(1000);
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runTests() {
        try {
            console.log('🚀 开始简化错误处理系统测试...\n');

            await this.connect();
            await this.wait(1000);

            await this.testApplicationError();
            await this.testErrorStats();
            await this.testErrorHistory();

            console.log('\n✅ 所有测试完成!');

        } catch (error) {
            console.error('❌ 测试失败:', error.message);
        } finally {
            if (this.ws) {
                this.ws.close();
                console.log('🔌 WebSocket连接已关闭');
            }
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new SimpleErrorTester();
    tester.runTests();
}

module.exports = SimpleErrorTester;
