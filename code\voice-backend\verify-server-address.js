/**
 * 服务器地址验证工具
 * 用于验证DTU设备可以正确连接到WebSocket服务器
 */

const net = require('net');
const os = require('os');

class ServerAddressVerifier {
    constructor() {
        this.serverPort = 8081;
        this.localIPs = this.getLocalIPAddresses();
    }

    /**
     * 获取本机所有IP地址
     */
    getLocalIPAddresses() {
        const interfaces = os.networkInterfaces();
        const addresses = [];

        for (const name of Object.keys(interfaces)) {
            for (const iface of interfaces[name]) {
                if (iface.family === 'IPv4' && !iface.internal) {
                    addresses.push({
                        name: name,
                        address: iface.address,
                        netmask: iface.netmask
                    });
                }
            }
        }

        return addresses;
    }

    /**
     * 测试端口是否可访问
     */
    testPortAccess(host, port) {
        return new Promise((resolve, reject) => {
            const socket = new net.Socket();
            const timeout = 5000; // 5秒超时

            socket.setTimeout(timeout);

            socket.on('connect', () => {
                socket.destroy();
                resolve(true);
            });

            socket.on('timeout', () => {
                socket.destroy();
                reject(new Error('连接超时'));
            });

            socket.on('error', (error) => {
                socket.destroy();
                reject(error);
            });

            socket.connect(port, host);
        });
    }

    /**
     * 验证WebSocket服务器状态
     */
    async verifyWebSocketServer() {
        console.log('🔍 验证WebSocket服务器状态...');
        console.log(`📡 检查端口: ${this.serverPort}`);
        console.log('');

        const results = [];

        for (const ip of this.localIPs) {
            console.log(`🌐 测试IP地址: ${ip.address} (${ip.name})`);

            try {
                await this.testPortAccess(ip.address, this.serverPort);
                console.log(`✅ ${ip.address}:${this.serverPort} - 可访问`);
                results.push({
                    ...ip,
                    accessible: true,
                    recommended: ip.address.startsWith('10.') || ip.address.startsWith('192.168.') || ip.address.startsWith('172.')
                });
            } catch (error) {
                console.log(`❌ ${ip.address}:${this.serverPort} - 不可访问 (${error.message})`);
                results.push({
                    ...ip,
                    accessible: false,
                    error: error.message
                });
            }
            console.log('');
        }

        return results;
    }

    /**
     * 生成DTU配置建议
     */
    generateDTUConfig(results) {
        console.log('📋 DTU平台配置建议:');
        console.log('=' * 50);

        const accessibleIPs = results.filter(r => r.accessible);

        if (accessibleIPs.length === 0) {
            console.log('❌ 没有找到可访问的IP地址');
            console.log('💡 请确保WebSocket服务器正在运行');
            console.log('   运行命令: cd code/voice-backend && node server.js');
            return;
        }

        // 推荐最佳IP地址
        const recommendedIP = accessibleIPs.find(ip => ip.recommended) || accessibleIPs[0];

        console.log('🎯 推荐配置 (用于银尔达DTU平台):');
        console.log('');
        console.log(`服务器地址: ${recommendedIP.address}`);
        console.log(`服务器端口: ${this.serverPort}`);
        console.log('协议类型: TCP');
        console.log('连接模式: 客户端');
        console.log('心跳间隔: 30');
        console.log('心跳数据: PING');
        console.log('注册包: {"type":"dtu_register","imei":"869080075169294"}');
        console.log('数据格式: JSON');
        console.log('');

        if (accessibleIPs.length > 1) {
            console.log('🔄 备选配置:');
            accessibleIPs.forEach((ip, index) => {
                if (ip.address !== recommendedIP.address) {
                    console.log(`   备选${index}: ${ip.address}:${this.serverPort}`);
                }
            });
            console.log('');
        }

        // 网络类型说明
        console.log('📡 网络类型说明:');
        if (recommendedIP.address.startsWith('10.')) {
            console.log('   类型: 企业内网 (Class A)');
            console.log('   适用: 企业内部DTU设备连接');
        } else if (recommendedIP.address.startsWith('192.168.')) {
            console.log('   类型: 家庭/小型办公网络 (Class C)');
            console.log('   适用: 家庭或小型办公室DTU设备');
        } else if (recommendedIP.address.startsWith('172.')) {
            console.log('   类型: 私有网络 (Class B)');
            console.log('   适用: 中型网络DTU设备连接');
        } else {
            console.log('   类型: 公网IP');
            console.log('   适用: 远程DTU设备连接');
        }
        console.log('');

        // 安全提醒
        console.log('🛡️ 安全提醒:');
        console.log('   1. 确保防火墙允许端口8081的入站连接');
        console.log('   2. 如果DTU设备在外网，建议使用VPN或专线连接');
        console.log('   3. 定期检查连接日志，监控异常访问');
        console.log('');

        return recommendedIP;
    }

    /**
     * 运行完整验证
     */
    async runVerification() {
        console.log('🚀 开始服务器地址验证');
        console.log('=' * 50);
        console.log('');

        // 显示本机IP信息
        console.log('💻 本机网络接口:');
        this.localIPs.forEach(ip => {
            console.log(`   ${ip.name}: ${ip.address}/${ip.netmask}`);
        });
        console.log('');

        // 验证服务器状态
        const results = await this.verifyWebSocketServer();

        // 生成配置建议
        const recommendedConfig = this.generateDTUConfig(results);

        return {
            results,
            recommendedConfig
        };
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const verifier = new ServerAddressVerifier();
    verifier.runVerification().then(result => {
        if (result.recommendedConfig) {
            console.log('✅ 验证完成，请使用推荐配置');
            process.exit(0);
        } else {
            console.log('❌ 验证失败，请检查服务器状态');
            process.exit(1);
        }
    }).catch(error => {
        console.error('❌ 验证过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = ServerAddressVerifier;
