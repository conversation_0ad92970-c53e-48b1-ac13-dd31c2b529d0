@echo off
chcp 65001 >nul
title WebSocket+DTU系统快速启动

echo.
echo ==========================================
echo    WebSocket+DTU系统快速启动器
echo    版本: 2.0.0 - 一键启动模式
echo ==========================================
echo.

:: 快速环境检查
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要Node.js环境，请先安装
    pause
    exit /b 1
)

echo ✅ 环境检查通过，正在启动系统...
echo.

:: 启动后端服务器
echo 🚀 启动WebSocket+DTU后端服务器...
start "WebSocket+DTU后端" cmd /k "cd code\voice-backend && node server.js"

:: 等待2秒
timeout /t 2 /nobreak >nul

:: 启动前端服务器
echo 🌐 启动前端Web界面...
start "前端Web界面" cmd /k "cd code\voice-frontend && node server.js"

:: 等待3秒
timeout /t 3 /nobreak >nul

:: 自动打开浏览器
echo 🌐 正在打开浏览器...
start http://localhost:4000

echo.
echo ========================================
echo 🎉 系统启动完成！
echo.
echo 📊 访问地址:
echo   • 主界面: http://localhost:4000
echo   • WebSocket: ws://localhost:8081/voice-ws
echo.
echo 💡 提示: 关闭此窗口将停止整个系统
echo ========================================
echo.

:: 保持窗口打开
echo 按任意键停止系统...
pause >nul

:: 停止所有Node.js进程
echo 🛑 正在停止系统...
taskkill /f /im node.exe >nul 2>&1
echo ✅ 系统已停止
