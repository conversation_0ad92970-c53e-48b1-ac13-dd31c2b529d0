// CH32V307智能语音识别盲文打印系统 - 开发环境配置
// 开发环境特定配置覆盖
// 版本: 2.0.0

module.exports = {
    // 系统信息
    system: {
        environment: 'development',
        debug: true,
        logLevel: 'debug'
    },

    // 服务器配置 - 开发环境
    server: {
        websocket: {
            host: '127.0.0.1',  // 本地开发
            port: 8081,
            maxConnections: 10,  // 开发环境限制连接数
            heartbeatInterval: 15000,  // 更频繁的心跳检测
            connectionTimeout: 30000
        },
        
        http: {
            enabled: true,  // 开发环境启用HTTP服务器
            port: 8080,
            staticPath: '../voice-frontend'
        }
    },

    // 讯飞语音识别 - 开发环境
    xunfei: {
        enabled: true,
        
        // 开发环境使用测试参数
        recognition: {
            timeout: 15000,  // 较短的超时时间
            confidenceThreshold: 0.6  // 较低的置信度阈值便于测试
        }
    },

    // DTU平台 - 开发环境
    dtu: {
        enabled: true,
        
        api: {
            timeout: 5000,  // 较短的超时时间
            retryAttempts: 2,  // 较少的重试次数
            retryDelay: 500
        },

        management: {
            heartbeatInterval: 15000,  // 更频繁的心跳
            offlineTimeout: 60000  // 1分钟离线超时
        },

        forwarding: {
            confidenceThreshold: 0.6  // 较低的置信度阈值
        }
    },

    // Air780e - 开发环境
    air780e: {
        device: {
            reconnectInterval: 3000,  // 更快的重连
            heartbeatInterval: 15000  // 更频繁的心跳
        }
    },

    // 开发环境特定功能
    development: {
        // 模拟数据
        mockData: {
            enabled: true,
            voiceRecognitionDelay: 1000,  // 模拟语音识别延迟
            dtuResponseDelay: 500  // 模拟DTU响应延迟
        },

        // 调试功能
        debug: {
            logWebSocketMessages: true,  // 记录WebSocket消息
            logDTUMessages: true,  // 记录DTU消息
            logVoiceRecognition: true,  // 记录语音识别过程
            saveDebugLogs: true  // 保存调试日志到文件
        },

        // 热重载
        hotReload: {
            enabled: true,
            watchFiles: ['*.js', '*.json'],
            excludeFiles: ['node_modules/**', 'logs/**']
        }
    }
};
