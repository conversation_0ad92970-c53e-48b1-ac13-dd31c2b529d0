@echo off
title WebSocket+DTU System Status Check

echo.
echo ========================================
echo   WebSocket+DTU System Status Check
echo   Version: 2.0.0
echo ========================================
echo.

:: Check Node.js environment
echo [1] Node.js Environment Check:
node --version >nul 2>&1
if errorlevel 1 (
    echo X Node.js not installed or not in PATH
) else (
    for /f "tokens=*" %%i in ('node --version') do echo + Node.js Version: %%i
)

echo.

:: Check port usage
echo [2] Port Usage Check:
netstat -an | findstr ":8081" >nul && echo + Port 8081 (WebSocket Backend) - In Use || echo - Port 8081 (WebSocket Backend) - Free
netstat -an | findstr ":4000" >nul && echo + Port 4000 (Frontend Web) - In Use || echo - Port 4000 (Frontend Web) - Free

echo.

:: Check process status
echo [3] Process Status Check:
tasklist | findstr "node.exe" >nul && echo + Node.js processes running || echo - No Node.js processes found

echo.

:: Check project files
echo [4] Project Files Check:
if exist "code\voice-backend\server.js" (echo + WebSocket backend file exists) else (echo - WebSocket backend file missing)
if exist "code\voice-frontend\server.js" (echo + Frontend server file exists) else (echo - Frontend server file missing)
if exist "code\voice-backend\config.js" (echo + Config file exists) else (echo - Config file missing)

echo.

:: Check dependencies
echo [5] Dependencies Check:
if exist "code\voice-backend\node_modules" (echo + Backend dependencies installed) else (echo - Backend dependencies not installed)
if exist "code\voice-frontend\node_modules" (echo + Frontend dependencies installed) else (echo - Frontend dependencies not installed)

echo.

:: Network connectivity test
echo [6] Network Connectivity Test:
ping -n 1 dtu.yinled.com >nul 2>&1 && echo + DTU platform connection OK || echo - DTU platform connection failed

echo.

:: HTTP service test
echo [7] HTTP Service Test:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 3 -ErrorAction Stop; Write-Host '+ Frontend HTTP service responding' } catch { Write-Host '- Frontend HTTP service not responding' }" 2>nul

echo.

:: WebSocket service test
echo [8] WebSocket Service Test:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081' -TimeoutSec 3 -ErrorAction Stop; Write-Host '+ WebSocket service responding' } catch { Write-Host '- WebSocket service not responding' }" 2>nul

echo.

:: System resource check
echo [9] System Resource Check:
for /f "tokens=2 delims=:" %%i in ('tasklist /fi "imagename eq node.exe" ^| findstr /c:" K"') do (
    echo + Node.js memory usage: %%i
    goto :found
)
echo - No Node.js process found
:found

echo.

:: Configuration validation
echo [10] Configuration Validation:
if exist "code\voice-backend\config.js" (
    echo + Validating configuration file...
    node -e "try { const config = require('./code/voice-backend/config.js'); console.log('+ Configuration file syntax OK'); console.log('+ WebSocket port:', config.server.websocket.port); } catch(e) { console.log('- Configuration file error:', e.message); }" 2>nul
)

echo.
echo ========================================
echo Status check completed!
echo.
echo Troubleshooting tips:
echo   - Port occupied: Restart system or change port
echo   - Dependencies missing: Run npm install
echo   - Network issues: Check firewall and network connection
echo   - Files missing: Re-download project files
echo ========================================
echo.

pause
