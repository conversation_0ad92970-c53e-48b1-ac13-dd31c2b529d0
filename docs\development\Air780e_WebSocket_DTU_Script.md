# Air780e WebSocket+DTU混合模式脚本技术文档

## 概述

`air780e_websocket_dtu.lua` 是为CH32V307智能语音识别盲文打印系统设计的Air780e 4G模块脚本，实现了WebSocket服务器通信和银尔达DTU平台透传的双重功能。

## 核心特性

### 🔄 双通道通信架构
- **WebSocket通道**: 与voice-backend服务器实时通信，支持语音识别结果接收
- **DTU透传通道**: 与银尔达DTU平台连接，实现设备数据透传
- **串口通信**: 与CH32V307主控芯片进行数据交换

### 📡 智能数据路由
- 串口数据同时转发到WebSocket服务器和DTU平台
- WebSocket接收的语音识别结果转发到CH32V307和DTU平台
- DTU平台接收的数据透传到CH32V307

### 🛡️ 可靠性保障
- 双连接独立重连机制
- 网络状态实时监控
- 心跳保活机制
- 错误自动恢复

## 配置参数

### WebSocket服务器配置
```lua
websocket = {
    server_host = "***********",       -- WebSocket服务器IP
    server_port = 8081,                 -- WebSocket服务器端口
    websocket_path = "/voice-ws",       -- WebSocket路径
    device_id = "ch32v307_dtu_01",      -- 设备ID
    reconnect_interval = 5000,          -- 重连间隔(ms)
    heartbeat_interval = 30000,         -- 心跳间隔(ms)
}
```

### DTU平台配置
```lua
dtu = {
    server_host = "dtu.yinled.com",     -- DTU服务器地址
    server_port = 8888,                 -- DTU服务器端口
    device_id = "869080075169294",      -- DTU设备ID (IMEI)
    device_key = "898604021024C0050919", -- DTU设备密钥 (ICCID)
    protocol = "tcp",                   -- 协议类型
    reconnect_interval = 10000,         -- DTU重连间隔(ms)
    heartbeat_interval = 60000,         -- DTU心跳间隔(ms)
}
```

## 数据流向

### 上行数据流 (CH32V307 → 云端)
```
CH32V307 → UART → Air780e → [WebSocket服务器 + DTU平台]
```

### 下行数据流 (云端 → CH32V307)
```
WebSocket服务器 → Air780e → UART → CH32V307
DTU平台 → Air780e → UART → CH32V307
```

## 支持的消息类型

### WebSocket消息类型

#### 1. 设备注册 (device_register)
```json
{
    "type": "device_register",
    "device_id": "ch32v307_dtu_01",
    "device_info": {
        "device_type": "ch32v307_dtu_hybrid",
        "firmware_version": "1.0.0",
        "capabilities": ["text_processing", "dtu_transparent", "dual_communication"]
    },
    "dtu_info": {
        "dtu_connected": true,
        "dtu_device_id": "869080075169294"
    }
}
```

#### 2. 语音识别结果处理
- 接收 `recognized_text` 字段的语音识别结果
- 转换为JSON格式发送到CH32V307
- 同时通过DTU透传发送

#### 3. DTU文字发送 (send_dtu_text)
```json
{
    "type": "send_dtu_text",
    "text": "要发送的文字内容"
}
```

#### 4. DTU状态查询 (get_dtu_status)
- 返回DTU连接状态和设备信息

#### 5. 设备心跳 (device_heartbeat)
```json
{
    "type": "device_heartbeat",
    "device_id": "ch32v307_dtu_01",
    "status": {
        "websocket_connected": true,
        "dtu_connected": true,
        "signal_strength": -65,
        "uptime": 3600
    }
}
```

### DTU消息格式

#### 1. 设备注册
```
DEVICE_ID:869080075169294,KEY:898604021024C0050919\r\n
```

#### 2. 心跳消息
```
HEARTBEAT:869080075169294:1625097600\r\n
```

#### 3. 数据透传
- 直接转发JSON格式的串口数据

## 错误处理机制

### 连接失败处理
- WebSocket连接失败：5秒后自动重连
- DTU连接失败：10秒后自动重连
- 网络断开：15秒检测一次，自动重连

### 数据处理错误
- JSON解析失败：记录错误日志，丢弃数据
- 串口缓冲区溢出：自动清空缓冲区
- 发送失败：记录警告日志

## 部署说明

### 1. 硬件要求
- Air780e 4G模块
- 有效的4G SIM卡
- 与CH32V307的UART连接

### 2. 网络要求
- 4G网络连接
- 能够访问WebSocket服务器和DTU平台

### 3. 配置步骤
1. 修改脚本中的服务器IP地址和端口
2. 确认DTU设备ID和密钥正确
3. 上传脚本到Air780e模块
4. 重启模块开始运行

## 监控和调试

### 日志输出
脚本提供详细的日志输出，包括：
- 连接状态变化
- 数据收发记录
- 错误信息
- 心跳状态

### 状态指示
- `ws_status`: WebSocket连接状态
- `dtu_status`: DTU连接状态
- `signal_strength`: 4G信号强度
- `uptime`: 系统运行时间

## 性能特性

### 资源使用
- 内存使用：约50KB
- CPU使用：低负载运行
- 网络流量：心跳+数据传输

### 响应时间
- WebSocket消息响应：<100ms
- DTU透传延迟：<200ms
- 重连时间：5-10秒

## 兼容性

### 支持的Air780e固件版本
- LuatOS 2.0+
- 支持WebSocket和Socket库

### 与现有系统集成
- 完全兼容现有CH32V307固件
- 支持现有的JSON消息格式
- 向后兼容原有WebSocket功能

## 故障排除

### 常见问题
1. **WebSocket连接失败**: 检查服务器IP和端口
2. **DTU连接失败**: 确认设备ID和密钥正确
3. **数据不透传**: 检查串口配置和JSON格式
4. **频繁重连**: 检查网络稳定性和信号强度

### 调试方法
1. 查看日志输出确定问题位置
2. 使用网络工具测试连接性
3. 验证JSON消息格式正确性
4. 检查设备注册信息

## 更新历史

### v1.0.0 (2025-07-04)
- 初始版本发布
- 实现WebSocket+DTU双通道通信
- 支持语音识别结果处理和DTU透传
- 完整的错误处理和重连机制
