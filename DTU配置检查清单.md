# 银尔达DTU功能重新启用检查清单

## ✅ 已完成项目

### 1. DTU平台地址配置
- [x] 平台地址：https://dtu.yinerda.com/
- [x] 平台IP：**************
- [x] 连接测试：✅ 成功

### 2. 设备认证信息配置
- [x] IMEI：869080075169294
- [x] ICCID：898604021024C0050919
- [x] 客户标识：18257342951
- [x] 设备型号：M100P

### 3. 系统配置更新
- [x] 配置文件更新：code/voice-backend/config.js
- [x] DTU功能启用：development环境已启用
- [x] API端点配置：https://dtu.yinerda.com
- [x] 端口配置：443 (HTTPS)

## ⏳ 待完成项目

### 4. 银尔达DTU平台操作
- [ ] **注册DTU管理平台账号**
  - 访问：https://dtu.yinerda.com/
  - 使用手机号或邮箱注册

- [ ] **添加设备到平台**
  - 进入"设备列表"
  - 点击"添加设备"
  - 输入IMEI：869080075169294
  - 选择设备型号：M100P
  - 填写客户信息：18257342951

- [ ] **创建设备分组**
  - 进入"分组管理"
  - 创建新分组：CH32V307_盲文打印机
  - 选择正确的设备型号
  - 将设备分配到分组

- [ ] **配置设备参数**
  - 通信协议：TCP透传
  - 服务器地址：您的服务器IP
  - 服务器端口：8081
  - 心跳间隔：30秒
  - 数据格式：JSON

### 5. 连接验证
- [ ] **重新运行连接测试**
  ```bash
  cd code/voice-backend
  node test-dtu-connection.js
  ```

- [ ] **验证设备状态**
  - 平台连接：应显示 ✅ 成功
  - 设备认证：应显示 ✅ 成功
  - 参数获取：应显示 ✅ 成功

## 🔧 故障排除指南

### 如果设备认证仍然失败：

1. **检查IMEI是否正确**
   - 确认IMEI：869080075169294
   - 在平台上核对设备信息

2. **检查设备型号匹配**
   - 确认选择的设备型号与实际设备匹配
   - M100P或其他银尔达型号

3. **检查分组配置**
   - 确认设备已正确分配到分组
   - 分组的设备型号与设备匹配

4. **联系银尔达技术支持**
   - 电话：0755-23732189
   - 说明设备需要授权或激活

### 如果参数获取失败：

1. **检查分组参数配置**
   - 确认分组中已设置正确的通信参数
   - 检查服务器地址和端口配置

2. **检查网络连接**
   - 确认设备可以访问互联网
   - 检查防火墙设置

## 📞 技术支持联系方式

- **银尔达官方技术支持**：0755-23732189
- **官方网站**：https://www.yinerda.com/
- **DTU管理平台**：https://dtu.yinerda.com/

## 🎯 预期结果

完成所有配置后，您应该看到：

```
🌐 平台连接: ✅ 成功
🔐 设备认证: ✅ 成功  
⚙️ 参数获取: ✅ 成功
🎯 总体状态: ✅ 全部成功
```

系统将支持：
- ✅ 4G DTU通信
- ✅ WebSocket实时数据传输
- ✅ 语音识别指令转发
- ✅ 设备状态监控
- ✅ 远程参数配置

## 📝 重要提醒

1. **SIM卡流量**：您的SIM卡有30M/月流量，请注意使用量
2. **设备授权**：如果设备提示"不存在"，需要联系销售进行生产授权
3. **参数更新**：设备参数更新需要重启设备才能生效
4. **定向卡设置**：如果使用定向SIM卡，需要将dtu.yinerda.com添加到白名单
