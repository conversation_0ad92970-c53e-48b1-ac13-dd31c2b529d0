/**
 * 打印错误处理和恢复机制
 * 基于现有错误处理框架，实现多层次的打印错误检测、处理和自动恢复
 * 
 * 功能特性:
 * - 多层次错误检测 (硬件层、通信层、应用层)
 * - 智能错误恢复策略 (重试、重置、降级)
 * - 错误日志记录和分析
 * - 错误通知和告警机制
 * - 用户界面错误处理支持
 * 
 * <AUTHOR> (Engineer)
 * @version 1.0.0
 * @date 2025-07-04
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

// 错误类型定义
const ERROR_TYPES = {
    // 硬件层错误 (1000-1999)
    HARDWARE: {
        STEPPER_MOTOR_FAULT: { code: 1001, level: 'error', description: '步进电机故障' },
        SENSOR_MALFUNCTION: { code: 1002, level: 'error', description: '传感器故障' },
        POWER_SUPPLY_ISSUE: { code: 1003, level: 'error', description: '电源供应异常' },
        MECHANICAL_JAM: { code: 1004, level: 'error', description: '机械卡纸' },
        PRINT_HEAD_ERROR: { code: 1005, level: 'error', description: '打印头错误' }
    },

    // 通信层错误 (2000-2999)
    COMMUNICATION: {
        DTU_CONNECTION_LOST: { code: 2001, level: 'warning', description: 'DTU连接丢失' },
        WEBSOCKET_DISCONNECTED: { code: 2002, level: 'warning', description: 'WebSocket连接断开' },
        UART_COMMUNICATION_FAILED: { code: 2003, level: 'error', description: 'UART通信失败' },
        DATA_TRANSMISSION_ERROR: { code: 2004, level: 'error', description: '数据传输错误' },
        TIMEOUT_ERROR: { code: 2005, level: 'warning', description: '通信超时' }
    },

    // 应用层错误 (3000-3999)
    APPLICATION: {
        INVALID_PRINT_DATA: { code: 3001, level: 'error', description: '无效打印数据' },
        QUEUE_OVERFLOW: { code: 3002, level: 'warning', description: '打印队列溢出' },
        TASK_TIMEOUT: { code: 3003, level: 'warning', description: '任务执行超时' },
        CONFIGURATION_ERROR: { code: 3004, level: 'error', description: '配置错误' },
        RESOURCE_EXHAUSTED: { code: 3005, level: 'error', description: '资源耗尽' }
    },

    // 系统层错误 (4000-4999)
    SYSTEM: {
        MEMORY_INSUFFICIENT: { code: 4001, level: 'error', description: '内存不足' },
        FILE_SYSTEM_ERROR: { code: 4002, level: 'error', description: '文件系统错误' },
        PERMISSION_DENIED: { code: 4003, level: 'error', description: '权限不足' },
        SERVICE_UNAVAILABLE: { code: 4004, level: 'error', description: '服务不可用' }
    }
};

// 恢复策略定义
const RECOVERY_STRATEGIES = {
    RETRY: 'retry',           // 重试策略
    RESET: 'reset',           // 重置策略
    FALLBACK: 'fallback',     // 降级策略
    MANUAL: 'manual',         // 手动干预
    IGNORE: 'ignore'          // 忽略错误
};

class PrintErrorHandler extends EventEmitter {
    constructor(options = {}) {
        super();

        this.config = {
            maxRetryAttempts: options.maxRetryAttempts || 3,
            retryDelay: options.retryDelay || 1000,
            logPath: options.logPath || './data/error-logs',
            enableAutoRecovery: options.enableAutoRecovery !== false,
            alertThresholds: {
                errorRate: options.alertThresholds?.errorRate || 10, // 10% 错误率告警
                consecutiveErrors: options.alertThresholds?.consecutiveErrors || 5
            },
            ...options
        };

        // 错误统计
        this.errorStats = {
            totalErrors: 0,
            errorsByType: {},
            errorsByLevel: { info: 0, warning: 0, error: 0 },
            consecutiveErrors: 0,
            lastErrorTime: null,
            recoveryAttempts: 0,
            successfulRecoveries: 0
        };

        // 错误历史记录
        this.errorHistory = [];
        this.maxHistorySize = 1000;

        // 恢复策略映射
        this.recoveryStrategies = new Map();
        this.initializeRecoveryStrategies();

        // 初始化错误日志目录
        this.initializeErrorLogging();

        console.log('🛡️ 打印错误处理系统已初始化');
    }

    /**
     * 初始化恢复策略映射
     */
    initializeRecoveryStrategies() {
        // 硬件错误恢复策略
        this.recoveryStrategies.set(1001, { strategy: RECOVERY_STRATEGIES.RESET, maxAttempts: 2 });
        this.recoveryStrategies.set(1002, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 3 });
        this.recoveryStrategies.set(1003, { strategy: RECOVERY_STRATEGIES.MANUAL, maxAttempts: 0 });
        this.recoveryStrategies.set(1004, { strategy: RECOVERY_STRATEGIES.MANUAL, maxAttempts: 0 });
        this.recoveryStrategies.set(1005, { strategy: RECOVERY_STRATEGIES.RESET, maxAttempts: 1 });

        // 通信错误恢复策略
        this.recoveryStrategies.set(2001, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 5 });
        this.recoveryStrategies.set(2002, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 3 });
        this.recoveryStrategies.set(2003, { strategy: RECOVERY_STRATEGIES.RESET, maxAttempts: 2 });
        this.recoveryStrategies.set(2004, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 3 });
        this.recoveryStrategies.set(2005, { strategy: RECOVERY_STRATEGIES.FALLBACK, maxAttempts: 1 });

        // 应用错误恢复策略
        this.recoveryStrategies.set(3001, { strategy: RECOVERY_STRATEGIES.IGNORE, maxAttempts: 0 });
        this.recoveryStrategies.set(3002, { strategy: RECOVERY_STRATEGIES.FALLBACK, maxAttempts: 1 });
        this.recoveryStrategies.set(3003, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 2 });
        this.recoveryStrategies.set(3004, { strategy: RECOVERY_STRATEGIES.MANUAL, maxAttempts: 0 });
        this.recoveryStrategies.set(3005, { strategy: RECOVERY_STRATEGIES.FALLBACK, maxAttempts: 1 });

        // 系统错误恢复策略
        this.recoveryStrategies.set(4001, { strategy: RECOVERY_STRATEGIES.FALLBACK, maxAttempts: 1 });
        this.recoveryStrategies.set(4002, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 2 });
        this.recoveryStrategies.set(4003, { strategy: RECOVERY_STRATEGIES.MANUAL, maxAttempts: 0 });
        this.recoveryStrategies.set(4004, { strategy: RECOVERY_STRATEGIES.RETRY, maxAttempts: 3 });
    }

    /**
     * 初始化错误日志系统
     */
    async initializeErrorLogging() {
        try {
            await fs.mkdir(this.config.logPath, { recursive: true });
            console.log(`📁 错误日志目录已创建: ${this.config.logPath}`);
        } catch (error) {
            console.error('❌ 创建错误日志目录失败:', error.message);
        }
    }

    /**
     * 处理错误
     * @param {Object} errorInfo - 错误信息
     * @param {Object} context - 错误上下文
     */
    async handleError(errorInfo, context = {}) {
        try {
            // 标准化错误信息
            const standardizedError = this.standardizeError(errorInfo, context);

            // 记录错误
            await this.logError(standardizedError);

            // 更新统计信息
            this.updateErrorStats(standardizedError);

            // 检查告警条件
            this.checkAlertConditions(standardizedError);

            // 执行恢复策略
            if (this.config.enableAutoRecovery) {
                await this.executeRecoveryStrategy(standardizedError);
            }

            // 发送错误事件
            this.emit('error', standardizedError);

            return standardizedError;

        } catch (handlingError) {
            console.error('❌ 错误处理过程中发生异常:', handlingError.message);
            this.emit('handlingError', handlingError);
        }
    }

    /**
     * 标准化错误信息
     */
    standardizeError(errorInfo, context) {
        const timestamp = new Date().toISOString();
        const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

        // 查找错误类型定义
        let errorType = null;
        let errorCode = errorInfo.code;

        if (typeof errorInfo === 'string') {
            // 字符串错误，尝试匹配已知错误类型
            errorType = this.findErrorTypeByMessage(errorInfo);
            errorCode = errorType?.code || 9999;
        } else if (errorInfo.code) {
            errorType = this.findErrorTypeByCode(errorInfo.code);
        }

        return {
            id: errorId,
            code: errorCode,
            type: errorType?.description || 'Unknown Error',
            level: errorType?.level || 'error',
            message: errorInfo.message || errorInfo.toString(),
            timestamp: timestamp,
            context: {
                taskId: context.taskId,
                clientId: context.clientId,
                component: context.component || 'unknown',
                operation: context.operation,
                ...context
            },
            recoveryStrategy: this.recoveryStrategies.get(errorCode),
            retryCount: 0,
            resolved: false
        };
    }

    /**
     * 根据错误代码查找错误类型
     */
    findErrorTypeByCode(code) {
        for (const category of Object.values(ERROR_TYPES)) {
            for (const errorType of Object.values(category)) {
                if (errorType.code === code) {
                    return errorType;
                }
            }
        }
        return null;
    }

    /**
     * 根据错误消息查找错误类型
     */
    findErrorTypeByMessage(message) {
        const lowerMessage = message.toLowerCase();

        // 硬件错误匹配
        if (lowerMessage.includes('步进电机') || lowerMessage.includes('motor')) {
            return ERROR_TYPES.HARDWARE.STEPPER_MOTOR_FAULT;
        }
        if (lowerMessage.includes('传感器') || lowerMessage.includes('sensor')) {
            return ERROR_TYPES.HARDWARE.SENSOR_MALFUNCTION;
        }
        if (lowerMessage.includes('卡纸') || lowerMessage.includes('jam')) {
            return ERROR_TYPES.HARDWARE.MECHANICAL_JAM;
        }

        // 通信错误匹配
        if (lowerMessage.includes('dtu') && lowerMessage.includes('连接')) {
            return ERROR_TYPES.COMMUNICATION.DTU_CONNECTION_LOST;
        }
        if (lowerMessage.includes('websocket')) {
            return ERROR_TYPES.COMMUNICATION.WEBSOCKET_DISCONNECTED;
        }
        if (lowerMessage.includes('超时') || lowerMessage.includes('timeout')) {
            return ERROR_TYPES.COMMUNICATION.TIMEOUT_ERROR;
        }

        // 应用错误匹配
        if (lowerMessage.includes('队列') && lowerMessage.includes('满')) {
            return ERROR_TYPES.APPLICATION.QUEUE_OVERFLOW;
        }
        if (lowerMessage.includes('配置') || lowerMessage.includes('config')) {
            return ERROR_TYPES.APPLICATION.CONFIGURATION_ERROR;
        }

        return null;
    }

    /**
     * 记录错误日志
     */
    async logError(errorInfo) {
        try {
            // 添加到内存历史记录
            this.errorHistory.unshift(errorInfo);
            if (this.errorHistory.length > this.maxHistorySize) {
                this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
            }

            // 写入文件日志
            const logDate = new Date().toISOString().split('T')[0];
            const logFile = path.join(this.config.logPath, `error-log-${logDate}.json`);

            const logEntry = {
                ...errorInfo,
                loggedAt: new Date().toISOString()
            };

            // 读取现有日志文件
            let existingLogs = [];
            try {
                const existingData = await fs.readFile(logFile, 'utf8');
                existingLogs = JSON.parse(existingData);
            } catch (readError) {
                // 文件不存在或格式错误，创建新文件
                existingLogs = [];
            }

            // 添加新日志条目
            existingLogs.push(logEntry);

            // 写入文件
            await fs.writeFile(logFile, JSON.stringify(existingLogs, null, 2));

            console.log(`📝 错误已记录: ${errorInfo.id} - ${errorInfo.type}`);

        } catch (logError) {
            console.error('❌ 错误日志记录失败:', logError.message);
        }
    }

    /**
     * 更新错误统计信息
     */
    updateErrorStats(errorInfo) {
        this.errorStats.totalErrors++;
        this.errorStats.lastErrorTime = errorInfo.timestamp;

        // 按类型统计
        const errorTypeKey = `${errorInfo.code}_${errorInfo.type}`;
        this.errorStats.errorsByType[errorTypeKey] = (this.errorStats.errorsByType[errorTypeKey] || 0) + 1;

        // 按级别统计
        this.errorStats.errorsByLevel[errorInfo.level] = (this.errorStats.errorsByLevel[errorInfo.level] || 0) + 1;

        // 连续错误计数
        if (errorInfo.level === 'error') {
            this.errorStats.consecutiveErrors++;
        } else {
            this.errorStats.consecutiveErrors = 0;
        }
    }

    /**
     * 检查告警条件
     */
    checkAlertConditions(errorInfo) {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);

        // 计算最近一小时的错误率
        const recentErrors = this.errorHistory.filter(err =>
            new Date(err.timestamp).getTime() > oneHourAgo
        );

        const errorRate = (recentErrors.length / Math.max(this.errorStats.totalErrors, 1)) * 100;

        // 错误率告警
        if (errorRate > this.config.alertThresholds.errorRate) {
            this.emit('alert', {
                type: 'high_error_rate',
                level: 'warning',
                message: `错误率过高: ${errorRate.toFixed(1)}%`,
                threshold: this.config.alertThresholds.errorRate,
                currentValue: errorRate,
                timestamp: new Date().toISOString()
            });
        }

        // 连续错误告警
        if (this.errorStats.consecutiveErrors >= this.config.alertThresholds.consecutiveErrors) {
            this.emit('alert', {
                type: 'consecutive_errors',
                level: 'error',
                message: `连续错误次数过多: ${this.errorStats.consecutiveErrors}`,
                threshold: this.config.alertThresholds.consecutiveErrors,
                currentValue: this.errorStats.consecutiveErrors,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 执行恢复策略
     */
    async executeRecoveryStrategy(errorInfo) {
        const strategy = errorInfo.recoveryStrategy;
        if (!strategy) {
            console.log(`⚠️ 错误 ${errorInfo.id} 没有定义恢复策略`);
            return false;
        }

        this.errorStats.recoveryAttempts++;

        try {
            console.log(`🔧 执行恢复策略: ${strategy.strategy} (错误: ${errorInfo.id})`);

            let recoveryResult = false;

            switch (strategy.strategy) {
                case RECOVERY_STRATEGIES.RETRY:
                    recoveryResult = await this.executeRetryStrategy(errorInfo, strategy);
                    break;

                case RECOVERY_STRATEGIES.RESET:
                    recoveryResult = await this.executeResetStrategy(errorInfo, strategy);
                    break;

                case RECOVERY_STRATEGIES.FALLBACK:
                    recoveryResult = await this.executeFallbackStrategy(errorInfo, strategy);
                    break;

                case RECOVERY_STRATEGIES.MANUAL:
                    recoveryResult = await this.executeManualStrategy(errorInfo, strategy);
                    break;

                case RECOVERY_STRATEGIES.IGNORE:
                    recoveryResult = true;
                    console.log(`ℹ️ 错误 ${errorInfo.id} 已忽略`);
                    break;

                default:
                    console.log(`⚠️ 未知恢复策略: ${strategy.strategy}`);
                    break;
            }

            if (recoveryResult) {
                this.errorStats.successfulRecoveries++;
                errorInfo.resolved = true;
                console.log(`✅ 错误 ${errorInfo.id} 恢复成功`);
                this.emit('recovery', { errorInfo, strategy: strategy.strategy, success: true });
            } else {
                console.log(`❌ 错误 ${errorInfo.id} 恢复失败`);
                this.emit('recovery', { errorInfo, strategy: strategy.strategy, success: false });
            }

            return recoveryResult;

        } catch (recoveryError) {
            console.error(`❌ 恢复策略执行异常:`, recoveryError.message);
            this.emit('recovery', { errorInfo, strategy: strategy.strategy, success: false, error: recoveryError });
            return false;
        }
    }

    /**
     * 执行重试策略
     */
    async executeRetryStrategy(errorInfo, strategy) {
        if (errorInfo.retryCount >= strategy.maxAttempts) {
            console.log(`⚠️ 错误 ${errorInfo.id} 已达到最大重试次数`);
            return false;
        }

        errorInfo.retryCount++;

        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * errorInfo.retryCount));

        // 发送重试事件，让相关组件处理重试逻辑
        this.emit('retry', {
            errorInfo,
            retryCount: errorInfo.retryCount,
            maxAttempts: strategy.maxAttempts
        });

        return true;
    }

    /**
     * 执行重置策略
     */
    async executeResetStrategy(errorInfo, strategy) {
        console.log(`🔄 执行重置策略: ${errorInfo.id}`);

        // 发送重置事件
        this.emit('reset', {
            errorInfo,
            component: errorInfo.context.component,
            resetType: 'soft_reset'
        });

        return true;
    }

    /**
     * 执行降级策略
     */
    async executeFallbackStrategy(errorInfo, strategy) {
        console.log(`⬇️ 执行降级策略: ${errorInfo.id}`);

        // 发送降级事件
        this.emit('fallback', {
            errorInfo,
            fallbackMode: 'safe_mode',
            originalOperation: errorInfo.context.operation
        });

        return true;
    }

    /**
     * 执行手动干预策略
     */
    async executeManualStrategy(errorInfo, strategy) {
        console.log(`👤 需要手动干预: ${errorInfo.id}`);

        // 发送手动干预事件
        this.emit('manualIntervention', {
            errorInfo,
            urgency: errorInfo.level === 'error' ? 'high' : 'medium',
            instructions: this.getManualInterventionInstructions(errorInfo)
        });

        return false; // 手动干预不算自动恢复成功
    }

    /**
     * 获取手动干预指导
     */
    getManualInterventionInstructions(errorInfo) {
        const instructions = {
            1003: '请检查电源连接和电压是否正常',
            1004: '请检查打印机是否有卡纸，清理纸张通道',
            3004: '请检查配置文件是否正确，重新加载配置',
            4003: '请检查文件系统权限，确保应用有足够的访问权限'
        };

        return instructions[errorInfo.code] || '请联系技术支持人员进行故障排除';
    }

    /**
     * 获取错误统计信息
     */
    getErrorStats() {
        return {
            ...this.errorStats,
            recoverySuccessRate: this.errorStats.recoveryAttempts > 0
                ? (this.errorStats.successfulRecoveries / this.errorStats.recoveryAttempts * 100).toFixed(1)
                : 0
        };
    }

    /**
     * 获取错误历史记录
     */
    getErrorHistory(limit = 50, level = null) {
        let history = this.errorHistory;

        if (level) {
            history = history.filter(error => error.level === level);
        }

        return history.slice(0, limit);
    }

    /**
     * 获取错误分析报告
     */
    getErrorAnalysisReport(timeRange = '24h') {
        const now = Date.now();
        let timeLimit;

        switch (timeRange) {
            case '1h':
                timeLimit = now - (60 * 60 * 1000);
                break;
            case '24h':
                timeLimit = now - (24 * 60 * 60 * 1000);
                break;
            case '7d':
                timeLimit = now - (7 * 24 * 60 * 60 * 1000);
                break;
            default:
                timeLimit = now - (24 * 60 * 60 * 1000);
        }

        const recentErrors = this.errorHistory.filter(error =>
            new Date(error.timestamp).getTime() > timeLimit
        );

        // 错误类型分布
        const errorTypeDistribution = {};
        const errorLevelDistribution = { info: 0, warning: 0, error: 0 };
        const componentErrors = {};

        recentErrors.forEach(error => {
            // 类型分布
            const typeKey = `${error.code}_${error.type}`;
            errorTypeDistribution[typeKey] = (errorTypeDistribution[typeKey] || 0) + 1;

            // 级别分布
            errorLevelDistribution[error.level]++;

            // 组件错误分布
            const component = error.context.component || 'unknown';
            componentErrors[component] = (componentErrors[component] || 0) + 1;
        });

        // 错误趋势分析
        const hourlyTrend = this.calculateHourlyErrorTrend(recentErrors);

        return {
            timeRange,
            totalErrors: recentErrors.length,
            errorTypeDistribution,
            errorLevelDistribution,
            componentErrors,
            hourlyTrend,
            topErrors: this.getTopErrors(recentErrors, 5),
            recommendations: this.generateRecommendations(recentErrors)
        };
    }

    /**
     * 计算每小时错误趋势
     */
    calculateHourlyErrorTrend(errors) {
        const hourlyCount = {};
        const now = new Date();

        // 初始化24小时的数据
        for (let i = 23; i >= 0; i--) {
            const hour = new Date(now.getTime() - i * 60 * 60 * 1000);
            const hourKey = hour.getHours().toString().padStart(2, '0');
            hourlyCount[hourKey] = 0;
        }

        // 统计每小时的错误数量
        errors.forEach(error => {
            const errorTime = new Date(error.timestamp);
            const hourKey = errorTime.getHours().toString().padStart(2, '0');
            if (hourlyCount.hasOwnProperty(hourKey)) {
                hourlyCount[hourKey]++;
            }
        });

        return hourlyCount;
    }

    /**
     * 获取最频繁的错误
     */
    getTopErrors(errors, limit = 5) {
        const errorCount = {};

        errors.forEach(error => {
            const key = `${error.code}_${error.type}`;
            if (!errorCount[key]) {
                errorCount[key] = {
                    code: error.code,
                    type: error.type,
                    level: error.level,
                    count: 0,
                    lastOccurrence: error.timestamp
                };
            }
            errorCount[key].count++;
            if (new Date(error.timestamp) > new Date(errorCount[key].lastOccurrence)) {
                errorCount[key].lastOccurrence = error.timestamp;
            }
        });

        return Object.values(errorCount)
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    }

    /**
     * 生成优化建议
     */
    generateRecommendations(errors) {
        const recommendations = [];
        const errorTypes = {};

        errors.forEach(error => {
            errorTypes[error.code] = (errorTypes[error.code] || 0) + 1;
        });

        // 基于错误类型生成建议
        Object.entries(errorTypes).forEach(([code, count]) => {
            const errorCode = parseInt(code);

            if (count >= 5) {
                switch (Math.floor(errorCode / 1000)) {
                    case 1: // 硬件错误
                        recommendations.push({
                            type: 'hardware',
                            priority: 'high',
                            message: '硬件错误频发，建议进行设备维护检查',
                            action: '联系硬件维护人员进行设备检查'
                        });
                        break;

                    case 2: // 通信错误
                        recommendations.push({
                            type: 'communication',
                            priority: 'medium',
                            message: '通信错误较多，建议检查网络连接稳定性',
                            action: '优化网络配置，增加重连机制'
                        });
                        break;

                    case 3: // 应用错误
                        recommendations.push({
                            type: 'application',
                            priority: 'medium',
                            message: '应用层错误频发，建议检查代码逻辑',
                            action: '审查相关代码，优化错误处理逻辑'
                        });
                        break;

                    case 4: // 系统错误
                        recommendations.push({
                            type: 'system',
                            priority: 'high',
                            message: '系统级错误较多，建议检查系统资源',
                            action: '监控系统资源使用情况，优化资源分配'
                        });
                        break;
                }
            }
        });

        return recommendations;
    }

    /**
     * 清理过期错误日志
     */
    async cleanupExpiredLogs(retentionDays = 30) {
        try {
            const logDir = this.config.logPath;
            const files = await fs.readdir(logDir);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

            let cleanedCount = 0;

            for (const file of files) {
                if (file.startsWith('error-log-') && file.endsWith('.json')) {
                    const dateStr = file.replace('error-log-', '').replace('.json', '');
                    const fileDate = new Date(dateStr);

                    if (fileDate < cutoffDate) {
                        await fs.unlink(path.join(logDir, file));
                        cleanedCount++;
                    }
                }
            }

            console.log(`🧹 已清理 ${cleanedCount} 个过期错误日志文件`);
            return cleanedCount;

        } catch (error) {
            console.error('❌ 清理错误日志失败:', error.message);
            return 0;
        }
    }

    /**
     * 手动标记错误为已解决
     */
    markErrorResolved(errorId, resolution = '') {
        const error = this.errorHistory.find(err => err.id === errorId);
        if (error) {
            error.resolved = true;
            error.resolution = resolution;
            error.resolvedAt = new Date().toISOString();

            console.log(`✅ 错误 ${errorId} 已手动标记为已解决`);
            this.emit('errorResolved', { errorId, resolution });
            return true;
        }

        console.log(`⚠️ 未找到错误 ${errorId}`);
        return false;
    }

    /**
     * 重置错误统计
     */
    resetErrorStats() {
        this.errorStats = {
            totalErrors: 0,
            errorsByType: {},
            errorsByLevel: { info: 0, warning: 0, error: 0 },
            consecutiveErrors: 0,
            lastErrorTime: null,
            recoveryAttempts: 0,
            successfulRecoveries: 0
        };

        console.log('📊 错误统计已重置');
        this.emit('statsReset');
    }
}

module.exports = PrintErrorHandler;
