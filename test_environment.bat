@echo off
title Environment Test
color 0E

echo ========================================
echo   Environment Diagnostic Test
echo ========================================
echo.

echo [Test 1] Checking current directory...
echo Current directory: %CD%
echo.

echo [Test 2] Checking Node.js installation...
where node
if errorlevel 1 (
    echo ERROR: Node.js not found in PATH
    echo Please install Node.js from: https://nodejs.org/
) else (
    echo Node.js found, checking version...
    node --version
)
echo.

echo [Test 3] Checking npm installation...
where npm
if errorlevel 1 (
    echo ERROR: npm not found in PATH
) else (
    echo npm found, checking version...
    npm --version
)
echo.

echo [Test 4] Checking project structure...
if exist "code\voice-backend\server.js" (
    echo OK: Backend server file exists
) else (
    echo ERROR: Backend server file missing
)

if exist "code\voice-frontend\server.js" (
    echo OK: Frontend server file exists
) else (
    echo ERROR: Frontend server file missing
)

if exist "code\voice-backend\package.json" (
    echo OK: Backend package.json exists
) else (
    echo ERROR: Backend package.json missing
)

if exist "code\voice-frontend\package.json" (
    echo OK: Frontend package.json exists
) else (
    echo ERROR: Frontend package.json missing
)
echo.

echo [Test 5] Checking port availability...
netstat -an | findstr :4000 >nul
if errorlevel 1 (
    echo OK: Port 4000 is available
) else (
    echo WARNING: Port 4000 is in use
)

netstat -an | findstr :8081 >nul
if errorlevel 1 (
    echo OK: Port 8081 is available
) else (
    echo WARNING: Port 8081 is in use
)
echo.

echo [Test 6] Testing Node.js execution...
echo Testing basic Node.js command...
node -e "console.log('Node.js is working properly')"
if errorlevel 1 (
    echo ERROR: Node.js execution failed
) else (
    echo OK: Node.js execution successful
)
echo.

echo ========================================
echo   Diagnostic Complete
echo ========================================
echo.
echo If all tests pass, try running: start_simple.bat
echo If tests fail, please fix the issues above.
echo.
pause
