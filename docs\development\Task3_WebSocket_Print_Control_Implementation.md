# Task 3: WebSocket服务器打印指令处理扩展 - 实现文档

## 任务概述
**任务ID**: Task 3  
**任务名称**: 扩展WebSocket服务器打印指令处理  
**完成状态**: ✅ 已完成  
**完成时间**: 2025-01-04  
**负责人**: <PERSON> (Engineer)

## 实现目标
扩展WebSocket服务器的消息处理能力，增加完整的打印控制指令处理系统，包括任务队列管理、状态监控、配置更新等功能。

## 核心功能实现

### 1. 消息路由扩展
在主WebSocket消息处理器中添加了4个新的消息类型：

```javascript
// 新增消息类型处理
} else if (data.type === 'print_command') {
    await handlePrintCommand(ws, data, clientId);
} else if (data.type === 'print_status_request') {
    await handlePrintStatusRequest(ws, data, clientId);
} else if (data.type === 'print_queue_management') {
    await handlePrintQueueManagement(ws, data, clientId);
} else if (data.type === 'print_config_update') {
    await handlePrintConfigUpdate(ws, data, clientId);
```

### 2. PrintTaskQueue类实现
创建了完整的打印任务队列管理系统：

**核心特性**:
- 优先级队列支持（high/normal）
- 可配置队列容量限制
- 任务超时管理
- 统计信息跟踪
- 任务生命周期管理

**关键方法**:
- `addTask(task)` - 添加任务到队列
- `getStatus()` - 获取队列状态统计
- `cancelTask(taskId)` - 取消指定任务
- `clearQueue()` - 清空队列

### 3. 打印命令处理器

#### 3.1 handlePrintCommand
主要的打印命令分发器，支持以下命令：
- `start_print` - 开始打印
- `pause_print` - 暂停打印
- `resume_print` - 恢复打印
- `cancel_print` - 取消打印
- `emergency_stop` - 紧急停止

#### 3.2 handleStartPrint
打印任务启动处理：
- 创建任务对象并加入队列
- 集成DTU通信发送打印指令
- 错误处理和状态广播
- 支持DTU离线时的队列缓存

#### 3.3 handlePausePrint/handleResumePrint/handleCancelPrint
打印控制命令处理：
- 使用新的DTU `sendPrintControl` 方法
- 实时任务状态更新
- 状态广播到所有客户端

#### 3.4 handleEmergencyStop
紧急停止处理：
- 清空本地打印队列
- 发送DTU紧急停止指令
- 全局状态广播

### 4. 状态查询处理器

#### handlePrintStatusRequest
支持三种状态查询：
- `queue_status` - 队列状态查询
- `device_status` - 设备状态查询（集成DTU状态）
- `task_status` - 特定任务状态查询

### 5. 队列管理处理器

#### handlePrintQueueManagement
支持队列管理操作：
- `get_queue` - 获取队列详情
- `clear_queue` - 清空队列
- `set_priority` - 设置任务优先级

### 6. 配置更新处理器

#### handlePrintConfigUpdate
支持动态配置更新：
- `speed_level` - 速度等级配置
- `get_config` - 获取当前配置
- DTU配置同步功能

### 7. 工具函数

#### broadcastToAllClients & broadcastPrintStatus
- 全局消息广播功能
- 打印状态实时更新推送

## DTU集成增强

### 新增DTU方法
在 `dtu-integration.js` 中添加了专门的打印相关方法：

1. **sendPrintTask(printData)** - 发送打印任务
2. **requestPrintStatus(taskId)** - 请求打印状态
3. **sendPrintControl(action, taskId, params)** - 发送打印控制命令
4. **updatePrintConfig(configType, configData)** - 更新设备配置

### DTU消息格式标准化
统一了DTU通信的消息格式：
- `type` - 消息类型标识
- `timestamp` - 时间戳
- `source` - 消息来源标识
- 结构化的参数传递

## 错误处理机制

### 多层错误处理
1. **DTU通信错误** - 自动降级到本地队列管理
2. **任务超时处理** - 可配置的任务超时机制
3. **队列容量管理** - 防止内存溢出的队列限制
4. **状态一致性** - 确保WebSocket和DTU状态同步

### 错误恢复策略
- DTU离线时的优雅降级
- 任务重试机制
- 状态自动同步

## 性能优化

### 消息处理优化
- 异步消息处理避免阻塞
- 批量状态更新减少网络开销
- 智能广播避免重复消息

### 内存管理
- 队列容量限制
- 任务对象生命周期管理
- 统计信息定期清理

## 配置集成

### 与configManager集成
- 动态配置热更新
- 配置变更的DTU同步
- 配置验证和错误处理

### 可配置参数
- 队列最大容量
- 任务超时时间
- 重试次数和间隔
- 统计信息保留期

## 测试验证

### 功能测试覆盖
- ✅ 消息路由正确性
- ✅ 任务队列管理
- ✅ DTU通信集成
- ✅ 错误处理机制
- ✅ 状态广播功能

### 性能测试
- ✅ 并发消息处理
- ✅ 队列容量压力测试
- ✅ 内存使用监控

## 文件变更记录

### 主要修改文件
1. **code/voice-backend/server.js** (692→1500+ 行)
   - 新增打印消息路由
   - 实现PrintTaskQueue类
   - 添加8个打印处理函数
   - 集成DTU打印方法

2. **code/voice-backend/dtu-integration.js** (293→370+ 行)
   - 新增4个打印专用方法
   - 标准化DTU消息格式
   - 增强错误处理

## 下一步计划

Task 3已完成，为后续任务奠定了坚实基础：
- Task 4: 增强Air780e脚本打印数据处理
- Task 5: 优化前端打印控制界面
- Task 6: 实现打印性能监控和统计系统

## 技术债务记录

### 已解决
- ✅ 消息处理器重复定义问题
- ✅ DTU方法命名标准化
- ✅ 错误处理一致性

### 待优化
- 任务持久化存储（服务器重启恢复）
- 更细粒度的权限控制
- 打印队列的优先级算法优化

---
**文档版本**: v1.0  
**最后更新**: 2025-01-04  
**维护者**: Alex (Engineer)
